from __future__ import annotations

import asyncio
import json
import time
from typing import Any, As<PERSON><PERSON><PERSON><PERSON>, Dict, Iterator, Optional

from langchain_core.runnables import Runnable
from pydantic import BaseModel, Field
from loguru import logger

from .agent_ import agent_

# ──────────────────────────────────────────────────────────────
# Pydantic 输入 / 输出模型
# ──────────────────────────────────────────────────────────────
class AgentInput(BaseModel):
    """HTTP / LangServe 输入模型"""

    question: str = Field(..., description="用户问题")
    context: Optional[Dict[str, Any]] = Field(
        default=None, description="可选上下文(当前暂未使用)"
    )
    session_id: Optional[str] = Field(default=None, description="会话ID")


class AgentOutput(BaseModel):
    """输出模型，与旧版本保持兼容"""

    answer: str = Field(..., description="回答内容")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")


# ──────────────────────────────────────────────────────────────
# Runnable 实现
# ──────────────────────────────────────────────────────────────
class RunnableAgent(Runnable[AgentInput, AgentOutput]):
    """将 agent_x 封装为 LangChain Runnable，支持 invoke / stream / astream"""

    def __init__(self):
        self.agent = agent_
        self.logger = logger.bind(component="RunnableAgentX")

    # ---- 型别定义 ----
    @property
    def InputType(self) -> type[AgentInput]:  # noqa: D401
        return AgentInput

    @property
    def OutputType(self) -> type[AgentOutput]:  # noqa: D401
        return AgentOutput

    # ---- 工具：解析输入为三元组 ----
    def _parse_input(self, input_data: Any) -> tuple[str, Optional[Dict[str, Any]], Optional[str]]:
        """统一解析 input（AgentInput 或 dict）"""
        if isinstance(input_data, AgentInput):
            return input_data.question, input_data.context, input_data.session_id
        if isinstance(input_data, dict):
            return (
                input_data.get("question", ""),
                input_data.get("context"),
                input_data.get("session_id"),
            )
        raise TypeError(f"Unsupported input type: {type(input_data)}")

    # ---- 同步调用 ----
    def invoke(self, input: AgentInput | Dict[str, Any], config: Optional[Dict[str, Any]] = None, **_) -> AgentOutput:  # type: ignore[override]
        question, context, session_id = self._parse_input(input)
        start = time.time()
        try:
            answer = self.agent.answer(question)
            return AgentOutput(
                answer=answer,
                metadata={
                    "processing_time": time.time() - start,
                    "session_id": session_id,
                    "cache_stats": {},
                },
            )
        except Exception as e:  # pragma: no cover
            self.logger.error(f"invoke error: {e}")
            return AgentOutput(
                answer=f"抱歉，处理您的问题时出现错误: {e}",
                metadata={"error": str(e)},
            )

    # ---- 流式输出（同步） ----
    def stream(self, input: AgentInput | Dict[str, Any], config: Optional[Dict[str, Any]] = None, **_) -> Iterator[str]:  # type: ignore[override]
        question, context, session_id = self._parse_input(input)
        try:
            answer = self.agent.answer(question)
            chunks = self._split_into_chunks(answer)
            total = len(chunks)
            for i, chunk in enumerate(chunks):
                time.sleep(0.05)
                if i == 0:
                    metadata = {"session_id": session_id, "total_chunks": total, "cache_stats": {}}
                    yield f"data: {{\"chunk\": {json.dumps(chunk, ensure_ascii=False)}, \"metadata\": {json.dumps(metadata, ensure_ascii=False)}}}\n\n"

                else:
                    yield f"data: {{\"chunk\": {json.dumps(chunk, ensure_ascii=False)}, \"chunk_index\": {i}}}\n\n"
            yield "data: {\"done\": true}\n\n"
        except Exception as e:  # pragma: no cover
            self.logger.error(f"stream error: {e}")
            yield f"data: {{\"error\": {json.dumps(str(e), ensure_ascii=False)}}}\n"


    # ---- 流式输出（异步） ----
    async def astream(self, input: AgentInput | Dict[str, Any], config: Optional[Dict[str, Any]] = None, **_) -> AsyncIterator[str]:  # type: ignore[override]
        question, context, session_id = self._parse_input(input)
        loop = asyncio.get_event_loop()
        try:
            answer = await loop.run_in_executor(None, self.agent.answer, question)
            chunks = self._split_into_chunks(answer)
            total = len(chunks)
            for i, chunk in enumerate(chunks):
                await asyncio.sleep(0.03)
                if i == 0:
                    metadata = {"session_id": session_id, "total_chunks": total, "cache_stats": {}}
                    yield f"data: {{\"chunk\": {json.dumps(chunk, ensure_ascii=False)}, \"metadata\": {json.dumps(metadata, ensure_ascii=False)}}}\n\n"

                else:
                    yield f"data: {{\"chunk\": {json.dumps(chunk, ensure_ascii=False)}, \"chunk_index\": {i}}}\n\n"
            yield "data: {\"done\": true}\n\n"
        except Exception as e:  # pragma: no cover
            self.logger.error(f"astream error: {e}")
            yield f"data: {{\"error\": {json.dumps(str(e), ensure_ascii=False)}}}\n"


    # ---- 健康检查 ----
    def get_health_status(self) -> Dict[str, Any]:
        return {"status": "healthy", "agent_ready": True, "timestamp": time.time()}

    # ---- 工具方法 ----
    def _split_into_chunks(self, text: str, chunk_size: int = 50) -> list[str]:
        """按句号/逗号将文本分块，最长 chunk_size 字符"""
        import re

        if not text:
            return [""]

        # 先按句子切分
        sentences = re.split(r"[。！？\n]", text)
        sentences = [s.strip() for s in sentences if s.strip()]

        chunks: list[str] = []
        for sentence in sentences:
            if len(sentence) <= chunk_size:
                chunks.append(sentence)
            else:
                parts = re.split(r"[，,\s]", sentence)
                current = ""
                for part in parts:
                    if len(current + part) <= chunk_size:
                        current += part
                    else:
                        if current:
                            chunks.append(current)
                        current = part
                if current:
                    chunks.append(current)

        return chunks if chunks else [text]


# 全局实例供 server.py 导入
runnable_agent = RunnableAgent() 