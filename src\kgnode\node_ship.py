"""
船舶动态节点ETL模块
用于处理船舶月度统计、船舶分货类月度统计和船舶分航线月度统计数据的ETL流程
"""

import pandas as pd
from loguru import logger
from typing import Dict, List, Optional, Set, Any
from datetime import datetime
import os
from concurrent.futures import ThreadPoolExecutor

from .database import DatabaseManager, Neo4jClient, OracleClient


class DataValidator:
    """数据质量验证器"""

    @staticmethod
    def validate_ship_data(df: pd.DataFrame) -> pd.DataFrame:
        """验证船舶数据质量"""
        if df.empty:
            return df

        original_count = len(df)

        # 必填字段检查
        required_fields = ['mmsi', 'name', 'ym']
        for field in required_fields:
            if field in df.columns:
                df = df.dropna(subset=[field])

        # 数据范围检查
        if 'opratio' in df.columns:
            df = df[df['opratio'].between(0, 1) | df['opratio'].isna()]

        # MMSI格式检查（应该是9位数字）
        if 'mmsi' in df.columns:
            df = df[df['mmsi'].astype(str).str.len() == 9]

        logger.info(f"数据验证: {original_count} -> {len(df)} 条记录")
        return df

    @staticmethod
    def validate_cargo_data(df: pd.DataFrame) -> pd.DataFrame:
        """验证货物统计数据质量"""
        if df.empty:
            return df

        original_count = len(df)

        # 必填字段检查
        required_fields = ['mmsi', 'ym', 'cargotype']
        for field in required_fields:
            if field in df.columns:
                df = df.dropna(subset=[field])

        # 货物重量应该大于0
        if 'cargo_ton' in df.columns:
            df = df[df['cargo_ton'] > 0]

        logger.info(f"货物数据验证: {original_count} -> {len(df)} 条记录")
        return df


class ShipDynamicETL:
    """船舶动态节点ETL管理器"""
    
    def __init__(self, database_manager: DatabaseManager = None, batch_size: int = 5000):
        """
        初始化船舶动态ETL管理器
        
        Args:
            database_manager: 数据库管理器实例
            batch_size: 批处理大小
        """
        self.batch_size = batch_size
        self.processed_count = 0
        self.error_count = 0
        
        if not database_manager:
            raise ValueError("必须提供database_manager实例")
        self.db_manager = database_manager

    def close(self):
        """关闭数据库连接"""
        if self.db_manager:
            self.db_manager.close_all()
            logger.info("ShipDynamicETL database connections closed")
    
    
    def _batch_write_ship_month_stats(self, df: pd.DataFrame) -> Dict[str, int]:
        """
        批量写入船舶月度统计数据
        
        Args:
            df: 包含船舶月度统计数据的DataFrame
            
        Returns:
            Dict: 处理结果统计
        """
        created_count = 0
        error_count = 0
        
        # 按batch_size分块处理
        for i in range(0, len(df), self.batch_size):
            chunk = df.iloc[i:i+self.batch_size]
            
            try:
                with self.db_manager.neo4j.get_session() as session:
                    with session.begin_transaction() as tx:
                        # 准备批量数据
                        rows = []
                        for _, row in chunk.iterrows():
                            # 使用MMSI作为主键组成部分，确保唯一性
                            ship_ym_key = f"{row['ym']}_{row['mmsi']}"

                            rows.append({
                                'name': ship_ym_key,
                                'mmsi': str(row['mmsi']) if pd.notna(row['mmsi']) else None,
                                'ym': str(row['ym']),
                                'opRatio': float(row['opratio']) if pd.notna(row['opratio']) else None,
                                'voyages': int(row['voyages']) if pd.notna(row['voyages']) else None,
                                'loadRatio': float(row['loadratio']) if pd.notna(row['loadratio']) else None,
                                'anchorTime_day': float(row['anchortime_day']) if pd.notna(row['anchortime_day']) else None,
                                'sailTime_day': float(row['sailtime_day']) if pd.notna(row['sailtime_day']) else None,
                                'sailRatio': float(row['sailratio']) if pd.notna(row['sailratio']) else None,
                                'turnover_tonkm': float(row['turnover_tonkm']) if pd.notna(row['turnover_tonkm']) else None,
                                'loadVoyages': int(row['loadvoyages']) if pd.notna(row['loadvoyages']) else None,
                                'dispatchLoadRatio': float(row['dispatchloadratio']) if pd.notna(row['dispatchloadratio']) else None,
                                'distanceLoadRatio': float(row['distanceloadratio']) if pd.notna(row['distanceloadratio']) else None,
                                'capacity_ton': float(row['capacity_ton']) if pd.notna(row['capacity_ton']) else None,
                                'loadVoyRatio': float(row['loadvoyratio']) if pd.notna(row['loadvoyratio']) else None,
                                'mileage_km': float(row['mileage_km']) if pd.notna(row['mileage_km']) else None,
                                'tonDayOutput': float(row['tondayoutput']) if pd.notna(row['tondayoutput']) else None,
                                'tonShipOutput': float(row['tonshipoutput']) if pd.notna(row['tonshipoutput']) else None
                            })
                        
                        # 批量写入 - 符合设计文档的属性定义
                        tx.run("""
                            UNWIND $rows AS row
                            MERGE (sms:ShipMonthStat {name: row.name})
                            SET sms.opRatio = row.opRatio,
                                sms.voyages = row.voyages,
                                sms.loadRatio = row.loadRatio,
                                sms.anchorTime_day = row.anchorTime_day,
                                sms.sailTime_day = row.sailTime_day,
                                sms.sailRatio = row.sailRatio,
                                sms.turnover_tonkm = row.turnover_tonkm,
                                sms.loadVoyages = row.loadVoyages,
                                sms.dispatchLoadRatio = row.dispatchLoadRatio,
                                sms.distanceLoadRatio = row.distanceLoadRatio,
                                sms.capacity_ton = row.capacity_ton,
                                sms.loadVoyRatio = row.loadVoyRatio,
                                sms.mileage_km = row.mileage_km,
                                sms.tonDayOutput = row.tonDayOutput,
                                sms.tonShipOutput = row.tonShipOutput,
                                sms.lastUpdated = datetime()
                            WITH sms, row
                            MERGE (s:Ship {mmsi: row.mmsi})
                            MERGE (ym:YearMonth {ym: row.ym})
                            MERGE (sms)-[:STAT_FOR_SHIP]->(s)
                            MERGE (sms)-[:STAT_FOR_MONTH]->(ym)
                        """, rows=rows)
                        
                        tx.commit()
                        created_count += len(chunk)
                        logger.info(f"批量写入船舶月度统计 {len(chunk)} 条记录")
                        
            except Exception as e:
                error_count += len(chunk)
                logger.error(f"批量写入船舶月度统计失败: {e}")
        
        return {"created": created_count, "errors": error_count}
    
    def load_ship_month_stats(self, ship_filter: str = None, time_filter: str = None, limit: int = 10000) -> Dict[str, int]:
        """
        从Oracle数据库加载船舶月度统计数据

        Args:
            ship_filter: 船舶过滤条件，如 "ship_name_cn = '汉海5号'"
            time_filter: 时间过滤条件，如 "ym >= '202401'"
            limit: 记录数限制，默认10000

        Returns:
            Dict: 加载结果统计
        """
        try:
            # 构建动态SQL查询 - 根据设计文档映射源数据字段
            base_sql = """
                SELECT
                    t.mmsi,
                    t.ship_name_cn name,
                    t.ym,
                    t.operate_ratio as opratio,
                    t.voy_num as voyages,
                    t.load_ratio as loadratio,
                    t.sum_anchor_time as anchortime_day,
                    t.sum_sail_time as sailtime_day,
                    t.sail_ratio as sailratio,
                    t.turnover_od as turnover_tonkm,
                    t.load_voy_num as loadvoyages,
                    t.dispatch_load_ratio as dispatchloadratio,
                    t.distance_load_ratio as distanceloadratio,
                    t.capacity_od as capacity_ton,
                    t.load_voy_ratio as loadvoyratio,
                    t.sum_mileage as mileage_km,
                    t.ton_day_output as tondayoutput,
                    t.ton_ship_output as tonshipoutput
                FROM yssjtj.dws_ship_index_i_m t
                WHERE 1=1 
            """

            # 动态添加过滤条件
            if ship_filter:
                base_sql += f" AND {ship_filter}"
            if time_filter:
                base_sql += f" AND {time_filter}"
            else:
                # 默认时间过滤，避免全表扫描
                base_sql += " AND t.ym >= '202401'"

            # 添加记录限制
            base_sql += f" AND ROWNUM <= {limit}"
            
            logger.info(f"从Oracle执行船舶月度统计查询")

            df = self.db_manager.oracle.execute_query_to_dataframe(base_sql)
            df.columns = df.columns.str.lower()

            logger.info(f"从Oracle加载了{len(df)}条船舶月度统计记录")

            if df.empty:
                logger.warning("从Oracle查询到的船舶月度统计数据为空")
                return {"created": 0, "errors": 0}

            # 数据验证
            df = DataValidator.validate_ship_data(df)
            
            
            # 批量写入数据
            result = self._batch_write_ship_month_stats(df)
            
            logger.info(f"船舶月度统计加载完成: 成功 {result['created']}, 失败 {result['errors']}")
            result["success"] = result["errors"] == 0
            result["total_created"] = result["created"]
            result["total_errors"] = result["errors"]
            return result

        except Exception as e:
            logger.error(f"从Oracle加载船舶月度统计失败: {e}")
            return {"created": 0, "errors": 1, "success": False, "total_created": 0, "total_errors": 1}
    
    def _batch_write_ship_month_cargo_stats(self, df: pd.DataFrame) -> Dict[str, int]:
        """
        批量写入船舶月度分货类统计数据
        
        Args:
            df: 包含船舶月度分货类统计数据的DataFrame
            
        Returns:
            Dict: 处理结果统计
        """
        created_count = 0
        error_count = 0
        
        # 按batch_size分块处理
        for i in range(0, len(df), self.batch_size):
            chunk = df.iloc[i:i+self.batch_size]
            
            try:
                with self.db_manager.neo4j.get_session() as session:
                    with session.begin_transaction() as tx:
                        # 准备批量数据
                        rows = []
                        for _, row in chunk.iterrows():
                            # 使用MMSI作为主键组成部分
                            ship_ym_cargo_key = f"{row['cargotype']}_{row['ym']}_{row['mmsi']}"

                            rows.append({
                                'name': ship_ym_cargo_key,
                                'mmsi': str(row['mmsi']) if pd.notna(row['mmsi']) else None,
                                'ym': str(row['ym']),
                                'cargoType': str(row['cargotype']),
                                'cargo_ton': float(row['cargo_ton']) if pd.notna(row['cargo_ton']) else None
                            })
                        
                        # 批量写入
                        tx.run("""
                            UNWIND $rows AS row
                            MERGE (smcs:ShipMonthCargoStat {name: row.name})
                            SET smcs.cargo_ton = row.cargo_ton,
                                smcs.lastUpdated = datetime()
                            WITH smcs, row
                            MERGE (s:Ship {mmsi: row.mmsi})
                            MERGE (ym:YearMonth {ym: row.ym})
                            WITH smcs, s, ym, row
                            OPTIONAL MATCH (ct:CargoType {subName: row.cargoType})
                            MERGE (smcs)-[:CARGO_STAT_FOR_SHIP]->(s)
                            MERGE (smcs)-[:CARGO_STAT_FOR_MONTH]->(ym)
                            FOREACH (cargo IN CASE WHEN ct IS NOT NULL THEN [ct] ELSE [] END |
                                MERGE (smcs)-[:CARGO_STAT_FOR_TYPE]->(cargo)
                            )
                        """, rows=rows)
                        
                        tx.commit()
                        created_count += len(chunk)
                        logger.info(f"批量写入船舶月度分货类统计 {len(chunk)} 条记录")
                        
            except Exception as e:
                error_count += len(chunk)
                logger.error(f"批量写入船舶月度分货类统计失败: {e}")
        
        return {"created": created_count, "errors": error_count}
    
    def load_ship_month_cargo_stats(self, ship_filter: str = None, time_filter: str = None, limit: int = 10000) -> Dict[str, int]:
        """
        从Oracle数据库加载船舶月度分货类统计数据

        Args:
            ship_filter: 船舶过滤条件，如 "ship_id = 'CN20180533386'"
            time_filter: 时间过滤条件，如 "ym >= '202401'"
            limit: 记录数限制，默认10000

        Returns:
            Dict: 加载结果统计
        """
        try:
            # 构建动态SQL查询
            base_sql = """
                SELECT
                    t.ship_id,
                    s.ship_name_cn as name,
                    s.mmsi,
                    t.ym,
                    t.CARGO_NAME as cargotype,
                    sum(t.capacity) as cargo_ton
                FROM yssjtj.od_dwd_statistics t
                JOIN yssjtj.dim_l_ship_basic_data s ON t.ship_id = s.ship_id
                WHERE 1=1
            """

            # 动态添加过滤条件
            if ship_filter:
                base_sql += f" AND {ship_filter}"
            if time_filter:
                base_sql += f" AND {time_filter}"
            else:
                base_sql += " AND t.ym >= '202401'"

            base_sql += " GROUP BY t.ship_id, s.ship_name_cn, s.mmsi, t.ym, t.CARGO_NAME"
            # base_sql += f" AND ROWNUM <= {limit}"
            
            logger.info(f"从Oracle执行船舶月度分货类统计查询")

            df = self.db_manager.oracle.execute_query_to_dataframe(base_sql)
            df.columns = df.columns.str.lower()

            logger.info(f"从Oracle加载了{len(df)}条船舶月度分货类统计记录")

            if df.empty:
                logger.warning("从Oracle查询到的船舶月度分货类统计数据为空")
                return {"created": 0, "errors": 0}

            # 数据验证
            df = DataValidator.validate_cargo_data(df)

            
            # 批量写入数据
            result = self._batch_write_ship_month_cargo_stats(df)
            
            logger.info(f"船舶月度分货类统计加载完成: 成功 {result['created']}, 失败 {result['errors']}")
            result["success"] = result["errors"] == 0
            result["total_created"] = result["created"]
            result["total_errors"] = result["errors"]
            return result

        except Exception as e:
            logger.error(f"从Oracle加载船舶月度分货类统计失败: {e}")
            return {"created": 0, "errors": 1, "success": False, "total_created": 0, "total_errors": 1}
    
    def _batch_write_ship_month_line_stats(self, df: pd.DataFrame) -> Dict[str, int]:
        """
        批量写入船舶月度分航线统计数据
        
        Args:
            df: 包含船舶月度分航线统计数据的DataFrame
            
        Returns:
            Dict: 处理结果统计
        """
        created_count = 0
        error_count = 0
        
        # 按batch_size分块处理
        for i in range(0, len(df), self.batch_size):
            chunk = df.iloc[i:i+self.batch_size]
            
            try:
                with self.db_manager.neo4j.get_session() as session:
                    with session.begin_transaction() as tx:
                        # 准备批量数据
                        rows = []
                        for _, row in chunk.iterrows():
                            # 使用MMSI作为主键组成部分
                            ship_ym_line_key = f"{row['porto']}_{row['portd']}_{row['ym']}_{row['mmsi']}"

                            # 生成航线ID（与 node_route.py 保持一致）
                            origin_port = str(row['porto']).strip()
                            destination_port = str(row['portd']).strip()
                            route_code = f"{origin_port[:2].upper()}-{destination_port[:2].upper()}"
                            route_id = f"{route_code}-{hash(f'{origin_port}-{destination_port}') % 1000:03d}"

                            rows.append({
                                'name': ship_ym_line_key,
                                'mmsi': str(row['mmsi']) if pd.notna(row['mmsi']) else None,
                                'ym': str(row['ym']),
                                'route_id': route_id,
                                'origin_port': origin_port,
                                'destination_port': destination_port,
                                'cargo_ton': float(row['cargo_ton']) if pd.notna(row['cargo_ton']) else None,
                                'turnover_tonkm': float(row['turnover_tonkm']) if pd.notna(row['turnover_tonkm']) else None,
                                'mileage_km': float(row['mileage_km']) if pd.notna(row['mileage_km']) else None,
                                'voyageCount': 1,  # 默认航次数
                                'avgLoadRatio': float(row['cargo_ton']) / 15000.0 if pd.notna(row['cargo_ton']) and row['cargo_ton'] > 0 else 0.0
                            })
                        
                        # 批量写入 - 符合数据库设计文档v4.0的航线维度要求
                        tx.run("""
                            UNWIND $rows AS row
                            MERGE (smls:ShipMonthLineStat {name: row.name})
                            SET smls.cargo_ton = row.cargo_ton,
                                smls.turnover_tonkm = row.turnover_tonkm,
                                smls.mileage_km = row.mileage_km,
                                smls.voyageCount = row.voyageCount,
                                smls.avgLoadRatio = row.avgLoadRatio,
                                smls.lastUpdated = datetime()
                            WITH smls, row

                            // 建立与船舶和时间的关系
                            MERGE (s:Ship {mmsi: row.mmsi})
                            MERGE (ym:YearMonth {ym: row.ym})
                            MERGE (smls)-[:LINE_STAT_FOR_SHIP]->(s)
                            MERGE (smls)-[:LINE_STAT_FOR_MONTH]->(ym)

                            // 连接到已存在的航线节点（航线应该已经由 RouteETL 创建）
                            WITH smls, row
                            OPTIONAL MATCH (sr:ShippingRoute {routeId: row.route_id})
                            FOREACH (route IN CASE WHEN sr IS NOT NULL THEN [sr] ELSE [] END |
                                MERGE (smls)-[:STAT_FOR_ROUTE]->(route)
                            )
                        """, rows=rows)
                        
                        tx.commit()
                        created_count += len(chunk)
                        logger.info(f"批量写入船舶月度分航线统计 {len(chunk)} 条记录")
                        
            except Exception as e:
                error_count += len(chunk)
                logger.error(f"批量写入船舶月度分航线统计失败: {e}")
        
        return {"created": created_count, "errors": error_count}
    
    def load_ship_month_line_stats(self, ship_filter: str = None, time_filter: str = None, limit: int = 10000) -> Dict[str, int]:
        """
        从Oracle数据库加载船舶月度分航线统计数据

        Args:
            ship_filter: 船舶过滤条件，如 "ship_id = 'CN20180533386'"
            time_filter: 时间过滤条件，如 "ym >= '202401'"
            limit: 记录数限制，默认10000

        Returns:
            Dict: 加载结果统计
        """
        try:
            # 构建动态SQL查询
            base_sql = """
                SELECT
                    t.ship_id,
                    s.ship_name_cn as name,
                    s.mmsi,
                    t.ym,
                    t.merge_city_name_o as porto,
                    t.merge_city_name_d as portd,
                    sum(t.capacity) as cargo_ton,
                    sum(t.TURNOVER) as turnover_tonkm,
                    sum(t.MILEAGE) as mileage_km
                FROM yssjtj.od_dwd_statistics t
                JOIN yssjtj.dim_l_ship_basic_data s ON t.ship_id = s.ship_id
                WHERE 1=1
            """

            # 动态添加过滤条件
            if ship_filter:
                base_sql += f" AND {ship_filter}"
            if time_filter:
                base_sql += f" AND {time_filter}"
            else:
                base_sql += " AND t.ym >= '202401'"

            base_sql += " GROUP BY t.ship_id, s.ship_name_cn, s.mmsi, t.ym, t.merge_city_name_o, t.merge_city_name_d"
            # base_sql += f" AND ROWNUM <= {limit}"
            
            logger.info(f"从Oracle执行船舶月度分航线统计查询")
            
            # 使用database_manager中的Oracle客户端读取数据
            df = self.db_manager.oracle.execute_query_to_dataframe(base_sql)
            
            # 将列名转换为小写，以匹配代码中的引用
            df.columns = df.columns.str.lower()
            
            logger.info(f"从Oracle加载了{len(df)}条船舶月度分航线统计记录")
            
            if df.empty:
                logger.warning("从Oracle查询到的船舶月度分航线统计数据为空")
                return {"created": 0, "errors": 0}
            
            # 批量写入数据
            result = self._batch_write_ship_month_line_stats(df)
            
            logger.info(f"船舶月度分航线统计加载完成: 成功 {result['created']}, 失败 {result['errors']}")
            result["success"] = result["errors"] == 0
            result["total_created"] = result["created"]
            result["total_errors"] = result["errors"]
            return result

        except Exception as e:
            logger.error(f"从Oracle加载船舶月度分航线统计失败: {e}")
            return {"created": 0, "errors": 1, "success": False, "total_created": 0, "total_errors": 1}
    
    def execute_etl(self, ship_filter: str = None, time_filter: str = None, limit: int = 10000) -> Dict[str, Any]:
        """
        执行完整的船舶动态节点ETL流程

        Args:
            ship_filter: 船舶过滤条件，如 "ship_name_cn = '汉海5号'"
            time_filter: 时间过滤条件，如 "ym >= '202401'"
            limit: 记录数限制，默认10000

        Returns:
            Dict: 完整的ETL执行结果
        """
        start_time = datetime.now()
        logger.info("开始执行船舶动态节点ETL流程")
        
        results = {
            "start_time": start_time,
            "ship_month_stats": {"created": 0, "errors": 0},
            "ship_month_cargo_stats": {"created": 0, "errors": 0},
            "ship_month_line_stats": {"created": 0, "errors": 0},
            "total_created": 0,
            "total_errors": 0,
            "duration_seconds": 0
        }
        
        try:
            # 1. 加载船舶月度统计
            logger.info("1. 开始加载船舶月度统计数据")
            month_result = self.load_ship_month_stats(ship_filter, time_filter, limit)
            results["ship_month_stats"] = month_result

            # 2. 加载船舶月度分货类统计
            logger.info("2. 开始加载船舶月度分货类统计数据")
            cargo_result = self.load_ship_month_cargo_stats(ship_filter, time_filter, limit)
            results["ship_month_cargo_stats"] = cargo_result

            # 3. 加载船舶月度分航线统计
            logger.info("3. 开始加载船舶月度分航线统计数据")
            line_result = self.load_ship_month_line_stats(ship_filter, time_filter, limit)
            results["ship_month_line_stats"] = line_result
            
            # 计算总计
            results["total_created"] = (
                month_result["created"] + 
                cargo_result["created"] + 
                line_result["created"]
            )
            results["total_errors"] = (
                month_result["errors"] + 
                cargo_result["errors"] + 
                line_result["errors"]
            )
            
        except Exception as e:
            logger.error(f"船舶动态节点ETL流程执行失败: {e}")
            results["total_errors"] += 1
        
        finally:
            end_time = datetime.now()
            results["end_time"] = end_time
            results["duration_seconds"] = (end_time - start_time).total_seconds()
            
            logger.info(f"船舶动态节点ETL流程完成:")
            logger.info(f"  - 月度统计: 创建 {results['ship_month_stats']['created']}, 错误 {results['ship_month_stats']['errors']}")
            logger.info(f"  - 分货类统计: 创建 {results['ship_month_cargo_stats']['created']}, 错误 {results['ship_month_cargo_stats']['errors']}")
            logger.info(f"  - 分航线统计: 创建 {results['ship_month_line_stats']['created']}, 错误 {results['ship_month_line_stats']['errors']}")
            logger.info(f"  - 总计: 创建 {results['total_created']}, 错误 {results['total_errors']}")
            logger.info(f"  - 耗时: {results['duration_seconds']:.2f} 秒")
        
        return results
    
    def load_ships(self, ship_filter: str = None, time_filter: str = None, limit: int = 10000) -> Dict[str, int]:
        """
        从数据库加载船舶档案数据
        根据设计文档Ship节点的属性定义创建船舶节点

        Args:
            ship_filter: 船舶过滤条件，如 "ship_name_cn = '汉海5号'"
            time_filter: 时间过滤条件，如 "ym >= '202401'"
            limit: 记录数限制，默认10000

        Returns:
            Dict: 加载的节点统计信息
        """
        # 构建动态SQL查询
        query = """
        SELECT DISTINCT
               t.ship_id,
               t.ship_name_cn,
               t.ship_no,
               t.ship_firstreg_no,
               t.ship_inspect_no,
               t.mmsi,
               t.regport_code,
               t.regport_name,
               t.regport_prov,
               t.ship_owner,
               t.ship_operator,
               t.SHIP_REGION_TYPE,
               t.ship_sub_type_code,
               t.SHIP_SUB_TYPE_NAME,
               t.ship_type_code,
               t.SHIP_TYPE_NAME,
               t.SHIP_DWT,
               t.ship_grosston,
               t.ship_built_date
        FROM yssjtj.dwd_ship_eep_report_i_d t
        WHERE 1=1 and ship_name_cn='汉海5号'
        """

        # 动态添加过滤条件
        if ship_filter:
            query += f" AND {ship_filter}"
        if time_filter:
            query += f" AND {time_filter}"
        else:
            query += " AND t.ym >= '202401'"

        query += f" AND ROWNUM <= {limit}"

        try:
            # 使用 OracleClient 执行查询，将结果转换为 DataFrame
            df = self.db_manager.oracle.execute_query_to_dataframe(query)
            if df.empty:
                logger.warning("从数据库查询到的船舶档案数据为空")
                return {"ships": 0, "ship_type_relations": 0}

            logger.info(f"从数据库加载了{len(df)}条船舶记录")
        except Exception as e:
            logger.error(f"查询船舶数据失败: {e}")
            return {"ships": 0, "ship_type_relations": 0}

        ships_count = 0
        ship_type_relations = 0

        # 使用 Neo4jClient 会话
        with self.db_manager.neo4j.get_session() as session:
            for _, row in df.iterrows():
                # 处理日期字段
                built_date = None
                if pd.notna(row['SHIP_BUILT_DATE']) and str(row['SHIP_BUILT_DATE']).strip():
                    try:
                        # 尝试解析日期格式 "2020/12/23"
                        built_date_str = str(row['SHIP_BUILT_DATE']).strip()
                        if '/' in built_date_str:
                            built_date = built_date_str.replace('/', '-')
                        else:
                            built_date = built_date_str
                    except Exception as e:
                        logger.warning(f"船舶 {row['SHIP_ID']} 建造日期解析失败: {e}")

                # 记录所有属性用于调试
                debug_props = {
                    "mmsi": str(row['MMSI']) if pd.notna(row['MMSI']) else "",
                    "shipId": str(row['SHIP_ID']) if pd.notna(row['SHIP_ID']) else "",
                    "name": str(row['SHIP_NAME_CN']) if pd.notna(row['SHIP_NAME_CN']) else "",
                    "firstRegNo": str(row['SHIP_FIRSTREG_NO']) if pd.notna(row['SHIP_FIRSTREG_NO']) else "",
                    "inspectNo": str(row['SHIP_INSPECT_NO']) if pd.notna(row['SHIP_INSPECT_NO']) else "",
                    "regPortCode": str(row['REGPORT_CODE']) if pd.notna(row['REGPORT_CODE']) else "",
                    "regPortName": str(row['REGPORT_NAME']) if pd.notna(row['REGPORT_NAME']) else "",
                    "owner": str(row['SHIP_OWNER']) if pd.notna(row['SHIP_OWNER']) else "",
                    "operator": str(row['SHIP_OPERATOR']) if pd.notna(row['SHIP_OPERATOR']) else "",
                    "navAreaType": str(row['SHIP_REGION_TYPE']) if pd.notna(row['SHIP_REGION_TYPE']) else "",
                    "dwt": float(row['SHIP_DWT']) if pd.notna(row['SHIP_DWT']) and str(row['SHIP_DWT']).replace('.','').isdigit() else 0.0,
                    "grossTon": float(row['SHIP_GROSSTON']) if pd.notna(row['SHIP_GROSSTON']) and str(row['SHIP_GROSSTON']).replace('.','').isdigit() else 0.0,
                    "builtDate": built_date
                }
                logger.debug(f"即将写入Neo4j的Ship属性: {debug_props}")

                # 创建船舶节点 - 按照设计文档的Ship属性，使用MMSI作为主键
                session.run("""
                    MERGE (s:Ship {mmsi: $mmsi})
                    SET s.shipId = $ship_id,
                        s.shipNo = $ship_no,
                        s.name = $name,
                        s.firstRegNo = $first_reg_no,
                        s.inspectNo = $inspect_no,
                        s.regPortCode = $reg_port_code,
                        s.regPortName = $reg_port_name,
                        s.regPortProvince = $reg_port_province,
                        s.owner = $owner,
                        s.operator = $operator,
                        s.navAreaType = $nav_area_type,
                        s.dwt = $dwt,
                        s.grossTon = $gross_ton,
                        s.builtDate = $built_date,
                        s.lastProfileUpdate = datetime()
                """,
                mmsi=str(row['MMSI']) if pd.notna(row['MMSI']) else "",
                ship_id=str(row['SHIP_ID']) if pd.notna(row['SHIP_ID']) else "",
                ship_no=str(row['SHIP_NO']) if pd.notna(row['SHIP_NO']) else "",
                name=str(row['SHIP_NAME_CN']) if pd.notna(row['SHIP_NAME_CN']) else "",
                first_reg_no=str(row['SHIP_FIRSTREG_NO']) if pd.notna(row['SHIP_FIRSTREG_NO']) else "",
                inspect_no=str(row['SHIP_INSPECT_NO']) if pd.notna(row['SHIP_INSPECT_NO']) else "",
                reg_port_code=str(row['REGPORT_CODE']) if pd.notna(row['REGPORT_CODE']) else "",
                reg_port_name=str(row['REGPORT_NAME']) if pd.notna(row['REGPORT_NAME']) else "",
                reg_port_province=str(row['REGPORT_PROV']) if pd.notna(row['REGPORT_PROV']) else "",
                owner=str(row['SHIP_OWNER']) if pd.notna(row['SHIP_OWNER']) else "",
                operator=str(row['SHIP_OPERATOR']) if pd.notna(row['SHIP_OPERATOR']) else "",
                nav_area_type=str(row['SHIP_REGION_TYPE']) if pd.notna(row['SHIP_REGION_TYPE']) else "",
                dwt=float(row['SHIP_DWT']) if pd.notna(row['SHIP_DWT']) and str(row['SHIP_DWT']).replace('.','').isdigit() else 0.0,
                gross_ton=float(row['SHIP_GROSSTON']) if pd.notna(row['SHIP_GROSSTON']) and str(row['SHIP_GROSSTON']).replace('.','').isdigit() else 0.0,
                built_date=built_date)

                ships_count += 1
                logger.debug(f"创建船舶: {row['SHIP_NAME_CN']} (MMSI: {row['MMSI']})，所有属性: {debug_props}")


                # try:
                #     check_result = session.run(
                #         "MATCH (s:Ship {mmsi: $mmsi, name: $name}) RETURN properties(s) as props",
                #         mmsi=str(row['MMSI']) if pd.notna(row['MMSI']) else "",
                #         name=str(row['SHIP_NAME_CN']) if pd.notna(row['SHIP_NAME_CN']) else ""
                #     )
                #     props = check_result.single()
                #     if props:
                #         logger.info(f"Neo4j中Ship节点实际属性: {props['props']}")
                #     else:
                #         logger.warning(f"未找到刚创建的Ship节点: mmsi={row['MMSI']}, name={row['SHIP_NAME_CN']}")
                # except Exception as e:
                #     logger.error(f"检查Neo4j Ship节点属性失败: {e}")

                # 建立船舶与船舶类型的关系（用SHIP_SUB_TYPE_NAME关联）
                if pd.notna(row['SHIP_SUB_TYPE_NAME']):
                    result = session.run("""
                        MATCH (s:Ship {name: $name})
                        MATCH (st:ShipType {subName: $sub_name})
                        MERGE (s)-[:IS_TYPE]->(st)
                        RETURN count(*) as created
                    """,
                    name=str(row['SHIP_NAME_CN']),
                    sub_name=str(row['SHIP_SUB_TYPE_NAME']))

                    if result.single()['created'] > 0:
                        ship_type_relations += 1
                        logger.debug(f"建立船舶-类型关系: {row['SHIP_NAME_CN']} -> {row['SHIP_SUB_TYPE_NAME']}")
                    else:
                        logger.warning(f"未找到船舶类型 {row['SHIP_SUB_TYPE_NAME']} 为船舶 {row['SHIP_NAME_CN']}")

        logger.info(f"创建了{ships_count}个船舶节点和{ship_type_relations}个船舶-类型关系")
        return {"ships": ships_count, "ship_type_relations": ship_type_relations}
    def get_statistics(self) -> Dict[str, int]:
        """
        获取船舶动态节点统计信息
        
        Returns:
            Dict: 各类船舶动态节点的数量统计
        """
        stats = {
            "ship_month_stats": 0,
            "ship_month_cargo_stats": 0,
            "ship_month_line_stats": 0,
            "total_dynamic_nodes": 0
        }
        
        try:
            with self.db_manager.neo4j.get_session() as session:
                # 船舶月度统计节点数量
                result = session.run("MATCH (sms:ShipMonthStat) RETURN count(sms) as count")
                stats["ship_month_stats"] = result.single()['count']
                
                # 船舶月度分货类统计节点数量
                result = session.run("MATCH (smcs:ShipMonthCargoStat) RETURN count(smcs) as count")
                stats["ship_month_cargo_stats"] = result.single()['count']
                
                # 船舶月度分航线统计节点数量
                result = session.run("MATCH (smls:ShipMonthLineStat) RETURN count(smls) as count")
                stats["ship_month_line_stats"] = result.single()['count']
                
                # 总计
                stats["total_dynamic_nodes"] = (
                    stats["ship_month_stats"] + 
                    stats["ship_month_cargo_stats"] + 
                    stats["ship_month_line_stats"]
                )
                
        except Exception as e:
            logger.error(f"获取船舶动态节点统计失败: {e}")
        
        return stats
    
    
    def get_ship_summary(self) -> List[Dict]:
        """
        获取船舶动态统计摘要信息
        
        Returns:
            List[Dict]: 船舶统计摘要列表
        """
        try:
            with self.db_manager.neo4j.get_session() as session:
                result = session.run("""
                    MATCH (s:Ship)<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)
                    WITH s, count(sms) as month_stat_count
                    OPTIONAL MATCH (s)<-[:CARGO_STAT_FOR_SHIP]-(smcs:ShipMonthCargoStat)
                    WITH s, month_stat_count, count(smcs) as cargo_stat_count
                    OPTIONAL MATCH (s)<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)
                    WITH s, month_stat_count, cargo_stat_count, count(smls) as line_stat_count
                    RETURN s.mmsi as mmsi, s.name as ship_name,
                           month_stat_count, cargo_stat_count, line_stat_count
                    ORDER BY month_stat_count DESC
                    LIMIT 100
                """)
                
                return [dict(record) for record in result]
                
        except Exception as e:
            logger.error(f"获取船舶动态统计摘要失败: {e}")
            return []

    def get_ship_by_mmsi(self, mmsi: str) -> Dict[str, Any]:
        """
        根据MMSI获取特定船舶的详细数据
        
        Args:
            mmsi: 船舶的MMSI号码
            
        Returns:
            Dict: 船舶详细数据，包含基本信息和统计数据
        """
        try:
            with self.db_manager.neo4j.get_session() as session:
                result = session.run("""
                    MATCH (s:Ship {mmsi: $mmsi})
                    OPTIONAL MATCH (s)<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth)
                    OPTIONAL MATCH (s)<-[:CARGO_STAT_FOR_SHIP]-(smcs:ShipMonthCargoStat)-[:CARGO_STAT_FOR_MONTH]->(ym2:YearMonth)
                    OPTIONAL MATCH (s)<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)-[:LINE_STAT_FOR_MONTH]->(ym3:YearMonth)
                    OPTIONAL MATCH (smls)-[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
                    WITH s,
                         collect(DISTINCT {
                             ym: sms.ym,
                             year: ym.year,
                             month: ym.month,
                             opRatio: sms.opRatio,
                             voyages: sms.voyages,
                             loadRatio: sms.loadRatio,
                             anchorTime_day: sms.anchorTime_day,
                             sailTime_day: sms.sailTime_day,
                             sailRatio: sms.sailRatio,
                             turnover_tonkm: sms.turnover_tonkm,
                             loadVoyages: sms.loadVoyages,
                             dispatchLoadRatio: sms.dispatchLoadRatio,
                             distanceLoadRatio: sms.distanceLoadRatio,
                             capacity_ton: sms.capacity_ton,
                             loadVoyRatio: sms.loadVoyRatio,
                             mileage_km: sms.mileage_km,
                             tonDayOutput: sms.tonDayOutput,
                             tonShipOutput: sms.tonShipOutput,
                             lastUpdated: sms.lastUpdated
                         }) as month_stats,
                         collect(DISTINCT {
                             ym: smcs.ym,
                             year: ym2.year,
                             month: ym2.month,
                             cargoTypeName: smcs.subName,
                             cargo_ton: smcs.cargo_ton,
                             lastUpdated: smcs.lastUpdated
                         }) as cargo_stats,
                         collect(DISTINCT {
                             ym: smls.ym,
                             year: ym3.year,
                             month: ym3.month,
                             routeId: sr.routeId,
                             routeName: sr.routeName,
                             originPort: sr.originPortName,
                             destinationPort: sr.destinationPortName,
                             cargo_ton: smls.cargo_ton,
                             turnover_tonkm: smls.turnover_tonkm,
                             mileage_km: smls.mileage_km,
                             voyageCount: smls.voyageCount,
                             avgLoadRatio: smls.avgLoadRatio,
                             lastUpdated: smls.lastUpdated
                         }) as line_stats
                    RETURN s.mmsi as mmsi,
                           s.shipId as shipId,
                           s.name as name,
                           s.firstRegNo as firstRegNo,
                           s.inspectNo as inspectNo,
                           s.regPortCode as regPortCode,
                           s.regPortName as regPortName,
                           s.owner as owner,
                           s.operator as operator,
                           s.navAreaType as navAreaType,
                           s.dwt as dwt,
                           s.grossTon as grossTon,
                           s.builtDate as builtDate,
                           s.lastProfileUpdate as lastProfileUpdate,
                           month_stats,
                           cargo_stats,
                           line_stats
                """, mmsi=mmsi)
                
                record = result.single()
                if not record:
                    return {'error': f'未找到MMSI为{mmsi}的船舶'}
                
                return {
                    'ship_profile': {
                        'mmsi': record['mmsi'],
                        'shipId': record['shipId'],
                        'name': record['name'],
                        'firstRegNo': record['firstRegNo'],
                        'inspectNo': record['inspectNo'],
                        'regPortCode': record['regPortCode'],
                        'regPortName': record['regPortName'],
                        'owner': record['owner'],
                        'operator': record['operator'],
                        'navAreaType': record['navAreaType'],
                        'dwt': record['dwt'],
                        'grossTon': record['grossTon'],
                        'builtDate': record['builtDate']                   },
                    'monthly_statistics': [stat for stat in record['month_stats'] if stat.get('ym')],
                    'cargo_statistics': [stat for stat in record['cargo_stats'] if stat.get('ym')],
                    'line_statistics': [stat for stat in record['line_stats'] if stat.get('ym')],
                    'statistics_summary': {
                        'month_stats_count': len([stat for stat in record['month_stats'] if stat.get('ym')]),
                        'cargo_stats_count': len([stat for stat in record['cargo_stats'] if stat.get('ym')]),
                        'line_stats_count': len([stat for stat in record['line_stats'] if stat.get('ym')])
                    }
                }
                
        except Exception as e:
            logger.error(f"根据MMSI获取船舶数据失败: {e}")
            return {'error': str(e)}