# **C-STEL: 长江航运时间表达语言 - 技术规格文档**

**版本:** 1.0
**日期:** 2025年7月30日

---

## 1. 概述 (Overview)

### 1.1. 目标 (Purpose)

**C-STEL (Changjiang Shipping Time Expression Language)** 是一种专为长江航运智能分析领域设计的、标准化的时间表达语言。其核心目标是解决在人机交互中，自然语言时间的模糊性与后端数据查询的精确性之间的矛盾。

它作为连接上层大语言模型（LLM）与底层数据API之间的桥梁，将“去年上半年”、“近三个季度”、“今年春节和国庆”等模糊描述，转换为机器可以精确解析和执行的、无歧改义的字符串。

### 1.2. 设计哲学 (Design Philosophy)

C-STEL的设计严格遵循以下三大原则：

1.  **精确无歧义 (Precision & Unambiguity)**: 任何一个合法的C-STEL表达式都**必须**有且仅有一个确定的时间解释。
2.  **可组合性 (Composability)**: 语言的原子单位（年、月、日）可以通过简单的操作符进行无限组合，以表达复杂的时间查询逻辑。
3.  **可读性 (Readability)**: 表达式的设计应尽可能直观，便于人类开发者和高级用户阅读、调试和手动编写。

### 1.3. 应用场景 (Use Cases)

*   **LLM意图翻译**: LLM将用户的自然语言查询翻译成C-STEL，作为调用下游分析API的参数。
*   **API请求参数**: 后端API直接接收C-STEL字符串作为标准的时间查询参数。
*   **前端组件**: 高级分析仪表盘中的时间选择器可以直接生成或接收C-STEL字符串。
*   **任务调度**: 在自动化报告或批处理任务中，使用C-STEL定义任务执行的时间范围。

---

## 2. 语法规格 (Syntax Specification)

### 2.1. 原子时间单位 (Atomic Time Units)

原子单位是C-STEL不可分割的基础构建块。

| 单位 | 格式 | 示例 | 说明 |
| :--- | :--- | :--- | :--- |
| **年 (Year)** | `Y<YYYY>` | `Y2024` | 代表完整的公历年（从1月1日到12月31日）。 |
| **季 (Quarter)** | `Q<YYYY>Q<Q>` | `Q2024Q1` | 代表一个自然季度（Q 必须是 1, 2, 3, 4）。 |
| **月 (Month)** | `M<YYYYMM>` | `M202403` | 代表一个完整的公历月（MM 必须是 01-12）。 |
| **日 (Day)** | `D<YYYYMMDD>` | `D20240315` | 代表一个具体的公历日。 |

### 2.2. 组合操作符 (Composition Operators)

操作符用于将原子单位粘合成更复杂的表达式。

#### **区间操作符 (`_`)**

*   **功能**: 定义一个从开始时间单位到结束时间单位的**连续**时间范围。
*   **语法**: `<Start_Bucket>_<End_Bucket>`
*   **示例**:
    *   `M202401_M202403` → 表示从2024年1月1日到2024年3月31日的完整时间段。
    *   `Y2022_Y2024` → 表示2022、2023、2024三个完整的年份。
*   **约束**:
    > **[!] 重要**: 区间操作符连接的两个原子单位的**粒度必须完全相同**。例如，`M202401_Y2024` 是一个**非法的表达式**。

#### **列表操作符 (`,`)**

*   **功能**: 将一个或多个独立的、**不连续**的时间表达式组合成一个集合。这是实现复杂查询的核心。
*   **语法**: `<Expression1>,<Expression2>,<Expression3>,...`
*   **示例**:
    *   `Y2023,Y2024` → 表示2023年和2024年这两个独立的时间段。
    *   `Y2022,Q2023Q2,M202405` → 表示三个不同粒度的独立时间段的集合。
    *   `M202301_M202303,M202401_M202403` → 表示两个不连续的区间的集合（例如用于同比分析）。

### 2.3. 高级表达式 (Advanced Expressions)

高级表达式是为常见查询场景提供的语法糖（Shortcuts），以提高表达效率。

| 类型 | 格式 | 示例 | 说明 |
| :--- | :--- | :--- | :--- |
| **相对时间 (Relative)** | `R<N><Unit>` | `R6M` | 以`current_date`为基准，向前追溯N个时间单位(Y/Q/M/D)。`R6M`表示包含当月在内的近6个月。 |
| **自动展开 (Auto-Expand)** | `A<Bucket>_<Granularity>` | `AY2024_M` | 将一个大时间桶`Bucket`按指定的`Granularity`粒度展开成一个列表。`AY2024_M`等价于`M202401,M202402,...,M202412`。 |
| **业务周期 (Business Cycles)** | `C<Code>` | `CYTD` | 以`current_date`为基准的常见业务周期。`CYTD`表示今年以来，`CQTD`表示本季度以来，`CMTD`表示本月以来。|

---

## 3. 解析与执行 (Parsing & Execution)

### 3.1. 解析流程

一个标准的C-STEL后端解析器应遵循以下逻辑流程：

1.  **分割**: 首先，使用列表操作符 `,` 作为第一级分隔符，将整个表达式字符串分割成一个“表达式组件”数组。
2.  **识别**: 遍历数组中的每个组件，判断其类型：
    *   如果包含区间操作符 `_`，则作为**区间表达式**处理。
    *   如果以 `R`, `A`, `C` 开头，则作为**高级表达式**处理。
    *   否则，作为**原子时间单位**处理。
3.  **转换**: 将每个解析出的组件转换为后端数据库可以理解的查询条件（如SQL的`WHERE`子句）。
4.  **组合**: 将所有组件转换的查询条件用 `OR`逻辑组合起来，形成最终的数据库查询。

### 3.2. 上下文依赖

> **[!] 实现关键**: C-STEL的解析**强依赖**于一个外部传入的`current_date`（当前日期）上下文。所有高级表达式（`R`, `C`）以及自然语言中的相对时间（如“去年”、“上个月”）都必须基于此日期进行计算，以确保解析结果的确定性。

---

## 4. 综合示例 (Comprehensive Examples)

假设 `current_date` = `2025-07-30`

| 用户自然语言查询 | 生成的C-STEL表达式 | 解析说明 |
| :--- | :--- | :--- |
| “查询汉海5号去年上半年的月度趋势” | `M202401_M202406` | 一个单一的月度区间表达式。 |
| “对比武汉港和南京港去年和今年的总吞吐量” | `Y2024,Y2025` | 两个独立的年度原子单位，通过列表操作符组合，用于对比。 |
| “分析长江干线2023年全年，2024年Q2，以及上个月的货运构成” | `Y2023,Q2024Q2,M202506` | 三个不同粒度的原子单位，通过列表操作符组合。 |
| “列出2024年每个季度的数据，并附上今年以来的总数” | `AY2024_Q,CYTD` | 一个自动展开表达式和一个业务周期表达式的组合。`AY2024_Q`会被解析为`Q2024Q1,Q2024Q2,Q2024Q3,Q2024Q4`。 |

---

## 5. 附录 (Appendix)

### A. LLM集成建议

为确保LLM能够稳定、准确地生成C-STEL表达式，建议采用**Function Calling**（工具调用）模式。

1.  **定义工具**: 将所有需要时间参数的业务API（如`get_metric_trend`）定义为LLM可用的工具。
2.  **内嵌参数**: 将C-STEL字符串作为这些工具的一个必需参数（如`time_expression: string`）。
3.  **提供指令**: 在System Prompt中，清晰地提供C-STEL的语法规则，并强制LLM在调用工具时，必须将用户的时间描述转换为`time_expression`参数。

**示例工具定义 (JSON Schema):**
```json
{
  "type": "function",
  "function": {
    "name": "get_metric_trend",
    "description": "获取单一实体某一指标随时间变化的趋势数据。",
    "parameters": {
      "type": "object",
      "properties": {
        "entity_name": { "type": "string" },
        "metric": { "type": "string" },
        "time_expression": {
          "type": "string",
          "description": "描述查询时间的C-STEL表达式，例如 'M202401_M202412'。"
        }
      },
      "required": ["entity_name", "metric", "time_expression"]
    }
  }
}
```

### B. 版本历史

*   **V1.0 (2025-07-30)**: 初始版本发布。定义了原子单位、组合操作符和高级表达式。