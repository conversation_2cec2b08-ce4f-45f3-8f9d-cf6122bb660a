#!/usr/bin/env python3
"""
调试港口航行数据查询问题
"""

import sys
import os

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def debug_port_navigation():
    """调试港口航行数据查询"""
    from kgnode.database import DatabaseManager
    
    db = DatabaseManager()
    
    try:
        with db.neo4j.get_session() as session:
            print("=== 调试港口航行数据查询 ===")
            
            # 1. 检查九江港的基本信息
            result = session.run("""
                MATCH (p:Port {name: '九江'})
                RETURN p.name as port_name
            """)
            port_info = result.single()
            if port_info:
                print(f"✅ 找到港口: {port_info['port_name']}")
            else:
                print("❌ 未找到九江港")
                return
            
            # 2. 检查PortMonthStat节点
            result = session.run("""
                MATCH (p:Port {name: '九江'})<-[:STAT_FOR_PORT]-(pms:PortMonthStat)
                RETURN count(pms) as stat_count
            """)
            stat_count = result.single()['stat_count']
            print(f"九江港PortMonthStat节点数量: {stat_count}")
            
            if stat_count == 0:
                print("❌ 没有找到PortMonthStat节点")
                # 检查是否有其他类型的统计节点
                result = session.run("""
                    MATCH (p:Port {name: '九江'})<-[r]-(n)
                    RETURN type(r) as rel_type, labels(n) as node_labels, count(*) as count
                """)
                print("九江港的所有关系:")
                for record in result:
                    print(f"  关系: {record['rel_type']}, 节点类型: {record['node_labels']}, 数量: {record['count']}")
                return
            
            # 3. 检查特定期间的PortMonthStat
            result = session.run("""
                MATCH (p:Port {name: '九江'})<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth)
                RETURN DISTINCT ym.ym as period
                ORDER BY ym.ym DESC
                LIMIT 5
            """)
            
            periods = []
            print("九江港PortMonthStat有数据的期间:")
            for record in result:
                period = record['period']
                periods.append(period)
                print(f"  {period}")
            
            if not periods:
                print("❌ 九江港PortMonthStat没有时间期间数据")
                return
            
            test_period = periods[0]
            print(f"\n测试期间: {test_period}")
            
            # 4. 检查该期间的PortMonthStat数据
            result = session.run("""
                MATCH (p:Port {name: '九江'})<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
                RETURN pms.outShipCount as out_ship_count,
                       pms.inShipCount as in_ship_count,
                       pms.outCargo_ton as out_cargo,
                       pms.inCargo_ton as in_cargo
                LIMIT 3
            """, period=test_period)
            
            print(f"期间 {test_period} 的PortMonthStat数据:")
            for i, record in enumerate(result):
                print(f"  {i+1}. 出港船舶: {record['out_ship_count']}, 进港船舶: {record['in_ship_count']}")
                print(f"      出港货物: {record['out_cargo']}, 进港货物: {record['in_cargo']}")
            
            # 5. 检查PORT_TO_PORT关系
            result = session.run("""
                MATCH (p:Port {name: '九江'})-[:PORT_TO_PORT]-(otherPort:Port)
                RETURN count(otherPort) as connected_ports
            """)
            connected_ports = result.single()['connected_ports']
            print(f"\n九江港通过PORT_TO_PORT连接的港口数量: {connected_ports}")
            
            if connected_ports == 0:
                print("❌ 九江港没有PORT_TO_PORT关系")
                # 检查是否有其他港口间关系
                result = session.run("""
                    MATCH (p:Port {name: '九江'})-[r]-(otherPort:Port)
                    RETURN type(r) as rel_type, count(*) as count
                """)
                print("九江港与其他港口的关系:")
                for record in result:
                    print(f"  关系类型: {record['rel_type']}, 数量: {record['count']}")
                return
            
            # 6. 显示连接的港口
            result = session.run("""
                MATCH (p:Port {name: '九江'})-[:PORT_TO_PORT]-(otherPort:Port)
                RETURN otherPort.name as port_name
                LIMIT 5
            """)
            
            print("九江港连接的港口:")
            for record in result:
                print(f"  {record['port_name']}")
            
            # 7. 测试完整查询
            print(f"\n测试完整查询 (期间: {test_period}):")
            result = session.run("""
                MATCH (p:Port {name: $portName})
                MATCH (p)<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
                MATCH (p)-[:PORT_TO_PORT]-(otherPort:Port)
                OPTIONAL MATCH (otherPort)<-[:STAT_FOR_PORT]-(otherPms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym)
                WITH p, otherPort,
                     COALESCE(pms.outShipCount, 0) + COALESCE(otherPms.inShipCount, 0) as ship_count,
                     COALESCE(pms.outCargo_ton, 0) + COALESCE(otherPms.inCargo_ton, 0) as cargo_volume
                WHERE ship_count > 0 OR cargo_volume > 0
                RETURN otherPort.name as destination,
                       ship_count,
                       cargo_volume
                ORDER BY cargo_volume DESC
                LIMIT 5
            """, portName='九江', period=test_period)
            
            count = 0
            for record in result:
                count += 1
                dest = record['destination']
                ships = record['ship_count']
                cargo = record['cargo_volume']
                print(f"  {count}. 目的地: {dest}, 船舶数: {ships}, 货物量: {cargo}")
            
            if count == 0:
                print("  ❌ 完整查询没有返回结果")
                
                # 进一步调试：分步检查
                print("\n进一步调试:")
                
                # 检查第一步
                result = session.run("""
                    MATCH (p:Port {name: $portName})
                    MATCH (p)<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
                    RETURN count(*) as step1_count
                """, portName='九江', period=test_period)
                step1 = result.single()['step1_count']
                print(f"  步骤1 (港口+统计+时间): {step1} 条记录")
                
                # 检查第二步
                result = session.run("""
                    MATCH (p:Port {name: $portName})
                    MATCH (p)<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
                    MATCH (p)-[:PORT_TO_PORT]-(otherPort:Port)
                    RETURN count(*) as step2_count
                """, portName='九江', period=test_period)
                step2 = result.single()['step2_count']
                print(f"  步骤2 (加上港口连接): {step2} 条记录")
                
                # 检查第三步
                result = session.run("""
                    MATCH (p:Port {name: $portName})
                    MATCH (p)<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
                    MATCH (p)-[:PORT_TO_PORT]-(otherPort:Port)
                    OPTIONAL MATCH (otherPort)<-[:STAT_FOR_PORT]-(otherPms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym)
                    RETURN count(*) as step3_count
                """, portName='九江', period=test_period)
                step3 = result.single()['step3_count']
                print(f"  步骤3 (加上目标港口统计): {step3} 条记录")
                
            else:
                print(f"  ✅ 完整查询返回 {count} 条结果")
                
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close_all()

if __name__ == "__main__":
    debug_port_navigation()
