"""
长江航运智能指标问答Agent (CJHY-KGAgent)
智能记忆模块

基于SQLite + FAISS + BGE实现对话历史存储、检索、语义相似度查询等功能
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger
from pathlib import Path

from ..config import settings
from .mem_store import SqliteFaissMemoryStore


class MemoryService:
    """智能记忆服务"""
    
    def __init__(self, db_path: str = None, index_path: str = None):
        """
        初始化记忆服务
        
        Args:
            db_path: 数据库路径
            index_path: 索引文件路径
        """
        # 设置默认路径
        if db_path is None:
            db_path = Path("data") / "memory.db"
        if index_path is None:
            index_path = Path("data") / "memory.faiss"
        
        # 确保数据目录存在
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)
        Path(index_path).parent.mkdir(parents=True, exist_ok=True)
        
        self.store = None
        self._initialize_store(db_path, index_path)
    
    def _initialize_store(self, db_path: str, index_path: str):
        """初始化存储后端"""
        try:
            logger.info("初始化SQLite + FAISS智能记忆存储...")
            self.store = SqliteFaissMemoryStore(db_path, index_path)
            logger.info("记忆存储初始化成功")
        except Exception as e:
            logger.error(f"记忆存储初始化失败: {e}")
            self.store = None
    
    def store_conversation(self, user_question: str, agent_answer: str, 
                          entities: Dict[str, Any] = None, 
                          metadata: Dict[str, Any] = None) -> str:
        """存储对话记录"""
        if self.store is None:
            logger.warning("记忆存储未初始化，跳过存储")
            return "no_store"
        
        try:
            logger.info(f"存储对话: {user_question[:50]}...")
            
            # 从metadata中提取intent
            intent = metadata.get("intent") if metadata else None
            
            # 存储到SQLite + FAISS
            record_id = self.store.store_conversation(
                question=user_question,
                answer=agent_answer,
                intent=intent,
                entities=entities or {},
                metadata=metadata
            )
            
            return str(record_id)
            
        except Exception as e:
            logger.error(f"存储对话失败: {e}")
            return "error"
    
    def query_similar_conversations(self, question: str, entities: Dict[str, Any] = None, 
                                   limit: int = 5) -> List[Dict[str, Any]]:
        """查询相似对话"""
        if self.store is None:
            logger.warning("记忆存储未初始化，返回空结果")
            return []
        
        try:
            logger.info(f"查询相似对话: {question[:50]}...")
            
            # 查询相似对话
            similar_conversations = self.store.query_similar_conversations(
                question=question,
                entities=entities,
                limit=limit
            )
            
            return similar_conversations
             
        except Exception as e:
            logger.error(f"查询相似对话失败: {e}")
            return []
    
    def get_conversation_history(self, user_id: str = None, limit: int = 10) -> List[Dict[str, Any]]:
        """获取对话历史"""
        if self.store is None:
            logger.warning("记忆存储未初始化，返回空结果")
            return []
        
        try:
            logger.info(f"获取对话历史: 用户={user_id}, 限制={limit}")
            return self.store.get_conversation_history(limit=limit)
        except Exception as e:
            logger.error(f"获取对话历史失败: {e}")
            return []
    
    def search_by_entities(self, entities: Dict[str, Any], limit: int = 5) -> List[Dict[str, Any]]:
        """根据实体搜索记忆"""
        if self.store is None:
            logger.warning("记忆存储未初始化，返回空结果")
            return []
        
        try:
            logger.info(f"根据实体搜索: {entities}")
            return self.store.search_by_entities(entities, limit=limit)
        except Exception as e:
            logger.error(f"根据实体搜索失败: {e}")
            return []
    
    def search_by_time_range(self, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        """根据时间范围搜索记忆"""
        # TODO: 实现时间范围搜索逻辑
        logger.info(f"根据时间范围搜索: {start_time} - {end_time}")
        return []
    
    def update_memory(self, memory_id: str, updates: Dict[str, Any]) -> bool:
        """更新记忆"""
        # TODO: 实现记忆更新逻辑
        logger.info(f"更新记忆: {memory_id}")
        return True
    
    def delete_memory(self, memory_id: str) -> bool:
        """删除记忆"""
        if self.store is None:
            logger.warning("记忆存储未初始化")
            return False
        
        try:
            logger.info(f"删除记忆: {memory_id}")
            return self.store.delete_conversation(int(memory_id))
        except Exception as e:
            logger.error(f"删除记忆失败: {e}")
            return False
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """获取记忆统计信息"""
        if self.store is None:
            logger.warning("记忆存储未初始化")
            return {
                "total_conversations": 0,
                "total_entities": 0,
                "storage_usage": "0MB",
                "last_updated": datetime.now().isoformat()
            }
        
        try:
            return self.store.get_stats()
        except Exception as e:
            logger.error(f"获取记忆统计失败: {e}")
            return {
                "total_conversations": 0,
                "total_entities": 0,
                "storage_usage": "0MB",
                "last_updated": datetime.now().isoformat()
            }
    
    def validate_memory_freshness(self, memory_id: str, max_age_hours: int = 24) -> bool:
        """验证记忆时效性"""
        # TODO: 实现记忆时效性验证
        logger.info(f"验证记忆时效性: {memory_id}")
        return True
    
    def extract_entities_from_conversation(self, conversation: str) -> Dict[str, Any]:
        """从对话中提取实体"""
        # TODO: 实现实体提取逻辑
        logger.info("从对话中提取实体...")
        return {}
    
    def generate_conversation_summary(self, conversation: str) -> str:
        """生成对话摘要"""
        # TODO: 实现对话摘要生成
        logger.info("生成对话摘要...")
        return "对话摘要..."


class ConversationContext:
    """对话上下文管理"""
    
    def __init__(self):
        self.current_session = {}
        self.session_history = []
    
    def start_new_session(self, user_id: str = None) -> str:
        """开始新的对话会话"""
        # TODO: 实现新会话开始逻辑
        session_id = f"session_{datetime.now().timestamp()}"
        self.current_session = {
            "session_id": session_id,
            "user_id": user_id,
            "start_time": datetime.now(),
            "messages": [],
            "entities": {},
            "context": {}
        }
        return session_id
    
    def add_message(self, message: Dict[str, Any]):
        """添加消息到当前会话"""
        # TODO: 实现消息添加逻辑
        self.current_session["messages"].append(message)
    
    def update_context(self, key: str, value: Any):
        """更新会话上下文"""
        self.current_session["context"][key] = value
    
    def get_current_context(self) -> Dict[str, Any]:
        """获取当前会话上下文"""
        return self.current_session.get("context", {})
    
    def end_session(self):
        """结束当前会话"""
        if self.current_session:
            self.session_history.append(self.current_session)
            self.current_session = {}


# 全局记忆服务实例
memory_service = MemoryService()
conversation_context = ConversationContext()


if __name__ == "__main__":
    # 记忆服务测试
    print("Mem0智能记忆服务测试...")
    
    service = MemoryService()
    
    # 测试存储对话
    memory_id = service.store_conversation(
        "九江港上个月的集装箱吞吐量是多少？",
        "根据数据显示，九江港上个月的集装箱吞吐量为12万TEU。",
        entities={"port": "九江港", "metric": "集装箱吞吐量", "time": "上个月"}
    )
    print(f"存储记忆ID: {memory_id}")
    
    # 测试查询相似对话
    similar = service.query_similar_conversations("九江港的货物吞吐量情况")
    print(f"相似对话: {similar}")
    
    # 测试统计信息
    stats = service.get_memory_stats()
    print(f"记忆统计: {stats}") 