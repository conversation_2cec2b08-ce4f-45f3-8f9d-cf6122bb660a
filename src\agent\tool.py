"""
船舶位置API工具模块
提供船舶实时位置查询功能
"""

from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
import random
from loguru import logger

class ShipToolKit:
    """船舶工具包 - 实现4个核心工具函数"""
    
    def __init__(self):
        """初始化工具包"""
        self.logger = logger

    
    def get_ais_data(self, keyword: str) -> Dict[str, Any]:
        """获取AIS数据：船型、航速、航向、上下水、吃水及位置

        Args:
            keyword: 船舶关键字（如MMSI或船名）

        Returns:
            AIS数据字典
        """
        import requests
        from datetime import datetime

        keyword = keyword.strip() if isinstance(keyword, str) else keyword
        self.logger.info(f"查询AIS数据: keyword={keyword}")
        url = "http://*************:18082/api/ais-test-api/chain-cloud/ais/realtime"
        params = {"keyword": keyword}
        try:
            response = requests.get(url, params=params, timeout=50)
            if response.status_code == 200:
                data = response.json()
                return {
                    "source": "ais_api",
                    "status": "success",
                    "data": data,
                    "timestamp": datetime.now().isoformat(),
                    "query_params": {"keyword": keyword}
                }
            else:
                error_msg = f"API请求失败，状态码: {response.status_code}"
        except Exception as e:
            error_msg = f"API请求异常: {str(e)}"
            self.logger.error(f"查询AIS数据 {keyword} 时出错: {e}")

        return {
            "source": "ais_api",
            "status": "error",
            "data": {},
            "timestamp": datetime.now().isoformat(),
            "query_params": {"keyword": keyword},
            "error": error_msg
        }
    
    def get_ship_archive(self,keyword:str) -> Dict[str, Any]:
        """
        获取船舶档案数据：吨位、长宽高、A级B级航区载重吨
        支持通过mmsi或ship_name查询 413784786
        """
        import requests
        from datetime import datetime
        keyword = keyword.strip() if isinstance(keyword, str) else keyword

        self.logger.info(f"查询船舶档案: keyword={keyword}")
        url = "http://*************:18082/api/ais-test-api/chain-cloud/ship/detail"
        params = {"keyword": keyword}
        full_url = requests.Request('GET', url, params=params).prepare().url
        self.logger.info(f"请求完整URL: {full_url}")
        try:
            response = requests.get(url, params=params, timeout=50)
            if response.status_code == 200:
                data = response.json()
                return {
                    "source": "archive_api",
                    "status": "success",
                    "data": data,
                    "timestamp": datetime.now().isoformat(),
                    "query_params": {"keyword": keyword}
                }
            else:
                error_msg = f"API请求失败，状态码: {response.status_code}"
        except Exception as e:
            error_msg = f"API请求异常: {str(e)}"
            self.logger.error(f"查询船舶档案 keyword={keyword} 时出错: {e}")

        return {
            "source": "archive_api",
            "status": "error",
            "data": {},
            "timestamp": datetime.now().isoformat(),
            "query_params": {"keyword": keyword},
            "error": error_msg
        }
    
    def get_area_ships(self, coordinates: str) -> Dict[str, Any]:
        """获取区域内船舶清单"""
        import requests
        from datetime import datetime
        coordinates = coordinates.strip() if isinstance(coordinates, str) else coordinates
        self.logger.info(f"查询区域船舶: {coordinates}")
        url = "http://*************:18082/api/ais-test-api/chain-cloud/ais/area-ship"
        try:
            resp = requests.get(url, params={"pointsStr": coordinates}, timeout=80)
            if resp.status_code == 200:
                data = resp.json().get("data", [])
                content = []
                for idx, ship in enumerate(data, 1):
                    name = ship.get("shipNameCn", "")
                    mmsi = ship.get("mmsi", "")
                    ship_type_name = ship.get("shipTypeName", "")
                    ship_length = ship.get("shipLength", "")
                    ship_breadth = ship.get("shipBreadth", "")
                    ship_depth = ship.get("shipDepth", "")  
                    if name:
                        if mmsi:
                            content.append(f"{idx}. {name},{mmsi},船舶类型({ship_type_name}),长宽型深({ship_length},{ship_breadth},{ship_depth})米")
                        else:
                            content.append(f"{idx}. {name}")
                content_str = "\n".join(content)
                return {
                    "source": "downgraded",
                    "status": "success",
                    "data": content_str,
                    "timestamp": datetime.now().isoformat(),
                    "count": len(data),
                    "query_params": {"coordinates": coordinates}
                }
            else:
                error = f"API请求失败，状态码: {resp.status_code}"
        except Exception as e:
            error = f"API请求异常: {str(e)}"
            self.logger.error(f"查询区域船舶 {coordinates} 时出错: {e}")
        return {
            "source": "area_api",
            "status": "error",
            "data": [],
            "timestamp": datetime.now().isoformat(),
            "count": 0,
            "query_params": {"coordinates": coordinates},
            "error": error
        }
    
    def get_section_data(self, section_name: str, ship_type: str = None) -> Dict[str, Any]:
        """获取断面数据，可选过滤船舶类型"""
        import requests
        section_name = section_name.strip() if isinstance(section_name, str) else section_name
        url = "http://*************:18082/api/ais-test-api/chain-cloud/ais/city-section-ship_td"
        params = {"sectionCity": section_name.replace("市", "")}
        if ship_type:
            params["shipType"] = ship_type
        try:
            resp = requests.get(url, params=params, timeout=100)
            if resp.status_code == 200:
                data = resp.json().get("data", {})
                content = []
                for idx, ship in enumerate(data, 1):
                    name = ship.get("中文船名", "")
                    mmsi = ship.get("mmsi", "")
                    sog = ship.get("速度", "")
                    org = ship.get("所处海事机构", "")
                    lat = ship.get("纬度", "")
                    lon = ship.get("经度", "")
                    if name:
                        if mmsi:
                            content.append(f"{idx}. {name},{mmsi},速度({sog}节),海事机构({org}),经纬度({lon},{lat})")
                        else:
                            content.append(f"{idx}. {name}")
                content_str = "\n".join(content)
                return {
                    "source": "downgraded",
                    "status": "success",
                    "data": content_str,
                    "timestamp": datetime.now().isoformat(),
                    "query_params": {"section_name": section_name, "ship_type": ship_type}
                }
            else:
                error = f"API请求失败，状态码: {resp.status_code}"
        except Exception as e:
            self.logger.error(f"查询断面数据 {section_name} 时出错: {e}")
            error = f"API请求异常: {str(e)}"
        return {
            "source": "section_api",
            "status": "error",
            "data": {},
            "timestamp": datetime.now().isoformat(),
            "query_params": {"section_name": section_name, "ship_type": ship_type},
            "error": error
        }

    def _process_ship_data(self, data, source_name, query_params):
        """处理船舶数据的公共方法"""
        from datetime import datetime
        content = []
        for idx, ship in enumerate(data, 1):
            name = ship.get("shipNameCn", "")
            mmsi = ship.get("mmsi", "")
            ship_type_name = ship.get("shipTypeName", "")
            ship_length = ship.get("shipLength", "")
            ship_breadth = ship.get("shipBreadth", "")
            ship_depth = ship.get("shipDepth", "")
            if name:
                if mmsi:
                    content.append(f"{idx}. {name},{mmsi},船舶类型({ship_type_name}),长宽型深({ship_length},{ship_breadth},{ship_depth})米")
                else:
                    content.append(f"{idx}. {name}")
        content_str = "\n".join(content)
        return {
            "source": source_name,
            "status": "success",
            "data": content_str,
            "timestamp": datetime.now().isoformat(),
            "count": len(data),
            "query_params": query_params
        }
    def _process_ship_data_section(self, data, source_name, query_params):
        """处理船舶数据的公共方法"""
        from datetime import datetime
        content = []
        for idx, ship in enumerate(data, 1):
            mmsi = ship.get("mmsi", "")
            ship_type_name = ship.get("shipTypeName", "")
            ship_name_cn = ship.get("shipNameCn", "")
            trip_name = ship.get("tripName", "")
            begin_date = ship.get("beginDate", "")
            end_date = ship.get("endDate", "")
            
            if ship_name_cn:
                if mmsi:
                    content.append(f"{idx}. {ship_name_cn},{mmsi},船舶类型({ship_type_name}),航程({trip_name}),时间({begin_date}-{end_date})")
                else:
                    content.append(f"{idx}. {ship_name_cn}")
        content_str = "\n".join(content)
        return {
            "source": source_name,
            "status": "success", 
            "data": content_str,
            "timestamp": datetime.now().isoformat(),
            "count": len(data),
            "query_params": query_params
        }
    def _handle_api_error(self, source_name, query_params, error):
        """处理API错误的公共方法"""
        from datetime import datetime
        return {
            "source": source_name,
            "status": "error",
            "data": [],
            "timestamp": datetime.now().isoformat(),
            "count": 0,
            "query_params": query_params,
            "error": error
        }

    def get_circle_ships(self, lat: float, lon: float, radius: float) -> Dict[str, Any]:
        """按经纬度和半径获取实时船舶数据（圆形区域）"""
        import requests
        url = "http://*************:18082/api/ais-test-api/chain-cloud/ais/circle-ship"
        params = {"lat": lat, "lon": lon, "radius": radius}  # radius单位：米
        self.logger.info(f"查询圆形区域船舶: lat={lat}, lon={lon}, radius={radius}")
        try:
            resp = requests.get(url, params=params, timeout=60)
            if resp.status_code == 200:
                data = resp.json().get("data", [])
                return self._process_ship_data(data, "downgraded", params)
            else:
                error = f"API请求失败，状态码: {resp.status_code}"
        except Exception as e:
            error = f"API请求异常: {str(e)}"
            self.logger.error(f"查询圆形区域船舶 lat={lat}, lon={lon}, radius={radius} 时出错: {e}")
        return self._handle_api_error("downgraded", params, error)

    def get_bridge_section_ships(self, section_name: str, minutes: int = 10) -> Dict[str, Any]:
        """按桥/口等截面名称获取实时船舶数据，可指定分钟数，默认10分钟"""
        import requests
        section_name = section_name.strip() if isinstance(section_name, str) else section_name
        url = "http://*************:18082/api/ais-test-api/chain-cloud/ais/bridge-section-ship"
        params = {"section_name": section_name, "minutes": minutes}
        self.logger.info(f"查询桥/口截面船舶: section_name={section_name}, minutes={minutes}")
        try:
            resp = requests.get(url, params=params, timeout=60)
            if resp.status_code == 200:
                data = resp.json().get("data", [])
                return self._process_ship_data_section(data, "downgraded", params)
            else:
                error = f"API请求失败，状态码: {resp.status_code}"
        except Exception as e:
            error = f"API请求异常: {str(e)}"
            self.logger.error(f"查询桥/口截面船舶 section_name={section_name}, minutes={minutes} 时出错: {e}")
        return self._handle_api_error("downgraded", params, error)



ship_toolkit = ShipToolKit()
# 导出主要工具函数
def get_ais_data(keyword: str) -> Dict[str, Any]:
    return ship_toolkit.get_ais_data(keyword)

def get_ship_archive(keyword: str) -> Dict[str, Any]:
    return ship_toolkit.get_ship_archive(keyword)

def get_area_ships(coordinates: str) -> Dict[str, Any]:
    """获取区域内船舶清单"""
    return ship_toolkit.get_area_ships(coordinates)

def get_section_data(section_name: str, ship_type: str = None) -> Dict[str, Any]:
    """获取城市断面数据，可选过滤船舶类型"""
    return ship_toolkit.get_section_data(section_name, ship_type)

def get_circle_ships(lat: float, lon: float, radius: float) -> Dict[str, Any]:
    """按经纬度和半径获取实时船舶数据（圆形区域）"""
    return ship_toolkit.get_circle_ships(lat, lon, radius)

def get_bridge_section_ships(section_name: str, minutes: int = 10) -> Dict[str, Any]:
    """按桥/口等截面名称获取实时船舶数据，可指定分钟数，默认10分钟"""
    return ship_toolkit.get_bridge_section_ships(section_name, minutes)

# 函数注册表
TOOL_REGISTRY: Dict[str, Callable[..., Dict[str, Any]]] = {
    "get_ais_data": get_ais_data,
    "get_ship_archive": get_ship_archive,
    "get_area_ships": get_area_ships,
    "get_section_data": get_section_data,
    "get_circle_ships": get_circle_ships,
    "get_bridge_section_ships": get_bridge_section_ships,
}

# LLM Function schema（仅原生模式用）- 通义千问格式
FUNCTION_SCHEMAS: List[Dict[str, Any]] = [
    {
        "type": "function",
        "function": {
            "name": "get_ais_data",
            "description": "获取船舶 AIS 实时数据，包括位置、航速等。支持通过mmsi(一串数字，9位左右)或ship_name查询。",
            "parameters": {
                "type": "object",
                "properties": {
                    "keyword": {
                        "type": "string",
                        "description": "船舶的 MMSI 或 船名"
                    }
                },
                "required": [],
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_ship_archive",
            "description": "获取船舶档案静态数据，如吨位、尺寸等。支持通过mmsi(一串数字，9位左右)或ship_name查询。",
            "parameters": {
                "type": "object",
                "properties": {
                    "keyword": {
                        "type": "string",
                        "description": "船舶的 MMSI 或 船名"
                    }
                },
                "required": [],
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_area_ships",
            "description": "根据经纬度串，查询该经纬串区域内船舶清单 (名称+MMSI)",
            "parameters": {
                "type": "object",
                "properties": {
                    "coordinates": {
                        "type": "string",
                        "description": "经纬度串，例如 '121.712 31.234, 122.212 31.834, 122.123 30.694'（注意：经纬度之间用逗号分隔，经纬度之间用空格分隔，至少3个点）"
                    }
                },
                "required": ["coordinates"],
            },
        }
    },
    {
        "type": "function", 
        "function": {
            "name": "get_section_data",
            "description": "查询长江流域某城市的船舶通过情况，可选过滤船舶类型",
            "parameters": {
                "type": "object",
                "properties": {
                    "section_name": {
                        "type": "string",
                        "description": "一般是城市名称，如 '上海、武汉、南京'"
                    },
                    "ship_type": {
                        "type": "string",
                        "description": "船舶类型，如'货船'，可为空"
                    }
                },
                "required": ["section_name"],
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_circle_ships",
            "description": "根据中心经纬度和半径（米）查询该圆形区域内船舶清单 (名称+MMSI)",
            "parameters": {
                "type": "object",
                "properties": {
                    "lat": {
                        "type": "number",
                        "description": "中心点纬度，如 31.234"
                    },
                    "lon": {
                        "type": "number",
                        "description": "中心点经度，如 121.712，注意经度大于纬度，经度范围-180到180，纬度范围-90到90",
                    },
                    "radius": {
                        "type": "number",
                        "description": "半径，单位米，注意半径不能超过100000米"
                    }
                },
                "required": ["lat", "lon", "radius"],
            },
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_bridge_section_ships",
            "description": "根据桥名或口名（截面名）查询该截面内船舶清单 (名称+MMSI)，可指定分钟数，默认10分钟",
            "parameters": {
                "type": "object",
                "properties": {
                    "section_name": {
                        "type": "string",
                        "description": "桥名或口名，如 '南京长江大桥'、'南通口'"
                    },
                    "minutes": {
                        "type": "integer",
                        "description": "查询多少分钟内经过该截面的船舶，默认10"
                    }
                },
                "required": ["section_name"],
            },
        }
    },
]

