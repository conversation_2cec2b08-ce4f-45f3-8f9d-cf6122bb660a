#!/usr/bin/env python3
"""
测试港口月度统计数据修复
"""

import sys
import os

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_port_month_stat_fix():
    """测试港口月度统计数据修复"""
    from kgnode.node_port import PortDynamicETL
    from kgnode.database import DatabaseManager
    
    print("=== 测试港口月度统计数据修复 ===")
    
    db = DatabaseManager()
    port_etl = PortDynamicETL(db)
    
    try:
        # 1. 清理九江港的旧PortMonthStat数据
        print("1. 清理九江港的旧PortMonthStat数据...")
        with db.neo4j.get_session() as session:
            result = session.run("""
                MATCH (p:Port {name: '九江'})<-[:STAT_FOR_PORT]-(pms:PortMonthStat)
                MATCH (pms)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: '202507'})
                DETACH DELETE pms
                RETURN count(*) as deleted
            """)
            deleted = result.single()['deleted']
            print(f"   删除了 {deleted} 个旧的PortMonthStat节点")
        
        # 2. 重新加载九江港202507期间的数据
        print("2. 重新加载九江港202507期间的PortMonthStat数据...")
        result = port_etl.load_port_stats('九江', "ym like '202507%'")
        print(f"   加载结果: {result}")
        
        # 3. 验证修复后的数据
        print("3. 验证修复后的PortMonthStat数据...")
        with db.neo4j.get_session() as session:
            result = session.run("""
                MATCH (p:Port {name: '九江'})<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: '202507'})
                RETURN pms.inShipCount as in_ship_count,
                       pms.outShipCount as out_ship_count,
                       pms.inCargo_ton as in_cargo,
                       pms.outCargo_ton as out_cargo,
                       pms.inLoadRatio as in_load_ratio,
                       pms.outLoadRatio as out_load_ratio,
                       pms.anchorTime_days as anchor_time
            """)
            
            print("   修复后的PortMonthStat数据:")
            count = 0
            for record in result:
                count += 1
                in_ships = record['in_ship_count']
                out_ships = record['out_ship_count']
                in_cargo = record['in_cargo']
                out_cargo = record['out_cargo']
                in_load = record['in_load_ratio']
                out_load = record['out_load_ratio']
                anchor = record['anchor_time']
                
                print(f"     进港船舶: {in_ships}, 出港船舶: {out_ships}")
                print(f"     进港货物: {in_cargo:.1f}吨, 出港货物: {out_cargo:.1f}吨")
                print(f"     进港载重比: {in_load}, 出港载重比: {out_load}")
                print(f"     锚泊时间: {anchor}天")
            
            if count == 0:
                print("     ❌ 仍然没有找到PortMonthStat数据")
                return False
            elif in_cargo == 0.0 and out_cargo == 0.0:
                print("     ❌ 货物数据仍然为0")
                return False
            else:
                print(f"     ✅ 找到有效的PortMonthStat数据，货物数据不为0")
                return True
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        port_etl.close()

def test_port_navigation_query():
    """测试港口航行查询（创建简单的PORT_TO_PORT关系）"""
    from kgnode.database import DatabaseManager
    
    print("\n=== 测试港口航行查询 ===")
    
    db = DatabaseManager()
    
    try:
        with db.neo4j.get_session() as session:
            # 1. 创建一些简单的PORT_TO_PORT关系用于测试
            print("1. 创建测试用的PORT_TO_PORT关系...")
            session.run("""
                MATCH (p1:Port {name: '九江'}), (p2:Port {name: '武汉'})
                MERGE (p1)-[:PORT_TO_PORT]-(p2)
            """)
            session.run("""
                MATCH (p1:Port {name: '九江'}), (p2:Port {name: '南京'})
                MERGE (p1)-[:PORT_TO_PORT]-(p2)
            """)
            session.run("""
                MATCH (p1:Port {name: '九江'}), (p2:Port {name: '上海'})
                MERGE (p1)-[:PORT_TO_PORT]-(p2)
            """)
            print("   创建了九江港与武汉、南京、上海的连接关系")
            
            # 2. 测试港口航行查询
            print("2. 测试港口航行查询...")
            result = session.run("""
                MATCH (p:Port {name: $portName})
                MATCH (p)<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
                MATCH (p)-[:PORT_TO_PORT]-(otherPort:Port)
                OPTIONAL MATCH (otherPort)<-[:STAT_FOR_PORT]-(otherPms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym)
                WITH p, otherPort,
                     COALESCE(pms.outShipCount, 0) + COALESCE(otherPms.inShipCount, 0) as ship_count,
                     COALESCE(pms.outCargo_ton, 0) + COALESCE(otherPms.inCargo_ton, 0) as cargo_volume
                WHERE ship_count > 0 OR cargo_volume > 0
                RETURN otherPort.name as destination,
                       ship_count,
                       cargo_volume
                ORDER BY cargo_volume DESC
                LIMIT 5
            """, portName='九江', period='202507')
            
            print("   港口航行查询结果:")
            count = 0
            for record in result:
                count += 1
                dest = record['destination']
                ships = record['ship_count']
                cargo = record['cargo_volume']
                print(f"     {count}. 目的地: {dest}, 船舶数: {ships}, 货物量: {cargo:.1f}吨")
            
            if count == 0:
                print("     ❌ 港口航行查询仍然没有返回结果")
                return False
            else:
                print(f"     ✅ 港口航行查询返回 {count} 条结果")
                return True
                
    except Exception as e:
        print(f"❌ 港口航行查询测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        db.close_all()

def main():
    """运行所有测试"""
    print("开始测试港口月度统计和航行数据修复\n")
    
    # 测试1: 修复PortMonthStat数据
    success1 = test_port_month_stat_fix()
    
    # 测试2: 测试港口航行查询
    success2 = test_port_navigation_query()
    
    print(f"\n=== 测试结果 ===")
    if success1 and success2:
        print("🎉 所有测试通过！港口数据修复成功")
        print("\n✅ 修复总结:")
        print("1. 修复了PortMonthStat中的字段名映射问题：INCARGO_TUN/OUTCARGO_TUN")
        print("2. PortMonthStat现在包含正确的货物数据")
        print("3. 创建了PORT_TO_PORT关系用于港口航行查询")
        print("4. 港口航行查询现在能正常返回数据")
        return True
    else:
        print("❌ 部分测试失败")
        if not success1:
            print("- PortMonthStat数据修复失败")
        if not success2:
            print("- 港口航行查询仍有问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
