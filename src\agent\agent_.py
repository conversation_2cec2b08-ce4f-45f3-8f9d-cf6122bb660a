from __future__ import annotations

import json
import os
from typing import Dict, Any, List, Optional, TypedDict, Callable, Annotated
from datetime import datetime

from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
from loguru import logger

from .tool_ import TOOL_REGISTRY, FUNCTION_SCHEMAS
from .llm import get_qwen_client
from .memory import memory_service


class Agent_State(TypedDict):
    """Agent状态定义"""
    messages: Annotated[List, add_messages]
    current_question: str
    tool_result: Optional[Dict[str, Any]]
    final_answer: str
    error_info: Optional[Dict[str, Any]]


class Agent_:
    """简洁高效的长江航运Agent"""

    def __init__(self):
        """初始化Agent"""
        self.logger = logger.bind(agent="Agent_")
        self.llm_client = get_qwen_client()
        self.memory = memory_service
        
        # 构建 LangGraph 工作流
        self.graph = self._build_graph()
        self.logger.info("Agent_初始化完成")

    def _build_graph(self) -> StateGraph:
        """构建简洁的工作流图"""
        sg = StateGraph(Agent_State)

        # 添加3个核心节点
        sg.add_node("decide_and_call", self._decide_and_call)
        sg.add_node("generate_answer", self._generate_answer)
        sg.add_node("handle_error", self._handle_error)

        # 设置流程
        sg.set_entry_point("decide_and_call")
        sg.add_edge("decide_and_call", "generate_answer")
        sg.add_edge("generate_answer", END)
        sg.add_edge("handle_error", END)

        return sg.compile()

    def _decide_and_call(self, state: Agent_State) -> Agent_State:
        """决策并调用工具 - 核心节点1"""
        question = state.get("current_question", "")
        messages = state.get("messages", [])
        
        try:
            self.logger.info(f"处理问题: {question[:50]}...")
            
            # 1. 相似性检索 - 增强上下文
            enhanced_messages = self._enhance_with_memory(messages, question)
            
            # 2. 调用LLM进行function calling
            tool_result = self._call_llm_with_tools(enhanced_messages)
            
            state["tool_result"] = tool_result
            self.logger.info(f"工具调用完成: {tool_result.get('status', 'unknown')}")
            
        except Exception as e:
            self.logger.error(f"决策调用失败: {e}")
            state["error_info"] = {
                "stage": "decide_and_call",
                "error": str(e),
                "message": "工具调用出现错误",
            }
        
        return state

    def _enhance_with_memory(self, messages: List, question: str) -> List:
        """使用记忆系统增强消息上下文"""
        try:
            # 查询相似对话
            similar_conversations = self.memory.query_similar_conversations(
                question=question,
                limit=2  # 限制数量保持简洁
            )
            
            if similar_conversations:
                self.logger.info(f"找到 {len(similar_conversations)} 个相似对话")
                
                # 构建上下文消息
                context_messages = []
                for conv in similar_conversations:
                    context_messages.append({
                        "role": "user", 
                        "content": f"历史问题: {conv.get('question', '')}"
                    })
                    context_messages.append({
                        "role": "assistant", 
                        "content": f"历史回答: {conv.get('answer', '')}"
                    })
                
                # 将上下文插入到当前对话前
                enhanced_messages = context_messages + messages
                self.logger.info("已增强消息上下文")
                return enhanced_messages
            
        except Exception as e:
            self.logger.warning(f"记忆检索失败: {e}")
        
        return messages

    def _call_llm_with_tools(self, messages: List) -> Dict[str, Any]:
        """调用LLM进行function calling"""
        try:
            # 添加system prompt
            from .prompt import get_system_prompt

            current_date = datetime.now().strftime("%Y-%m-%d")
            system_prompt = get_system_prompt(current_date)

            # 转换消息格式
            formatted_messages = [
                {"role": "system", "content": system_prompt}
            ]

            for msg in messages:
                if isinstance(msg, dict):
                    # 跳过已有的system消息，避免重复
                    if msg.get("role") != "system":
                        formatted_messages.append(msg)
                else:
                    # 处理LangChain消息对象
                    if hasattr(msg, 'content'):
                        role = "user" if isinstance(msg, HumanMessage) else "assistant"
                        formatted_messages.append({"role": role, "content": msg.content})

            # 调用LLM
            response = self.llm_client.chat(
                formatted_messages,
                tools=FUNCTION_SCHEMAS,
                temperature=0,
            )
            
            # 处理响应
            if isinstance(response, dict):
                # 检查是否是降级回复
                if response.get("downgraded") is True:
                    self.logger.info("检测到LLM降级回复")
                    return {
                        "status": "success",
                        "source": "llm_text",
                        "data": {"answer": response.get("content", ""), "downgraded": True}
                    }
                
                # 处理function call
                function_call = response.get("function_call")
                if function_call:
                    return self._execute_function_call(function_call)
            
            # 普通文本回复
            if isinstance(response, str):
                self.logger.info("LLM返回普通文本回复")
                return {
                    "status": "success",
                    "source": "llm_text",
                    "data": {"answer": response, "downgraded": True}
                }
            
            raise ValueError("LLM响应格式不正确")
            
        except Exception as e:
            self.logger.error(f"LLM调用失败: {e}")
            raise

    def _execute_function_call(self, function_call: Dict[str, Any]) -> Dict[str, Any]:
        """执行function call"""
        fn_name = function_call.get("name")
        args_json = function_call.get("arguments", "{}")
        
        try:
            args = json.loads(args_json) if isinstance(args_json, str) else args_json
            self.logger.info(f"执行工具调用: {fn_name}({args})")
            
            return self._call_tool_by_name(fn_name, args)
            
        except Exception as e:
            self.logger.error(f"工具执行失败: {fn_name} - {e}")
            return {
                "status": "error",
                "source": "tool_error",
                "data": {},
                "error": str(e)
            }

    def _call_tool_by_name(self, fn_name: str, args: Dict[str, Any]) -> Dict[str, Any]:
        """根据函数名称和参数调用工具 - 复用agent_x的逻辑"""
        if fn_name not in TOOL_REGISTRY:
            raise ValueError(f"未注册的函数: {fn_name}")

        try:
            # 统一参数解析 - 复用agent_x的逻辑
            if fn_name in ["get_ais_data", "get_ship_archive"]:
                keyword = (
                    args.get("keyword")
                    or args.get("ship_name")
                    or args.get("mmsi")
                )
                if not keyword:
                    raise ValueError(f"函数 {fn_name} 缺少必需参数 keyword/ship_name/mmsi")
                return TOOL_REGISTRY[fn_name](keyword)
            
            elif fn_name == "get_area_ships":
                coordinates = args.get("coordinates")
                if not coordinates:
                    raise ValueError(f"函数 {fn_name} 缺少必需参数 coordinates")
                return TOOL_REGISTRY[fn_name](coordinates)
            
            elif fn_name == "get_section_data":
                section_name = args.get("section_name")
                if not section_name:
                    raise ValueError(f"函数 {fn_name} 缺少必需参数 section_name")
                ship_type = args.get("ship_type")
                return TOOL_REGISTRY[fn_name](section_name, ship_type)
            
            elif fn_name == "get_bridge_section_ships":
                section_name = args.get("section_name")
                if not section_name:
                    raise ValueError(f"函数 {fn_name} 缺少必需参数 section_name")
                minutes = args.get("minutes", 10)
                return TOOL_REGISTRY[fn_name](section_name, minutes)
            
            elif fn_name == "get_circle_ships":
                lat = args.get("lat")
                lon = args.get("lon")
                radius = args.get("radius")
                if lat is None or lon is None or radius is None:
                    raise ValueError(f"函数 {fn_name} 缺少必需参数 lat/lon/radius")
                return TOOL_REGISTRY[fn_name](lat, lon, radius)

            # 新增的tool_.py中的函数 - 基于API设计文档v3.1
            elif fn_name == "query_shipping_data":
                query_type = args.get("query_type")
                entity = args.get("entity")
                if not query_type or not entity:
                    raise ValueError(f"函数 {fn_name} 缺少必需参数 query_type/entity")

                metric = args.get("metric")
                time_expression = args.get("time_expression")
                filters = args.get("filters")
                options = args.get("options")

                return TOOL_REGISTRY[fn_name](
                    query_type=query_type,
                    entity=entity,
                    metric=metric,
                    time_expression=time_expression,
                    filters=filters,
                    options=options
                )

            elif fn_name == "search_entities":
                entity_type = args.get("entity_type")
                search_term = args.get("search_term")
                if not entity_type or not search_term:
                    raise ValueError(f"函数 {fn_name} 缺少必需参数 entity_type/search_term")

                limit = args.get("limit", 10)
                return TOOL_REGISTRY[fn_name](entity_type, search_term, limit)

            elif fn_name == "get_metadata":
                metadata_type = args.get("metadata_type")
                if not metadata_type:
                    raise ValueError(f"函数 {fn_name} 缺少必需参数 metadata_type")
                return TOOL_REGISTRY[fn_name](metadata_type)

            elif fn_name == "get_realtime_data":
                entity_type = args.get("entity_type")
                entity_id = args.get("entity_id")
                if not entity_type or not entity_id:
                    raise ValueError(f"函数 {fn_name} 缺少必需参数 entity_type/entity_id")
                return TOOL_REGISTRY[fn_name](entity_type, entity_id)

            else:
                # 其他函数直接传递字典参数
                return TOOL_REGISTRY[fn_name](args)
                
        except Exception as e:
            self.logger.error(f"工具调用异常: {fn_name} - {e}")
            return {
                "status": "error",
                "source": "tool_execution",
                "data": {},
                "error": str(e)
            }

    def _generate_answer(self, state: Agent_State) -> Agent_State:
        """生成最终答案 - 核心节点2"""
        tool_result = state.get("tool_result", {})
        question = state.get("current_question", "")

        try:
            # 检查是否有错误
            if state.get("error_info"):
                return self._handle_error(state)

            if not tool_result:
                state["final_answer"] = "抱歉，未查询到任何数据。"
                return state

            # 处理降级回复
            if tool_result.get("source") == "llm_text":
                answer = tool_result.get("data", {}).get("answer", "")
                state["final_answer"] = answer if answer else "抱歉，未能获取到有效回复。"
                self.logger.info("使用LLM降级回复作为最终答案")
                self._store_conversation(question, state["final_answer"])
                return state

            # 处理工具调用错误
            if tool_result.get("status") == "error":
                error_msg = tool_result.get("error", "未知错误")
                state["final_answer"] = f"抱歉，查询过程中出现错误：{error_msg}"
                self._store_conversation(question, state["final_answer"])
                return state

            # 生成自然语言答案
            final_answer = self._generate_natural_answer(question, tool_result)
            state["final_answer"] = final_answer
            
            # 存储到记忆系统
            self._store_conversation(question, final_answer, tool_result)
            
            self.logger.info("答案生成完成")

        except Exception as e:
            self.logger.error(f"答案生成失败: {e}")
            state["final_answer"] = "抱歉，答案生成过程中出现错误。"

        return state

    def _generate_natural_answer(self, question: str, tool_result: Dict[str, Any]) -> str:
        """生成自然语言答案"""
        try:
            # 读取系统提示词
            with open("src/prompt/gen_0.6.txt", "r", encoding="utf-8") as f:
                system_prompt = f.read()
            
            user_prompt = (
                f"用户问题: {question}\n\n"
                f"JSON 数据如下:\n{json.dumps(tool_result, ensure_ascii=False, indent=2)}"
            )
            
            answer = self.llm_client.chat(
                [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                temperature=0,
            )
            
            return answer.strip() if answer else json.dumps(tool_result, ensure_ascii=False)
            
        except Exception as e:
            self.logger.error(f"生成自然语言答案失败: {e}")
            # 降级到JSON输出
            return json.dumps(tool_result, ensure_ascii=False)

    def _store_conversation(self, question: str, answer: str, tool_result: Dict[str, Any] = None):
        """存储对话到记忆系统"""
        try:
            # 提取实体信息（简化版本）
            entities = self._extract_entities_from_result(tool_result) if tool_result else {}
            
            # 构建元数据
            metadata = {
                "timestamp": datetime.now().isoformat(),
                "tool_used": tool_result.get("source") if tool_result else None,
                "status": tool_result.get("status") if tool_result else "success"
            }
            
            # 存储对话
            record_id = self.memory.store_conversation(
                user_question=question,
                agent_answer=answer,
                entities=entities,
                metadata=metadata
            )
            
            self.logger.info(f"对话已存储: {record_id}")
            
        except Exception as e:
            self.logger.warning(f"存储对话失败: {e}")

    def _extract_entities_from_result(self, tool_result: Dict[str, Any]) -> Dict[str, Any]:
        """从工具结果中提取实体信息"""
        entities = {}
        
        try:
            query_params = tool_result.get("query_params", {})
            
            # 提取船舶相关实体
            if "keyword" in query_params:
                keyword = query_params["keyword"]
                if keyword.isdigit():
                    entities["mmsi"] = [keyword]
                else:
                    entities["ships"] = [keyword]
            
            # 提取位置相关实体
            if "coordinates" in query_params:
                entities["coordinates"] = [query_params["coordinates"]]
            
            if "section_name" in query_params:
                entities["sections"] = [query_params["section_name"]]
            
            if "lat" in query_params and "lon" in query_params:
                entities["location"] = [f"{query_params['lat']},{query_params['lon']}"]
                
        except Exception as e:
            self.logger.warning(f"实体提取失败: {e}")
        
        return entities

    def _handle_error(self, state: Agent_State) -> Agent_State:
        """错误处理 - 核心节点3"""
        error_info = state.get("error_info", {})
        error_message = error_info.get("message", "系统出现未知错误")
        
        state["final_answer"] = f"抱歉，{error_message}。"
        
        # 记录错误对话
        question = state.get("current_question", "")
        if question:
            self._store_conversation(question, state["final_answer"])
        
        self.logger.error(f"处理错误: {error_info}")
        return state

    def answer(self, question: str) -> str:
        """处理单个用户问题，返回答案字符串"""
        initial_state: Agent_State = {
            "messages": [HumanMessage(content=question)],
            "current_question": question,
            "tool_result": None,
            "final_answer": "",
            "error_info": None,
        }
        
        try:
            final_state = self.graph.invoke(initial_state)
            return final_state["final_answer"]
        except Exception as e:
            self.logger.error(f"问答处理失败: {e}")
            return f"抱歉，系统处理问题时出现错误：{str(e)}"

    def chat(self, messages: List[Dict[str, str]]) -> str:
        """多轮对话接口"""
        if not messages:
            return "请提供问题。"
        
        # 获取最新问题
        latest_message = messages[-1]
        if latest_message.get("role") != "user":
            return "请提供用户问题。"
        
        question = latest_message.get("content", "")
        
        # 构建消息历史
        message_objects = []
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            if role == "user":
                message_objects.append(HumanMessage(content=content))
            else:
                message_objects.append(AIMessage(content=content))
        
        initial_state: Agent_State = {
            "messages": message_objects,
            "current_question": question,
            "tool_result": None,
            "final_answer": "",
            "error_info": None,
        }
        
        try:
            final_state = self.graph.invoke(initial_state)
            return final_state["final_answer"]
        except Exception as e:
            self.logger.error(f"多轮对话处理失败: {e}")
            return f"抱歉，系统处理对话时出现错误：{str(e)}"


# 创建全局实例
agent_ = Agent_()


if __name__ == "__main__":
    # 测试单轮对话
    print("=== 单轮对话测试 ===")
    q1 = "413784786现在在哪里？"
    print(f"Q: {q1}")
    print(f"A: {agent_.answer(q1)}")
    
    print("\n=== 多轮对话测试 ===")
    # 测试多轮对话
    messages = [
        {"role": "user", "content": "413784786现在在哪里？"},
        {"role": "assistant", "content": "根据最新AIS数据..."},
        {"role": "user", "content": "这艘船的档案信息呢？"}
    ]
    print(f"多轮对话: {messages[-1]['content']}")
    print(f"A: {agent_.chat(messages)}") 