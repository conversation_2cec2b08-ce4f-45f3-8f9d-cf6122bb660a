[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "ch-agent"
version = "1.0.0"
description = "长江航运智能指标问答Agent (CJHY-KGAgent) - 基于Neo4j知识图谱和LangGraph的智能问答系统"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "CJHY Development Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "CJHY Development Team", email = "<EMAIL>"}
]
keywords = ["agent", "neo4j", "langgraph", "qa", "shipping", "knowledge-graph"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn>=0.24.0",
    "langchain>=0.1.0",
    "langchain-core>=0.1.0",
    "langgraph>=0.0.26",
    "langserve>=0.0.30",
    "loguru>=0.7.2",
    "requests>=2.31.0",
    "python-dotenv>=1.0.0",
    "pydantic>=2.5.0",
    "neo4j>=5.15.0",
    "apscheduler>=3.10.4",
    "pandas>=2.1.4",
    "numpy>=1.24.3",
    "sentence-transformers>=2.2.2",
    "faiss-cpu>=1.7.4",
    "torch>=2.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
]
jupyter = [
    "jupyter>=1.0.0",
    "ipython>=8.14.0",
]
all = [
    "ch-agent[dev,jupyter]",
]

[project.scripts]
ch-agent = "main:main"
ch-agent-web = "streamlit_app:main"
ch-agent-api = "run_api:main"
ch-agent-sync = "data_sync:main"

[project.urls]
Homepage = "https://github.com/cjhy/ch-agent"
Documentation = "https://github.com/cjhy/ch-agent/blob/main/README.md"
Repository = "https://github.com/cjhy/ch-agent.git"
Issues = "https://github.com/cjhy/ch-agent/issues"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/doc",
    "/README.md",
    "/pyproject.toml",
]

# 代码格式化配置
[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "neo4j.*",
    "mem0.*",
    "langchain.*",
    "langgraph.*",
    "streamlit.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests.py"]
python_files = ["test_*.py", "*_test.py", "tests.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
