好的，这是根据我们之前的分析和讨论，为您生成的完整、修正后的《长江航运智能分析Agent - API设计文档》版本 V2.1。

该版本解决了API与数据库之间的不一致性，新增了关键的航线分析功能，丰富了实时数据接口，并为后端实现提供了更清晰的指导。

---

## **长江航运智能分析Agent - API设计文档**

**版本:** 2.1
**日期:** 2025年7月26日

### **1. 概述与设计哲学**

#### **1.1. 目标**
本文档定义了一套完整的、面向LLM（大语言模型）的API接口，旨在为“长江航运智能分析Agent”提供驱动能力。Agent将通过调用这些API，响应领导层关于长江航运的各类数据查询与分析需求，覆盖从单点信息查询到复杂的趋势、异常及预测分析。

#### **1.2. 设计原则**
本API套件严格遵循以下五大核心原则：

1.  **业务语义化 (Business-Semantic):** API的设计聚焦于业务问题，而非技术实现。LLM与业务术语（如“吞吐量”、“周转率”）交互，所有到数据库字段（`inCargo_ton`）的映射均在API后端完成。
2.  **LLM友好 (LLM-Friendly):** API的描述清晰、无歧义，参数设计简洁。接受自然语言输入（如时间范围），将解析复杂性的工作留给后端，降低LLM的使用门槛。
3.  **高内聚、低耦合 (High Cohesion, Low Coupling):** 功能相似的API被组织在一起。每个API专注于完成一个正交的、定义明确的任务，便于LLM理解、组合和调用。
4.  **结果可重用与可追溯 (Reusable & Traceable Results):** API返回结构化的JSON，并包含唯一的实体ID。这使得Agent能够轻松地在多轮对话中进行下钻分析，将上一步的输出作为下一步的输入。
5.  **安全、稳定、可控 (Secure, Stable, Controllable):** API层是Agent与数据库之间的“安全隔离区”。所有数据库查询都由后端预定义、优化和控制，杜绝了LLM直接操作数据库带来的性能和安全风险。

---

### **2. API套件结构**

为便于管理和未来扩展（如两阶段路由），API被划分为以下四个逻辑分组：

*   **船舶APIs (Ship APIs):** 负责所有与单一船舶相关的查询。
*   **地理实体APIs (Geo-Entity APIs):** 负责所有与地理位置（港口、省份、流域）相关的查询。
*   **通用分析APIs (General-Analysis APIs):** 负责执行跨实体的、更高级的分析任务。
*   **元数据APIs (Metadata APIs):** 提供辅助信息，增强Agent的交互能力。

---

### **3. API端点规格详述**

#### **第一组：船舶 (Ship) APIs**

**1.1. `search_ships`**
*   **LLM函数描述:** "根据船名或MMSI模糊搜索船舶，返回一个包含匹配船舶及其ID的列表。当不确定船舶全名时，优先调用此函数进行查找。"
*   **功能:** 作为船舶查询的入口，解决输入不精确的问题。
*   **请求:** `POST /api/v1/ships/search`
    ```json
    {
      "search_term": "汉海"
    }
    ```
*   **响应:**
    ```json
    {
      "ships": [
        {"name": "汉海5号", "mmsi": "413256960", "shipId": "CN2018..."},
        {"name": "汉海6号", "mmsi": "413256961", "shipId": "CN2019..."}
      ]
    }
    ```
*   **设计依据:** 将用户的不确定性转化为一个确定的选项列表。返回`mmsi`和`shipId`为后续所有需要唯一标识符的API调用铺平道路。

**1.2. `get_ship_profile`**
*   **LLM函数描述:** "获取指定船舶的详细档案信息，例如船东、所属公司、船舶类型、参考载重吨(dwt)、总吨位以及建造日期等静态信息。"
*   **功能:** 查询`Ship`节点的静态属性。
*   **请求:** `GET /api/v1/ships/{ship_id}/profile`
*   **响应:**
    ```json
    {
      "mmsi": "413256960",
      "name": "汉海5号",
      "owner": "A航运公司",
      "dwt": 16338.00,
      "builtDate": "2020-12-23"
    }
    ```
*   **设计依据:** 严格分离静态档案和动态统计数据，符合单一职责原则。

**1.3. `get_ship_monthly_stats`**
*   **LLM函数描述:** "查询指定船舶在特定月份的聚合运营统计数据，包括有效营运率、航次数、周转量、货运量、在港天数等。"
*   **功能:** 提供单一船舶的月度运营快照（PointQuery）。
*   **请求:** `GET /api/v1/ships/{ship_id}/stats/monthly?time_range=上个月`
*   **响应:**
    ```json
    {
      "shipId": "CN2018...",
      "time_period": "202506",
      "opRatio": 0.92,
      "voyages": 10,
      "turnover_tonkm": 500000,
      "capacity_ton": 15000
    }
    ```
*   **设计依据:** `time_range`参数接受自然语言，后端负责解析，对LLM友好。后端查询`ShipMonthStat`节点。

**1.4. `get_ship_monthly_composition`**
*   **LLM函数描述:** "查询指定船舶在特定月份运输的各类货物的构成列表，包含具体货运量和占比。"
*   **功能:** 分析单一船舶的货运构成（Composition）。
*   **请求:** `GET /api/v1/ships/{ship_id}/composition/monthly?time_range=2024年5月`
*   **响应:**
    ```json
    {
      "shipId": "CN2018...",
      "time_period": "202405",
      "composition": [
        {"cargo_type": "煤炭及制品", "cargo_ton": 5000, "percentage": 0.33},
        {"cargo_type": "金属矿石", "cargo_ton": 3000, "percentage": 0.20}
      ]
    }
    ```*   **设计依据:**
    *   专用于构成分析，减轻LLM负担。后端查询`ShipMonthCargoStat`。
    *   **[说明]** `percentage` 字段由后端动态计算。计算公式为：`(本条目的 cargo_ton / 该船该月总货运量) * 100`。总货运量从 `ShipMonthStat.capacity_ton` 获取。

**1.5. `get_ship_monthly_line_stats` [新增]**
*   **LLM函数描述:** "查询指定船舶在特定月份的所有航线及其统计数据，包括O/D港口、货运量和周转量。用于分析船舶的具体航线表现。"
*   **功能:** 提供单一船舶的月度航线明细分析。
*   **请求:** `GET /api/v1/ships/{ship_id}/lines/monthly?time_range=上个月`
*   **响应:**
    ```json
    {
      "shipId": "CN2018...",
      "time_period": "202506",
      "line_stats": [
        {"portO": "武汉", "portD": "南京", "cargo_ton": 5000, "turnover_tonkm": 2500000, "mileage_km": 500},
        {"portO": "武汉", "portD": "上海", "cargo_ton": 4500, "turnover_tonkm": 4950000, "mileage_km": 1100}
      ]
    }
    ```
*   **设计依据:**
    *   **新增此API以暴露数据库中 `ShipMonthLineStat` 节点的宝贵数据**，弥补了原API设计在航线分析维度的空白。
    *   使Agent能够回答“某条船上个月跑了哪些地方？”、“哪条航线运货最多？”等具体问题。

**1.6. `get_ship_realtime_status` [修正]**
*   **LLM函数描述:** "查询指定船舶最新的实时动态，包括AIS位置(经纬度)、航速(sog)、航向(cog)以及最新的报港业务状态，如所在港口、进出港申报时间等。"
*   **功能:** 提供高时效性的动态信息。
*   **请求:** `GET /api/v1/ships/{ship_id}/realtime`
*   **响应:**
    ```json
    {
      "mmsi": "413256960",
      "ais": {
        "lat": 30.56,
        "lon": 114.30,
        "sog": 10.2,
        "cog": 95.5,
        "navStatus": "在航(主机推动)",
        "timestamp": "2025-07-26T10:30:00Z"
      },
      "port_report": {
        "portStatus": "在途",
        "reportedPort": "武汉港",
        "reportTimeIn": "2025-07-25T08:00:00Z",
        "reportTimeOut": null,
        "actualCarryCapacityIn_ton": 8000
      },
      "lastUpdated": "2025-07-26T10:31:00Z"
    }
    ```
*   **设计依据:**
    *   响应结构调整为 `ais` 和 `port_report` 两个子对象，以清晰地区分数据来源（AIS物理信号 vs. 业务申报系统）。
    *   增加了数据库 `ShipRealtime` 节点中更多有价值的业务字段，如 `reportedPort`, `reportTimeIn`, `actualCarryCapacityIn_ton`，增强了Agent的实时洞察能力。

---

#### **第二组：地理实体 (Geo-Entity) APIs**

**2.1. `get_geographical_stats`**
*   **LLM函数描述:** "查询某个地理实体（如'武汉港', '湖北省', '长江'）在特定时间（如月度）的整体运营统计数据，例如进出港艘次、总货运量等。"
*   **功能:** 提供地理维度的运营快照（PointQuery）。
*   **请求:** `GET /api/v1/geo/stats?entity_type=Port&entity_name=武汉&time_range=2024年Q2`
*   **响应:**
    ```json
    {
      "entity_type": "Port", "entity_name": "武汉",
      "time_period": "2024Q2",
      "inShipCount": 18000, "outShipCount": 18100,
      "totalThroughput_ton": 28000000
    }
    ```
*   **设计依据:**
    *   通过`entity_type`参数将对港口、省份、流域的同类查询统一到一个API，减少API数量，提高内聚性。
    *   **[说明]** 当 `entity_type` 为 `Port` 或 `Province` 时，响应中的 `totalThroughput_ton` 字段由后端通过将 `inCargo_ton` 和 `outCargo_ton` 相加动态计算得出。当 `entity_type` 为 `Basin` 时，则直接取自 `BasinMonthStat.totalThroughput_ton` 字段。

**2.2. `get_geographical_composition`**
*   **LLM函数描述:** "查询某个地理实体（如'武汉港'）在特定时间内，按货物类别分类的详细运量构成及占比。"
*   **功能:** 分析地理实体的业务构成（Composition）。
*   **请求:** `GET /api/v1/geo/composition?entity_type=Port&entity_name=武汉&time_range=上个月`
*   **响应:**
    ```json
    {
      "entity_name": "武汉", "time_period": "202506",
      "composition": [
        {"cargo_type": "煤炭及制品", "inCargo_ton": 59576, "outCargo_ton": 120000, "total_ton": 179576, "percentage": 0.18},
        {"cargo_type": "金属矿石", "inCargo_ton": 80000, "outCargo_ton": 100000, "total_ton": 180000, "percentage": 0.19}
      ]
    }
    ```
*   **设计依据:**
    *   统一的地理实体查询入口，专用于构成分析。
    *   **[说明]** 响应中的 `total_ton` 和 `percentage` 字段由后端动态计算。`total_ton` = `inCargo_ton` + `outCargo_ton`。`percentage` 的分母为该地理实体当月的总吞吐量。

---

#### **第三组：通用分析 (General-Analysis) APIs**

**3.1. `get_metric_trend`**
*   **LLM函数描述:** "获取指定实体（如'汉海5号', '武汉港', '长江'）的某一特定指标（如'货运量', '有效营运率'）在一段时间内的趋势数据。用于分析变化趋势。"
*   **功能:** 覆盖**趋势分析(Trend)**场景。
*   **请求:** `POST /api/v1/analysis/trend`
    ```json
    {
      "entity_type": "Port",
      "entity_name": "武汉",
      "metric": "总吞吐量",
      "time_range": "过去6个月"
    }
    ```
*   **响应:**
    ```json
    {
      "entity_name": "武汉", "metric": "总吞吐量",
      "trend": [
        {"time_period": "202501", "value": 9800000},
        {"time_period": "202502", "value": 9500000},
        {"time_period": "202506", "value": 10200000}
      ]
    }
    ```
*   **设计依据:**
    *   将趋势分析的逻辑封装，返回可以直接用于绘图或解读的时间序列JSON。
    *   **[说明]** 后端需要维护一个**业务指标字典**，将 `metric` 参数（如 "总吞吐量"）映射到数据库的具体字段（如 `BasinMonthStat.totalThroughput_ton`）或计算逻辑（如 `SUM(PortMonthStat.inCargo_ton, PortMonthStat.outCargo_ton)`）。

**3.2. `compare_entities_by_metric`**
*   **LLM函数描述:** "在同一时间、同一指标下，对比两个或多个实体（如'武汉港'和'南京港'）的表现。"
*   **功能:** 覆盖**对比分析(Compare)**场景。
*   **请求:** `POST /api/v1/analysis/compare`
    ```json
    {
      "entity_type": "Port",
      "entity_names": ["武汉", "南京"],
      "metric": "进港艘次",
      "time_range": "上个月"
    }
    ```
*   **响应:**
    ```json
    {
      "metric": "进港艘次", "time_period": "202506",
      "comparison": [
        {"entity_name": "武汉", "value": 6192},
        {"entity_name": "南京", "value": 7200}
      ]
    }
    ```
*   **设计依据:**
    *   接口清晰地反映了“对比”这一业务动作，易于LLM理解和调用。
    *   **[说明]** `metric` 参数的解析同样依赖于业务指标字典。

**3.3. `rank_entities_by_metric`**
*   **LLM函数描述:** "按指定的运营指标对一类实体（如'港口'或'船舶'）进行排名。可以按特定货类进行筛选，例如'查询上个月煤炭运量最高的港口'。"
*   **功能:** 覆盖**排名分析(Ranking)**场景。
*   **请求:** `POST /api/v1/analysis/rank`
    ```json
    {
      "entity_type": "Port",
      "time_range": "上个月",
      "metric": "出港货运量",
      "top_n": 5,
      "cargo_type_filter": "煤炭及制品"
    }
    ```
*   **响应:**
    ```json
    {
      "ranking": [
        {"rank": 1, "entity_name": "南京港", "value": 500000},
        {"rank": 2, "entity_name": "镇江港", "value": 450000}
      ]
    }
    ```
*   **设计依据:**
    *   将所有“哪个最XX”的问题统一到一个接口。
    *   **[说明]** `metric` 参数的解析同样依赖于业务指标字典。

**3.4. `detect_anomalies` (高级扩展)**
*   **LLM函数描述:** "根据预定义的分析模型检测系统中的异常点。例如，查找'运营效率过低的船舶'或'吞吐量异常波动的港口'。"
*   **功能:** 覆盖**异常检测(Anomaly)**场景。
*   **请求:** `POST /api/v1/analysis/anomalies`
    ```json
    {
      "analysis_model": "ship_op_ratio_anomaly",
      "time_range": "上个季度",
      "threshold_sigma": 3.0
    }
    ```
*   **响应:**
    ```json
    {
      "anomalies": [
        {"entity_name": "“希望号”", "metric": "有效营运率", "value": 0.2, "average": 0.85, "description": "该船有效营运率显著低于同类型船舶均值(低于3倍标准差)。"}
      ]
    }
    ```*   **设计依据:** 将复杂的统计学或机器学习逻辑封装成一个简单的API调用，LLM只需知道要调用哪个“模型”，而无需关心实现细节。

**3.5. `get_metric_forecast` (高级扩展)**
*   **LLM函数描述:** "基于历史数据，对某一指标的未来走势进行预测。例如'预测长江未来半年的总货运量'。"
*   **功能:** 覆盖**预测(Forecast)**场景。
*   **请求:** `POST /api/v1/analysis/forecast`
    ```json
    {
      "entity_type": "Basin",
      "entity_name": "长江",
      "metric": "总吞吐量",
      "forecast_horizon": "未来6个月"
    }
    ```
*   **响应:**
    ```json
    {
      "forecast": [
        {"time_period": "202507", "predicted_value": 1.2e8, "confidence_upper": 1.3e8, "confidence_lower": 1.1e8}
      ]
    }
    ```
*   **设计依据:** 将专业的时序预测模型服务化，使Agent具备前瞻性洞察能力。返回置信区间以体现预测的不确定性。

---

#### **第四组：元数据 (Metadata) APIs**

**4.1. `list_available_options`**
*   **LLM函数描述:** "获取某个类别下的可用筛选选项列表，例如获取所有'货物类别'或所有'省份'的列表。"
*   **功能:** 增强Agent的交互性和引导能力。
*   **请求:** `GET /api/v1/metadata/options?type=CargoType`
*   **响应:**
    ```json
    {
      "options": ["煤炭及制品", "金属矿石", "集装箱", "石油、天然气及制品"]
    }
    ```
*   **设计依据:** 当用户意图模糊时（“查查货运情况”），Agent可调用此API获取所有货类，然后向用户提供清晰的选项，变被动为主动。