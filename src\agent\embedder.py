"""
文本嵌入器组件
使用 bge-small-zh-v1.5 模型进行文本向量化
"""

import numpy as np
from typing import List, Union, Optional
import torch
from loguru import logger
from pathlib import Path
import os

try:
    from sentence_transformers import SentenceTransformer
except ImportError:
    logger.error("sentence-transformers not installed. Run: pip install sentence-transformers")
    raise


class BGEEmbedder:
    """BGE Small 中文嵌入器"""
    
    def __init__(self, model_name: str = None, device: Optional[str] = None):
        """
        初始化嵌入器
        
        Args:
            model_name: 模型名称或路径，None表示自动选择本地模型
            device: 设备，None表示自动选择
        """
        # 优先使用本地模型路径
        if model_name is None:
            model_name = self._get_local_model_path()
        
        self.model_name = model_name
        self.device = device or ("cuda" if torch.cuda.is_available() else "cpu")
        self.model = None
        self.embedding_dim = 512  # bge-small-zh-v1.5 的向量维度
        
        self._load_model()
    
    def _get_local_model_path(self) -> str:
        """获取本地模型路径，如果不存在则使用在线路径"""
        # 常见的本地模型路径
        possible_paths = [
            "./models/bge-small-zh-v1.5"
        ]
        
        for path in possible_paths:
            expanded_path = os.path.expanduser(path)
            if os.path.exists(expanded_path):
                logger.info(f"Found local BGE model at: {expanded_path}")
                return expanded_path
        
        return "BAAI/bge-small-zh-v1.5"
    
    def _load_model(self):
        """加载模型"""
        try:
            logger.info(f"Loading embedding model: {self.model_name} on {self.device}")
            
            # 设置离线模式环境变量（如果使用本地路径）
            if os.path.exists(os.path.expanduser(self.model_name)):
                os.environ["TRANSFORMERS_OFFLINE"] = "1"
                os.environ["HF_HUB_OFFLINE"] = "1"
            
            self.model = SentenceTransformer(self.model_name, device=self.device)
            
            # 获取实际的嵌入维度
            test_embedding = self.model.encode("test", convert_to_numpy=True)
            self.embedding_dim = test_embedding.shape[0]
            logger.info(f"Embedding model loaded successfully, dimension: {self.embedding_dim}")
            
        except Exception as e:
            logger.error(f"Failed to load embedding model: {e}")
            logger.error("Please ensure BGE model is available locally or internet connection is stable")
            raise
    
    def embed(self, texts: Union[str, List[str]], normalize: bool = True) -> np.ndarray:
        """
        对文本进行嵌入
        
        Args:
            texts: 单个文本或文本列表
            normalize: 是否对向量进行L2归一化
            
        Returns:
            嵌入向量，shape为 (1, dim) 或 (n, dim)
        """
        if self.model is None:
            raise RuntimeError("Model not loaded")
        
        # 确保输入是列表
        if isinstance(texts, str):
            texts = [texts]
            single_text = True
        else:
            single_text = False
        
        try:
            # 生成嵌入
            embeddings = self.model.encode(
                texts,
                convert_to_numpy=True,
                normalize_embeddings=normalize,
                batch_size=32,
                show_progress_bar=False
            )
            
            # 确保返回正确的形状
            if single_text:
                return embeddings.reshape(1, -1)
            else:
                return embeddings
                
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {e}")
            raise
    
    def embed_single(self, text: str, normalize: bool = True) -> np.ndarray:
        """
        对单个文本进行嵌入
        
        Args:
            text: 输入文本
            normalize: 是否归一化
            
        Returns:
            一维嵌入向量
        """
        embedding = self.embed(text, normalize=normalize)
        return embedding.flatten()
    
    def similarity(self, text1: str, text2: str) -> float:
        """
        计算两个文本的相似度
        
        Args:
            text1: 文本1
            text2: 文本2
            
        Returns:
            余弦相似度 (0-1)
        """
        embeddings = self.embed([text1, text2], normalize=True)
        similarity = np.dot(embeddings[0], embeddings[1])
        return float(similarity)
    
    def get_embedding_dim(self) -> int:
        """获取嵌入维度"""
        return self.embedding_dim
    
    def is_available(self) -> bool:
        """检查模型是否可用"""
        return self.model is not None


# 全局嵌入器实例
_embedder_instance: Optional[BGEEmbedder] = None


def get_embedder() -> BGEEmbedder:
    """获取全局嵌入器实例"""
    global _embedder_instance
    if _embedder_instance is None:
        _embedder_instance = BGEEmbedder()
    return _embedder_instance


if __name__ == "__main__":
    # 测试嵌入器
    embedder = BGEEmbedder()
    
    # 测试单个文本
    text = "长江航运智能问答系统"
    embedding = embedder.embed_single(text)
    print(f"Text: {text}")
    print(f"Embedding shape: {embedding.shape}")
    print(f"Embedding dim: {embedder.get_embedding_dim()}")
    
    # 测试相似度
    text1 = "九江港的吞吐量是多少？"
    text2 = "九江港口的货物吞吐量情况如何？"
    similarity = embedder.similarity(text1, text2)
    print(f"Similarity between '{text1}' and '{text2}': {similarity:.4f}") 