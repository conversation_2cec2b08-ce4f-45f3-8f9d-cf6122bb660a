## Neo4j 设计文档: 长江航运 - 综合统计分析平台

**版本:** 4.0 (Final Comprehensive Design)
**日期:** 2023年10月27日

**1. 概述**

本文档详细阐述了一个用于存储和分析长江航运数据的 Neo4j 图数据库模式。该模型专注于个体船舶档案、船舶类型、货物分类，以及在不同地理层级（港口、省份、流域）和时间粒度（月度、年度）上的预聚合运营统计数据。设计强调字段含义的清晰性、与源数据的明确映射、多维度分析的灵活性、查询效率以及便捷的程序化数据入库。

**2. 数据模型**

模型包含以下主要节点标签：`Ship`, `ShipCategory`, `ShipType`, `Year`, `YearMonth`, `CargoCategory`, `CargoType`, `Port`, `Province`, `Basin`, `ShippingRoute`, `ShipMonthStat`, `ShipMonthCargoStat`, `ShipMonthLineStat`, `RouteMonthStat`, `RouteMonthCargoStat`, `ShipYearStat`, `PortMonthStat`, `PortMonthCargoStat`, `ProvinceMonthStat`, `ProvinceMonthCargoStat`, `BasinMonthStat`, `BasinMonthCargoStat`, `BasinYearStat`, `BasinYearCargoStat`, 以及可选的 `BasinYearShipTypeStat`。
t
**2.1. 节点标签和属性**

**源数据字段名前缀约定 (用于属性描述中的映射关系):**
*   `SRC_SHIP_SUMMARY.` 指代 "船舶汇总样例数据"中的字段。
*   `SRC_PORT_MONTHLY.` 指代 "港口月度统计样例数据"中的字段。
*   `SRC_PORT_CARGO.` 指代 "港口分货类月度统计样例数据"中的字段。
*   `SRC_CARGO_DEF.` 指代 "货物类别定义样例数据"中的字段。

---

**2.1.1. `ShipCategory` (船舶大类)**
*   **Description:** 代表船舶的顶层分类（例如，"集装箱"，"散货"）。基于源数据中的 `SHIP_TYPE_NAME`。
*   **Properties:**
    *   `name: String` (Primary Key - from `SRC_SHIP_SUMMARY.SHIP_TYPE_NAME`, e.g., "集装箱")
*   **Example:** `(:ShipCategory {name: "集装箱"})`

---

**2.1.2. `ShipType` (船舶类型)**
*   **Description:** 代表船舶的具体运营分类（例如，"集装箱船"，"散货船"）。基于源数据中的 `SHIP_SUB_TYPE_NAME`，隶属于更广泛的 `ShipCategory` 分类。
*   **Properties:**
    *   `subCode: String` (Key Candidate - from `SRC_SHIP_SUMMARY.SHIP_SUB_TYPE_CODE`, e.g., "0205")
    *   `subName: String` (Key Candidate - from `SRC_SHIP_SUMMARY.SHIP_SUB_TYPE_NAME`, e.g., "集装箱船")
    *   `code: String` (Optional - from `SRC_SHIP_SUMMARY.SHIP_TYPE_CODE`, e.g., "02")
    *   `name: String` (Optional - from `SRC_SHIP_SUMMARY.SHIP_TYPE_NAME`, e.g., "集装箱")
*   **Example:** `(:ShipType {subCode: "0205", subName: "集装箱船", name: "集装箱"})`

---

**2.1.3. `Ship` (船舶档案)**
*   **Description:** 代表具有其静态/档案信息的个体船舶。
*   **Properties (mapped from `SRC_SHIP_SUMMARY.` - 船舶档案信息):**
    *   `mmsi: String` (Primary Key - from `MMSI`, e.g., "413256960")
    *   `shipId: String` (Alternate Key - from `SHIP_ID`, e.g., "CN20180533386")
    *   `shipNo: String` (from `SHIP_NO`)
    *   `name: String` (from `SHIP_NAME_CN`, e.g., "汉海5号")
    *   `firstRegNo: String` (from `SHIP_FIRSTREG_NO`)
    *   `inspectNo: String` (from `SHIP_INSPECT_NO`)
    *   `regPortCode: String` (from `REGPORT_CODE`, e.g., "3303")
    *   `regPortName: String` (from `REGPORT_NAME`, e.g., "武汉")
    *   `regPortProvince: String` (from `REGPORT_PROV`, e.g., "湖北省")
    *   `owner: String` (from `SHIP_OWNER`)
    *   `operator: String` (from `SHIP_OPERATOR`)
    *   `navAreaType: String` (from `SHIP_REGION_TYPE`, e.g., "1" for 海船, "0" for 内河)
    *   `dwt: Float` (from `SHIP_DWT`, e.g., 16338.00, 参考载重吨)
    *   `grossTon: Float` (from `SHIP_GROSSTON`, e.g., 16218.00, 总吨位)
    *   `orgCode: String` (from `SHIP_ORG_CODE`)
    *   `builtDate: Date` (from `SHIP_BUILT_DATE`, e.g., `date("2020-12-23")`, 建成时间)
    *   `detailInfo: String` (from `DETAIL_SHIP_INFO`, 船舶大小船型信息)
    *   `regNo: String` (from `SHIP_REG_NO`, 船舶登记号)
    *   `lastProfileUpdate: Datetime` (Timestamp of the last update to this node's profile information during data load)
*   **Example:** `(:Ship {mmsi: "413256960", name: "汉海5号", dwt: 16338.00, builtDate: date("2020-12-23")})`

---

**2.1.4. `Year` (年份)**
*   **Description:** 代表一个日历年份。
*   **Properties:**
    *   `year: Integer` (Primary Key - e.g., 2024)
*   **Example:** `(:Year {year: 2024})`

---

**2.1.5. `YearMonth` (年月)**
*   **Description:** 代表特定年份中的特定月份。
*   **Properties:**
    *   `ym: String` (Primary Key - from `SRC_SHIP_SUMMARY.YM`, `SRC_PORT_MONTHLY.YM`, `SRC_PORT_CARGO.YM`, format YYYYMM, e.g., "202401")
    *   `year: Integer` (Derived from `ym`, e.g., 2024 - denormalized for direct filtering)
    *   `month: Integer` (Derived from `ym`, e.g., 1 - denormalized for direct filtering)
    *   `daysInMonth: Integer` (from `SRC_SHIP_SUMMARY.DAY_COUNT`, e.g., 31)
*   **Example:** `(:YearMonth {ym: "202401", year: 2024, month: 1, daysInMonth: 31})`

---

**2.1.6. `CargoCategory` (货物大类)**
*   **Description:** 货物的顶层分类。
*   **Properties:**
    *   `name: String` (from `SRC_CARGO_DEF.TYPE_NAME`, e.g., "干散货类")
*   **Example:** `(:CargoCategory { name: "干散货类"})`

---

**2.1.7. `CargoType` (货物类别)**
*   **Description:** 具体的货物类型或品名。
*   **Properties:**
    *   `subCode: String` (Primary Key - from `SRC_CARGO_DEF.TYPE_SUB_CODE`, e.g., "0100")
    *   `subName: String` (from `SRC_CARGO_DEF.TYPE_SUB_NAME`, e.g., "煤炭及制品")
    *   `name: String` (from `SRC_CARGO_DEF.TYPE_NAME`, e.g., "煤炭及制品")
*   **Example:** `(:CargoType {subCode: "0100", subName: "煤炭及制品",name:"干散货类"})`

---

**2.1.8. `Port` (港口)**
*   **Description:** 物理港口。
*   **Properties:**
    *   `name: String` (Primary Key - from `SRC_PORT_MONTHLY.港口`, `SRC_PORT_CARGO.港口`, e.g., "武汉")
    *   `prov: String` (Optional - 港口所在省份)
    *   `sortNo: Integer` (Optional - 排序号)
*   **Example:** `(:Port {name: "武汉", province: "湖北省"})`

---

**2.1.9. `Province` (省份)**
*   **Description:** 行政省份。
*   **Properties:**
    *   `name: String` (Primary Key - e.g., "湖北省")
    *   `code: String` (Optional - 省份代码)
*   **Example:** `(:Province {name: "湖北省"})`

---

**2.1.10. `Basin` (流域)**
*   **Description:** 水域流域，特指"长江"。
*   **Properties:**
    *   `name: String` (Primary Key - e.g., "长江")
*   **Example:** `(:Basin {name: "长江"})`

---

**2.1.11. `ShippingRoute` (航线)**
*   **Description:** 代表长江航运中的标准化航线，连接两个港口的运输路径。航线是船舶运营和货物流动的基础单元。
*   **Properties:**
    *   `routeId: String` (Primary Key - 航线唯一标识符, e.g., "WH-NJ-001")
    *   `routeName: String` (航线名称, e.g., "武汉-南京航线")
    *   `routeCode: String` (航线代码, e.g., "WH-NJ")
    *   `originPortName: String` (起点港口名称, e.g., "武汉")
    *   `destinationPortName: String` (终点港口名称, e.g., "南京")
    *   `distance_km: Float` (航线距离 - 公里, e.g., 500.0)
*   **Example:** `(:ShippingRoute {routeId: "WH-NJ-001", routeName: "武汉-南京航线", distance_km: 500.0})`

---

**统计节点 (Statistical Nodes)**

**2.1.11. `ShipMonthStat` (船舶月度统计)**
*   **Description:** 存储特定船舶在特定 `YearMonth` 周期内的整体预聚合运营统计数据。
*   **Properties (mapped from `SRC_SHIP_SUMMARY.` - 月度汇总信息, 保持V3.1/V3.8的详细命名):**
    *   `name: String` (from `SHIP_NAME_CN`, e.g., "汉海5号")
    *   `opRatio: Float` (from `OPERATE_RATIO`)（有效营运率）
    *   `voyages: Integer` (from `VOY_NUM`)（航次数）
    *   `loadRatio: Float` (from `LOAD_RATIO`)（负载率）
    *   `anchorTime_day: Float` (from `SUM_ANCHOR_TIME`)（在港时间）
    *   `sailTime_day: Float` (from `SUM_SAIL_TIME`)（航行时间）
    *   `sailRatio: Float` (from `SAIL_RATIO`)（航行率）
    *   `turnover_tonkm: Float` (from `TURNOVER_OD`)（周转量）
    *   `loadVoyages: Integer` (from `LOAD_VOY_NUM`)（负载航次数）
    *   `dispatchLoadRatio: Float` (from `DISPATCH_LOAD_RATIO`)（发船装载率）
    *   `distanceLoadRatio: Float` (from `DISTANCE_LOAD_RATIO`)（运距装载率）
    *   `capacity_ton: Float` (from `CAPACITY_OD`)（货运量）
    *   `loadVoyRatio: Float` (from `LOAD_VOY_RATIO`)（负载航次率）
    *   `mileage_km: Float` (from `SUM_MILEAGE`)（运距）
    *   `tonDayOutput: Float` (from `TON_DAY_OUTPUT`)（吨天产量）
    *   `tonShipOutput: Float` (from `TON_SHIP_OUTPUT`)（吨船产量）
    *   `lastUpdated: Datetime`
*   **Example:** `(:ShipMonthStat {sourceRecordId: "26dff...", opTime_min: 43322})`

---

**2.1.12. `ShipMonthCargoStat` (船舶月度分货类统计)**
*   **Description:** 存储特定船舶在特定月份运输特定货物类别的统计数据。
*   **Properties:**
    *   `name: String` (from `SHIP_NAME_CN`, e.g., "汉海5号")    
    *   `subName: String` (e.g., "煤炭及制品")
    *   `cargo_ton: Float` (该船该月该货类的货运量 - 吨)
    *   `lastUpdated: Datetime`
*   **Example:** `(:ShipMonthCargoStat {cargo_ton: 5000.00})`
---

**2.1.12. `ShipMonthLineStat` (船舶月度分航线统计)** [优化版]
*   **Description:** 存储特定船舶在特定月份运输特定航线的统计数据。现在通过关系连接到独立的ShippingRoute节点。
*   **Properties:**
    *   `name: String` (from `SHIP_NAME_CN`, e.g., "汉海5号")
    *   `cargo_ton: Float` (该船该月该航线的货运量 - 吨)
    *   `turnover_tonkm: Float` (周转量 - 吨公里)
    *   `mileage_km: Float` (里程 - 公里)
    *   `voyageCount: Integer` (该航线航次数)
    *   `avgLoadRatio: Float` (该航线平均装载率)
    *   `lastUpdated: Datetime`
*   **Note:** 原有的 `portO` 和 `portD` 属性被移除，改为通过关系 `[:STAT_FOR_ROUTE]->(:ShippingRoute)` 连接
*   **Example:** `(:ShipMonthLineStat {cargo_ton: 5000.00, voyageCount: 3})`

---

**2.1.13. `RouteMonthStat` (航线月度统计)** [新增]
*   **Description:** 存储特定航线在特定月份的整体运营统计数据，聚合所有在该航线运营的船舶数据。
*   **Properties:**
    *   `totalShipCount: Integer` (该月在该航线运营的船舶总数)
    *   `totalVoyageCount: Integer` (该航线总航次数)
    *   `totalCargo_ton: Float` (该航线总货运量 - 吨)
    *   `totalTurnover_tonkm: Float` (该航线总周转量 - 吨公里)
    *   `avgLoadRatio: Float` (该航线平均装载率)
    *   `avgVoyageTime_hours: Float` (平均航行时间 - 小时)
    *   `utilizationRate: Float` (航线利用率: 实际运营天数/总天数)
    *   `lastUpdated: Datetime`
*   **Example:** `(:RouteMonthStat {totalShipCount: 25, totalCargo_ton: 150000.00})`

---

**2.1.14. `RouteMonthCargoStat` (航线月度分货类统计)** [新增]
*   **Description:** 存储特定航线在特定月份运输特定货物类别的统计数据。
*   **Properties:**
    *   `cargo_ton: Float` (该航线该月该货类的货运量 - 吨)
    *   `shipCount: Integer` (运输该货类的船舶数量)
    *   `voyageCount: Integer` (运输该货类的航次数)
    *   `avgCargoPerVoyage_ton: Float` (该货类平均每航次货运量)
    *   `lastUpdated: Datetime`
*   **Example:** `(:RouteMonthCargoStat {cargo_ton: 50000.00, shipCount: 8})`

---

**2.1.14. `PortMonthStat` (港口整体月度统计)**
*   **Description:** 港口在特定年月下的整体运营统计。
*   **Properties (mapped from `SRC_PORT_MONTHLY.`):**
    *   `inShipCount: Integer` (from `进港艘次`)
    *   `outShipCount: Integer` (from `出港艘次`)
    *   `inCargo_ton: Float` (from `进港运量`)
    *   `outCargo_ton: Float` (from `出港运量`)
    *   `anchorTime_days: Float` (from `在港时间`)
    *   `inLoadRatio: Float` (from `进港装载率`)
    *   `outLoadRatio: Float` (from `出港装载率`)
    *   `lastUpdated: Datetime`
*   **Example:** `(:PortMonthStat {inShipCount: 6192, inCargo_ton: 9539695.39})`

---

**2.1.15. `PortMonthCargoStat` (港口月度分货类统计)**
*   **Description:** 港口在特定年月处理特定货物的统计。
*   **Properties (mapped from `SRC_PORT_CARGO.`):**
    *   `inCargo_ton: Float` (from `进港货运量`)
    *   `outCargo_ton: Float` (from `出港货运量`)
    *   `lastUpdated: Datetime`
*   **Example:** `(:PortMonthCargoStat {inCargo_ton: 59576})`

---

**2.1.16. `ProvinceMonthStat` (省份整体月度统计)**
*   **Description:** 省份在特定年月下的整体运营统计。
*   **Properties:** (使用`total`或`avg`前缀)
    *   `totalinShipCount: Integer`
    *   `totaloutShipCount: Integer`
    *   `totalinCargo_ton: Float`
    *   `totaloutCargo_ton: Float`
    *   `avgPortStayTime_days: Float`
    *   `avginLoadRatio: Float`
    *   `avgoutLoadRatio: Float`
    *   `lastUpdated: Datetime`

---

**2.1.17. `ProvinceMonthCargoStat` (省份月度分货类统计)**
*   **Description:** 省份在特定年月处理特定货物的统计。
*   **Properties:** (使用`total`前缀)
    *   `totalinCargo_ton: Float`
    *   `totaloutCargo_ton: Float`
    *   `lastUpdated: Datetime`

---

**2.1.18. `BasinMonthStat` (流域整体月度统计)**
*   **Description:** 流域在特定年月下的整体运营统计。
*   **Properties:** (使用`total`前缀)
    *   `totalinCargo_ton: Float`
    *   `totaloutCargo_ton: Float`
    *   `totalThroughput_ton: Float`
    *   `activeShipCount: Integer` (需数据源)
    *   `activeCrewCount: Integer` (需数据源)
    *   `lastUpdated: Datetime`

---

**2.1.19. `BasinMonthCargoStat` (流域月度分货类统计)**
*   **Description:** 流域在特定年月处理特定货物的统计。
*   **Properties:** (使用`total`前缀)
    *   `totalCargoVolume_ton: Float`
    *   `lastUpdated: Datetime`

---

**2.1.20. `BasinYearStat` (流域整体年度统计)**
*   **Description:** 流域在特定年份的整体运营及结构统计。
*   **Properties:** (使用`annualTotal`或`annualAvg`前缀，或直接的年度计数)
    *   `annualTotalinCargo_ton: Float`
    *   `annualTotaloutCargo_ton: Float`
    *   `annualTotalThroughput_ton: Float`
    *   `annualActiveShipCount: Integer`
    *   `annualRegisteredShipCount: Integer`
    *   `annualActiveCrewCount: Integer`
    *   `annualRegisteredCrewCount: Integer`
    *   `annualSeaShipCount: Integer`
    *   `annualRiverShipCount: Integer`
    *   `annualAvgDwtOverall_ton: Float`
    *   `annualAvgDwtSeaShip_ton: Float`
    *   `annualAvgDwtRiverShip_ton: Float`
    *   `lastUpdated: Datetime`

---

**2.1.21. `BasinYearCargoStat` (流域年度分货类统计)**
*   **Description:** 流域在特定年份处理特定货物的总运量。
*   **Properties:** (使用`annualTotal`前缀)
    *   `annualTotalCargoVolume_ton: Float`
    *   `lastUpdated: Datetime`

---

**2.1.22. `BasinYearShipTypeStat` (可选 - 流域年度分船型统计)**
*   **Description:** 流域、年份、船型的特定统计。
*   **Properties:**
    *   `shipCount: Integer`
    *   `avgDwt_ton: Float`
    *   `totalDwt_ton: Float`
    *   `lastUpdated: Datetime`

---

**2.1.23. `ShipRealtime` (船舶实时状态)**
*   **Description:** 一个自包含的信息聚合节点，存储来自多个源的最新动态信息。
*   **Properties:**
    *   **AIS物理状态 (由AIS流每10分钟更新):**
        *   `lat: Float` (最新纬度)
        *   `lon: Float` (最新经度)
        *   `sog: Float` (最新对地航速)
        *   `cog: Float` (最新对地航向)
        *   `navStatus: String` (最新导航状态)
        *   `aisTimestamp: Datetime` (该条AIS信息的时间戳)
        *   `km: Float` (航道里程)
        *   `portAis: String` (通过AIS反向地理编码得到的城市或区域名)
        *   `orgName: String` (管辖海事机构)
    *   **报港业务状态 (由报港数据流按天更新):**
        *   `portStatus: String` (业务状态: '在港' 或 '在途')
        *   `portReport: String` (报告的港口名称, e.g., "武汉港")
        *   `reportTimeIn: Datetime` (进港报告时间)
        *   `reportTimeOut: Datetime` (出港报告时间)
        *   `actualCarryCapacityIn_ton: Float` (进港实载 - 吨)
        *   `actualCarryCapacityOut_ton: Float` (出港实载 - 吨)
        *   `portCargoIn_to: String` (进港装卸情况描述)
        *   `portCargoOut_ton: String` (出港装卸情况描述)
    *   **元数据:**
        *   `lastUpdated: Datetime` (此节点任何信息的最后更新时间)
*   **Example:** `(:ShipRealtime {lat: 30.563, lon: 114.305, navStatus: "Under way", portStatus: "在途", lastUpdated: datetime("2024-04-01T12:05:00")})`

---

**2.2. 关系** (增加航线维度)
*   **基础实体关系:**
    *   `(s:Ship)-[:IS_TYPE]->(st:ShipType)`
    *   `(st:ShipType)-[:BELONGS_TO_CATEGORY]->(sc:ShipCategory)`
    *   `(ym:YearMonth)-[:MONTH_OF_YEAR]->(y:Year)`
    *   `(ct:CargoType)-[:IS_CATEGORY_OF]->(cc:CargoCategory)`
    *   `(p:Port)-[:LOCATED_IN_PROVINCE]->(prov:Province)`
    *   `(prov:Province)-[:PART_OF_BASIN]->(b:Basin)`
*   **航线关系:** [新增]
    *   `(sr:ShippingRoute)-[:ROUTE_ORIGIN]->(pO:Port)` (航线起点)
    *   `(sr:ShippingRoute)-[:ROUTE_DESTINATION]->(pD:Port)` (航线终点)
    *   `(sr:ShippingRoute)-[:ROUTE_IN_BASIN]->(b:Basin)` (航线所属流域)
    *   `(sr:ShippingRoute)-[:ROUTE_CROSSES_PROVINCE]->(prov:Province)` (航线经过的省份，可能多个)
*   **船舶统计关系:** [更新]
    *   `(sms:ShipMonthStat)-[:STAT_FOR_SHIP]->(s:Ship)`, `(sms)-[:STAT_FOR_MONTH]->(ym:YearMonth)`
    *   `(smcs:ShipMonthCargoStat)-[:CARGO_STAT_FOR_SHIP]->(s:Ship)`, `(smcs)-[:CARGO_STAT_FOR_MONTH]->(ym:YearMonth)`, `(smcs)-[:CARGO_STAT_FOR_TYPE]->(ct:CargoType)`
    *   `(smls:ShipMonthLineStat)-[:LINE_STAT_FOR_SHIP]->(s:Ship)`, `(smls)-[:LINE_STAT_FOR_MONTH]->(ym:YearMonth)`, `(smls)-[:STAT_FOR_ROUTE]->(sr:ShippingRoute)` [更新]
    *   `(sys:ShipYearStat)-[:ANNUAL_STAT_FOR_SHIP]->(s:Ship)`, `(sys)-[:ANNUAL_STAT_FOR_YEAR]->(y:Year)`
*   **港口统计关系:**
    *   `(pms:PortMonthStat)-[:STAT_FOR_PORT]->(p:Port)`, `(pms)-[:STAT_FOR_MONTH]->(ym:YearMonth)`
    *   `(pmcs:PortMonthCargoStat)-[:PORT_CARGO_STAT_FOR_PORT]->(p:Port)`, `(pmcs)-[:PORT_CARGO_STAT_FOR_MONTH]->(ym:YearMonth)`, `(pmcs)-[:PORT_CARGO_STAT_FOR_TYPE]->(ct:CargoType)`
*   **省份统计关系:**
    *   `(pmsProv:ProvinceMonthStat)-[:PROV_STAT_FOR_PROVINCE]->(prov:Province)`, `(pmsProv)-[:PROV_STAT_FOR_MONTH]->(ym:YearMonth)`
    *   `(pmcsProv:ProvinceMonthCargoStat)-[:PROV_CARGO_STAT_FOR_PROVINCE]->(prov:Province)`, `(pmcsProv)-[:PROV_CARGO_STAT_FOR_MONTH]->(ym:YearMonth)`, `(pmcsProv)-[:PROV_CARGO_STAT_FOR_TYPE]->(ct:CargoType)`
*   **航线统计关系:** [新增]
    *   `(rms:RouteMonthStat)-[:ROUTE_STAT_FOR_ROUTE]->(sr:ShippingRoute)`, `(rms)-[:ROUTE_STAT_FOR_MONTH]->(ym:YearMonth)`
    *   `(rmcs:RouteMonthCargoStat)-[:ROUTE_CARGO_STAT_FOR_ROUTE]->(sr:ShippingRoute)`, `(rmcs)-[:ROUTE_CARGO_STAT_FOR_MONTH]->(ym:YearMonth)`, `(rmcs)-[:ROUTE_CARGO_STAT_FOR_TYPE]->(ct:CargoType)`
*   **流域统计关系:**
    *   `(bms:BasinMonthStat)-[:BASIN_STAT_FOR_BASIN]->(b:Basin)`, `(bms)-[:BASIN_STAT_FOR_MONTH]->(ym:YearMonth)`
    *   `(bmcs:BasinMonthCargoStat)-[:BASIN_CARGO_STAT_FOR_BASIN]->(b:Basin)`, `(bmcs)-[:BASIN_CARGO_STAT_FOR_MONTH]->(ym:YearMonth)`, `(bmcs)-[:BASIN_CARGO_STAT_FOR_TYPE]->(ct:CargoType)`
    *   `(bys:BasinYearStat)-[:BASIN_ANNUAL_STAT_FOR_BASIN]->(b:Basin)`, `(bys)-[:BASIN_ANNUAL_STAT_FOR_YEAR]->(y:Year)`
    *   `(bycs:BasinYearCargoStat)-[:BASIN_ANNUAL_CARGO_STAT_FOR_BASIN]->(b:Basin)`, `(bycs)-[:BASIN_ANNUAL_CARGO_STAT_FOR_YEAR]->(y:Year)`, `(bycs)-[:BASIN_ANNUAL_CARGO_STAT_FOR_TYPE]->(ct:CargoType)`
    *   (可选) `(bysts:BasinYearShipTypeStat)-[:BYSTS_FOR_BASIN]->(b:Basin)`, `(bysts)-[:BYSTS_FOR_YEAR]->(y:Year)`, `(bysts)-[:BYSTS_FOR_SHIPTYPE]->(st:ShipType)`

**2.3. 数据模型图 (Mermaid)** (结构与V3.9类似，各节点属性列表更详尽)

**3. 约束和索引** (与V3.9一致，基于定义的PK属性名)

**4. 数据加载策略** (与V3.9一致，ETL脚本中使用的参数名需对应本版本定义的属性名)
    *   ETL聚合 `ShipYearStat` 时，确保使用正确的聚合函数（SUM/AVG）对应每个月度属性。

**5. Cypher 查询示例** (增加航线维度查询)

**5.1. 航线相关查询示例**

```cypher
-- 查询所有航线及其基本信息
MATCH (sr:ShippingRoute)
OPTIONAL MATCH (sr)-[:ROUTE_ORIGIN]->(pO:Port)
OPTIONAL MATCH (sr)-[:ROUTE_DESTINATION]->(pD:Port)
RETURN sr.routeName, pO.name as origin, pD.name as destination, sr.distance_km
ORDER BY sr.distance_km DESC

-- 查询特定航线的月度统计
MATCH (sr:ShippingRoute {routeName: "武汉-南京航线"})
<-[:ROUTE_STAT_FOR_ROUTE]-(rms:RouteMonthStat)
-[:ROUTE_STAT_FOR_MONTH]->(ym:YearMonth)
WHERE ym.ym >= "202401"
RETURN ym.ym, rms.totalShipCount, rms.totalCargo_ton, rms.avgLoadRatio
ORDER BY ym.ym

-- 查询船舶在特定航线的表现
MATCH (s:Ship {name: "汉海5号"})
<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)
-[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
-[:ROUTE_STAT_FOR_MONTH]->(ym:YearMonth {ym: "202506"})
RETURN sr.routeName, smls.cargo_ton, smls.voyageCount, smls.avgLoadRatio

-- 查询最繁忙的航线TOP5
MATCH (sr:ShippingRoute)<-[:ROUTE_STAT_FOR_ROUTE]-(rms:RouteMonthStat)
-[:ROUTE_STAT_FOR_MONTH]->(ym:YearMonth {ym: "202506"})
RETURN sr.routeName, rms.totalShipCount, rms.totalCargo_ton
ORDER BY rms.totalCargo_ton DESC LIMIT 5

-- 查询航线的货物构成
MATCH (sr:ShippingRoute {routeName: "武汉-南京航线"})
<-[:ROUTE_CARGO_STAT_FOR_ROUTE]-(rmcs:RouteMonthCargoStat)
-[:ROUTE_CARGO_STAT_FOR_TYPE]->(ct:CargoType)
-[:ROUTE_CARGO_STAT_FOR_MONTH]->(ym:YearMonth {ym: "202506"})
RETURN ct.subName, rmcs.cargo_ton, rmcs.shipCount
ORDER BY rmcs.cargo_ton DESC
```

**5.2. 传统查询示例** (与V3.9一致，查询中的属性名需对应本版本定义的属性名)

---

## 6. 航线维度的业务价值

### 6.1. 新增航线维度的重要意义

航线维度的引入填补了数据库设计中的一个重要空白，带来以下业务价值：

1. **航线运营分析**: 可以分析特定航线的运营效率、货运量趋势、船舶利用率等
2. **航线对比**: 支持不同航线之间的运营数据对比，识别高效航线和瓶颈航线
3. **航线优化**: 基于历史数据分析航线的最优配置和资源分配
4. **货流分析**: 分析特定航线上的货物流向和构成，支持物流规划
5. **船舶调度**: 基于航线数据优化船舶在不同航线间的调度安排

### 6.2. 数据模型优化

相比原有设计，新的航线维度实现了：

- **数据标准化**: 将原本分散在 `ShipMonthLineStat` 中的 `portO` 和 `portD` 标准化为独立的 `ShippingRoute` 实体
- **关系清晰化**: 通过明确的关系连接，使航线与港口、省份、流域的关系更加清晰
- **统计完整性**: 新增 `RouteMonthStat` 和 `RouteMonthCargoStat`，提供航线级别的聚合统计
- **查询效率**: 支持更高效的航线相关查询和分析

### 6.3. API集成支持

航线维度完美支持新API设计v3.1中的各种查询类型：

- **POINT查询**: 查询特定航线在特定时间的运营指标
- **PROFILE查询**: 获取航线的完整画像，包括基本信息、历史统计、货物构成等
- **TREND查询**: 分析航线运营指标的时间趋势
- **COMPARE查询**: 对比多条航线的运营表现
- **RANK查询**: 按运营指标对航线进行排名

---

## 7. 注意事项

*   **数据一致性:** 确保所有统计数据的时间范围和聚合逻辑一致。
*   **性能优化:** 对于高频查询的属性（如 `mmsi`, `name`, `ym`, `routeId`），建议创建索引。
*   **扩展性:** 模型设计支持未来添加更多统计维度和实体类型。
*   **航线标识:** 建议建立标准的航线编码体系，确保航线标识的唯一性和一致性。
*   **数据迁移:** 现有的 `ShipMonthLineStat` 数据需要迁移到新的航线关系结构中。

