-- =====================================================
-- 航线数据写入SQL脚本
-- 用于从Oracle数据库提取航线相关数据
-- 符合数据库设计文档v4.0的航线维度要求
-- =====================================================

-- =====================================================
-- 1. 航线基础数据提取
-- 用于创建ShippingRoute节点
-- =====================================================

-- 1.1 提取唯一航线组合（基础版本）
SELECT DISTINCT
    t.merge_city_name_o as origin_port,
    t.merge_city_name_d as destination_port,
    COUNT(*) as frequency,
    AVG(t.MILEAGE) as avg_distance_km,
    MIN(t.ym) as first_seen,
    MAX(t.ym) as last_seen
FROM yssjtj.od_dwd_statistics t
JOIN yssjtj.dim_l_ship_basic_data s ON t.ship_id = s.ship_id
WHERE 1=1
  AND t.merge_city_name_o IS NOT NULL 
  AND t.merge_city_name_d IS NOT NULL
  AND t.merge_city_name_o != t.merge_city_name_d
  AND t.ym >= '202401'  -- 可调整时间范围
GROUP BY t.merge_city_name_o, t.merge_city_name_d
HAVING COUNT(*) >= 5  -- 至少5次航行才认为是有效航线
ORDER BY COUNT(*) DESC;

-- 1.2 提取航线详细信息（增强版本）
SELECT DISTINCT
    t.merge_city_name_o as origin_port,
    t.merge_city_name_d as destination_port,
    COUNT(*) as total_voyages,
    COUNT(DISTINCT t.ship_id) as unique_ships,
    AVG(t.MILEAGE) as avg_distance_km,
    MIN(t.MILEAGE) as min_distance_km,
    MAX(t.MILEAGE) as max_distance_km,
    SUM(t.capacity) as total_cargo_ton,
    AVG(t.capacity) as avg_cargo_per_voyage,
    MIN(t.ym) as first_operation,
    MAX(t.ym) as last_operation,
    -- 计算航线活跃度
    COUNT(DISTINCT t.ym) as active_months,
    -- 主要货物类型
    LISTAGG(DISTINCT t.cargo_type, ',') WITHIN GROUP (ORDER BY t.cargo_type) as cargo_types
FROM yssjtj.od_dwd_statistics t
JOIN yssjtj.dim_l_ship_basic_data s ON t.ship_id = s.ship_id
WHERE 1=1
  AND t.merge_city_name_o IS NOT NULL 
  AND t.merge_city_name_d IS NOT NULL
  AND t.merge_city_name_o != t.merge_city_name_d
  AND t.ym >= '202401'
GROUP BY t.merge_city_name_o, t.merge_city_name_d
HAVING COUNT(*) >= 10  -- 提高阈值确保航线重要性
ORDER BY COUNT(*) DESC;

-- =====================================================
-- 2. 航线月度统计数据提取
-- 用于创建RouteMonthStat节点
-- =====================================================

-- 2.1 航线月度基础统计
SELECT 
    t.ym,
    t.merge_city_name_o as origin_port,
    t.merge_city_name_d as destination_port,
    COUNT(DISTINCT t.ship_id) as total_ship_count,
    COUNT(*) as total_voyage_count,
    SUM(t.capacity) as total_cargo_ton,
    SUM(t.TURNOVER) as total_turnover_tonkm,
    AVG(t.MILEAGE) as avg_distance_km,
    -- 计算平均装载率
    AVG(CASE WHEN t.capacity > 0 AND s.ship_dwt > 0 
        THEN t.capacity / s.ship_dwt ELSE NULL END) as avg_load_ratio,
    -- 计算航线效率指标
    SUM(t.capacity) / COUNT(*) as avg_cargo_per_voyage,
    SUM(t.TURNOVER) / SUM(t.MILEAGE) as cargo_efficiency_ratio
FROM yssjtj.od_dwd_statistics t
JOIN yssjtj.dim_l_ship_basic_data s ON t.ship_id = s.ship_id
WHERE 1=1
  AND t.merge_city_name_o IS NOT NULL 
  AND t.merge_city_name_d IS NOT NULL
  AND t.merge_city_name_o != t.merge_city_name_d
  AND t.ym >= '202401'
GROUP BY t.ym, t.merge_city_name_o, t.merge_city_name_d
HAVING COUNT(*) >= 3  -- 每月至少3次航行
ORDER BY t.ym DESC, SUM(t.capacity) DESC;

-- 2.2 航线月度高级统计（包含时间分析）
SELECT 
    t.ym,
    t.merge_city_name_o as origin_port,
    t.merge_city_name_d as destination_port,
    COUNT(DISTINCT t.ship_id) as total_ship_count,
    COUNT(*) as total_voyage_count,
    SUM(t.capacity) as total_cargo_ton,
    SUM(t.TURNOVER) as total_turnover_tonkm,
    AVG(t.MILEAGE) as avg_distance_km,
    AVG(CASE WHEN t.capacity > 0 AND s.ship_dwt > 0 
        THEN t.capacity / s.ship_dwt ELSE NULL END) as avg_load_ratio,
    -- 船舶类型分布
    COUNT(DISTINCT s.ship_sub_type_name) as ship_type_diversity,
    -- 货物类型分布
    COUNT(DISTINCT t.cargo_type) as cargo_type_diversity,
    -- 计算利用率（假设每月30天）
    COUNT(DISTINCT TRUNC(t.start_time)) / 30.0 as utilization_rate,
    -- 主要船舶类型
    (SELECT s2.ship_sub_type_name 
     FROM yssjtj.od_dwd_statistics t2
     JOIN yssjtj.dim_l_ship_basic_data s2 ON t2.ship_id = s2.ship_id
     WHERE t2.ym = t.ym 
       AND t2.merge_city_name_o = t.merge_city_name_o
       AND t2.merge_city_name_d = t.merge_city_name_d
     GROUP BY s2.ship_sub_type_name
     ORDER BY COUNT(*) DESC
     FETCH FIRST 1 ROWS ONLY) as dominant_ship_type
FROM yssjtj.od_dwd_statistics t
JOIN yssjtj.dim_l_ship_basic_data s ON t.ship_id = s.ship_id
WHERE 1=1
  AND t.merge_city_name_o IS NOT NULL 
  AND t.merge_city_name_d IS NOT NULL
  AND t.merge_city_name_o != t.merge_city_name_d
  AND t.ym >= '202401'
GROUP BY t.ym, t.merge_city_name_o, t.merge_city_name_d
HAVING COUNT(*) >= 5
ORDER BY t.ym DESC, SUM(t.capacity) DESC;

-- =====================================================
-- 3. 航线分货类统计数据提取
-- 用于创建RouteMonthCargoStat节点
-- =====================================================

-- 3.1 航线分货类基础统计
SELECT 
    t.ym,
    t.merge_city_name_o as origin_port,
    t.merge_city_name_d as destination_port,
    t.cargo_type,
    COUNT(DISTINCT t.ship_id) as ship_count,
    COUNT(*) as voyage_count,
    SUM(t.capacity) as cargo_ton,
    AVG(t.capacity) as avg_cargo_per_voyage_ton,
    SUM(t.TURNOVER) as total_turnover_tonkm,
    AVG(t.MILEAGE) as avg_distance_km
FROM yssjtj.od_dwd_statistics t
JOIN yssjtj.dim_l_ship_basic_data s ON t.ship_id = s.ship_id
WHERE 1=1
  AND t.merge_city_name_o IS NOT NULL 
  AND t.merge_city_name_d IS NOT NULL
  AND t.merge_city_name_o != t.merge_city_name_d
  AND t.cargo_type IS NOT NULL
  AND t.ym >= '202401'
GROUP BY t.ym, t.merge_city_name_o, t.merge_city_name_d, t.cargo_type
HAVING COUNT(*) >= 2  -- 每种货物类型至少2次运输
ORDER BY t.ym DESC, SUM(t.capacity) DESC;

-- 3.2 航线货物类型市场份额分析
SELECT 
    t.ym,
    t.merge_city_name_o as origin_port,
    t.merge_city_name_d as destination_port,
    t.cargo_type,
    COUNT(DISTINCT t.ship_id) as ship_count,
    COUNT(*) as voyage_count,
    SUM(t.capacity) as cargo_ton,
    -- 计算该货物在该航线的市场份额
    SUM(t.capacity) / SUM(SUM(t.capacity)) OVER (
        PARTITION BY t.ym, t.merge_city_name_o, t.merge_city_name_d
    ) as market_share_in_route,
    -- 计算该航线在该货物的市场份额
    SUM(t.capacity) / SUM(SUM(t.capacity)) OVER (
        PARTITION BY t.ym, t.cargo_type
    ) as route_share_in_cargo,
    AVG(t.capacity) as avg_cargo_per_voyage_ton,
    SUM(t.TURNOVER) as total_turnover_tonkm
FROM yssjtj.od_dwd_statistics t
JOIN yssjtj.dim_l_ship_basic_data s ON t.ship_id = s.ship_id
WHERE 1=1
  AND t.merge_city_name_o IS NOT NULL 
  AND t.merge_city_name_d IS NOT NULL
  AND t.merge_city_name_o != t.merge_city_name_d
  AND t.cargo_type IS NOT NULL
  AND t.ym >= '202401'
GROUP BY t.ym, t.merge_city_name_o, t.merge_city_name_d, t.cargo_type
HAVING COUNT(*) >= 3
ORDER BY t.ym DESC, SUM(t.capacity) DESC;

-- =====================================================
-- 4. 航线关系数据提取
-- 用于建立航线与港口、省份、流域的关系
-- =====================================================

-- 4.1 航线与港口关系
SELECT DISTINCT
    t.merge_city_name_o as origin_port,
    t.merge_city_name_d as destination_port,
    -- 尝试匹配港口的省份信息
    p1.regport_prov as origin_province,
    p2.regport_prov as destination_province
FROM yssjtj.od_dwd_statistics t
LEFT JOIN (
    SELECT DISTINCT regport_name, regport_prov 
    FROM yssjtj.dim_l_ship_basic_data 
    WHERE regport_name IS NOT NULL
) p1 ON t.merge_city_name_o = p1.regport_name
LEFT JOIN (
    SELECT DISTINCT regport_name, regport_prov 
    FROM yssjtj.dim_l_ship_basic_data 
    WHERE regport_name IS NOT NULL
) p2 ON t.merge_city_name_d = p2.regport_name
WHERE 1=1
  AND t.merge_city_name_o IS NOT NULL 
  AND t.merge_city_name_d IS NOT NULL
  AND t.merge_city_name_o != t.merge_city_name_d
  AND t.ym >= '202401'
ORDER BY t.merge_city_name_o, t.merge_city_name_d;

-- =====================================================
-- 5. 数据质量检查查询
-- =====================================================

-- 5.1 检查数据完整性
SELECT 
    '航线数据完整性检查' as check_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN merge_city_name_o IS NULL THEN 1 END) as missing_origin,
    COUNT(CASE WHEN merge_city_name_d IS NULL THEN 1 END) as missing_destination,
    COUNT(CASE WHEN cargo_type IS NULL THEN 1 END) as missing_cargo_type,
    COUNT(CASE WHEN capacity IS NULL OR capacity <= 0 THEN 1 END) as invalid_capacity,
    COUNT(CASE WHEN MILEAGE IS NULL OR MILEAGE <= 0 THEN 1 END) as invalid_mileage
FROM yssjtj.od_dwd_statistics t
WHERE t.ym >= '202401';

-- 5.2 检查航线数据分布
SELECT 
    '航线数据分布' as analysis_type,
    COUNT(DISTINCT merge_city_name_o || '-' || merge_city_name_d) as unique_routes,
    COUNT(DISTINCT merge_city_name_o) as unique_origin_ports,
    COUNT(DISTINCT merge_city_name_d) as unique_destination_ports,
    COUNT(DISTINCT cargo_type) as unique_cargo_types,
    COUNT(DISTINCT ym) as unique_months,
    MIN(ym) as earliest_month,
    MAX(ym) as latest_month
FROM yssjtj.od_dwd_statistics t
WHERE 1=1
  AND t.merge_city_name_o IS NOT NULL 
  AND t.merge_city_name_d IS NOT NULL
  AND t.merge_city_name_o != t.merge_city_name_d
  AND t.ym >= '202401';

-- =====================================================
-- 使用说明：
-- 1. 这些SQL查询可以直接在Oracle数据库中执行
-- 2. 根据实际需要调整WHERE条件中的时间范围和过滤条件
-- 3. 可以调整HAVING子句中的阈值来控制数据质量
-- 4. 建议先运行数据质量检查查询了解数据状况
-- 5. 在生产环境中使用时，建议添加适当的索引优化查询性能
-- =====================================================
