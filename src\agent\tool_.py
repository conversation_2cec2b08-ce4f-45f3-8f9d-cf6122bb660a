"""
长江航运智能分析Agent - 工具模块 v3.1
基于最新API设计文档v3.1，提供统一的查询工具接口
支持C-STEL时间表达语言和多实体类型查询
"""

from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
import re
import json
import requests
from loguru import logger


class CSTELParser:
    """C-STEL时间表达式解析器"""
    
    def __init__(self, current_date: str = None):
        """
        初始化C-STEL解析器
        
        Args:
            current_date: 当前日期，格式YYYY-MM-DD，默认为今天
        """
        self.current_date = current_date or datetime.now().strftime("%Y-%m-%d")
        self.current_year = int(self.current_date[:4])
        self.current_month = int(self.current_date[5:7])
        
    def parse(self, expression: str) -> Dict[str, Any]:
        """
        解析C-STEL时间表达式
        
        Args:
            expression: C-STEL时间表达式
            
        Returns:
            解析结果字典
        """
        try:
            expression = expression.strip()
            
            # 相对时间表达式
            if expression.startswith('R'):
                return self._parse_relative(expression)
            
            # 聚合表达式
            if expression.startswith('A'):
                return self._parse_aggregate(expression)
            
            # 当前时间表达式
            if expression in ['CYTD', 'CMTD']:
                return self._parse_current(expression)
            
            # 区间表达式
            if '_' in expression:
                return self._parse_range(expression)
            
            # 列表表达式
            if ',' in expression:
                return self._parse_list(expression)
            
            # 原子表达式
            return self._parse_atomic(expression)
            
        except Exception as e:
            return {
                "status": "error",
                "error": f"C-STEL解析失败: {str(e)}",
                "expression": expression
            }
    
    def _parse_atomic(self, expr: str) -> Dict[str, Any]:
        """解析原子时间表达式"""
        # 年份: Y2024
        if expr.startswith('Y') and len(expr) == 5:
            year = int(expr[1:])
            return {
                "status": "success",
                "type": "atomic",
                "granularity": "year",
                "periods": [str(year)],
                "db_mapping": {"year_nodes": [year], "month_nodes": []}
            }
        
        # 季度: Q2024Q1
        if expr.startswith('Q') and 'Q' in expr[1:]:
            year = int(expr[1:5])
            quarter = int(expr[6:])
            months = self._quarter_to_months(year, quarter)
            return {
                "status": "success",
                "type": "atomic",
                "granularity": "quarter",
                "periods": [f"{year}Q{quarter}"],
                "db_mapping": {"year_nodes": [year], "month_nodes": months}
            }
        
        # 月份: M202401
        if expr.startswith('M') and len(expr) == 7:
            year_month = expr[1:]
            year = int(year_month[:4])
            return {
                "status": "success",
                "type": "atomic", 
                "granularity": "month",
                "periods": [year_month],
                "db_mapping": {"year_nodes": [year], "month_nodes": [year_month]}
            }
        
        # 日期: D20240315 (降级到月)
        if expr.startswith('D') and len(expr) == 9:
            date_str = expr[1:]
            year = int(date_str[:4])
            month = int(date_str[4:6])
            year_month = f"{year}{month:02d}"
            return {
                "status": "success",
                "type": "atomic",
                "granularity": "day",
                "periods": [date_str],
                "db_mapping": {"year_nodes": [year], "month_nodes": [year_month]},
                "degraded": True,
                "degraded_to": "month"
            }
        
        raise ValueError(f"无效的原子表达式: {expr}")
    
    def _parse_range(self, expr: str) -> Dict[str, Any]:
        """解析区间表达式"""
        start_expr, end_expr = expr.split('_', 1)
        start_result = self._parse_atomic(start_expr)
        end_result = self._parse_atomic(end_expr)
        
        if start_result["status"] != "success" or end_result["status"] != "success":
            raise ValueError("区间表达式中包含无效的原子表达式")
        
        # 生成区间内的所有时间点
        periods = self._generate_range_periods(start_result, end_result)
        
        return {
            "status": "success",
            "type": "range",
            "granularity": start_result["granularity"],
            "periods": periods["periods"],
            "db_mapping": periods["db_mapping"]
        }
    
    def _parse_relative(self, expr: str) -> Dict[str, Any]:
        """解析相对时间表达式"""
        # R6M: 最近6个月
        if expr.endswith('M'):
            months = int(expr[1:-1])
            current_ym = f"{self.current_year}{self.current_month:02d}"
            month_nodes = []
            
            for i in range(months):
                date = datetime(self.current_year, self.current_month, 1) - timedelta(days=32*i)
                month_nodes.append(f"{date.year}{date.month:02d}")
            
            month_nodes.reverse()
            
            return {
                "status": "success",
                "type": "relative",
                "granularity": "month",
                "periods": month_nodes,
                "db_mapping": {"year_nodes": list(set([int(m[:4]) for m in month_nodes])), "month_nodes": month_nodes}
            }
        
        raise ValueError(f"不支持的相对时间表达式: {expr}")

    def _parse_list(self, expr: str) -> Dict[str, Any]:
        """解析列表表达式"""
        expressions = [e.strip() for e in expr.split(',')]
        all_periods = []
        all_year_nodes = set()
        all_month_nodes = []
        granularity = None

        for sub_expr in expressions:
            result = self._parse_atomic(sub_expr)
            if result["status"] != "success":
                raise ValueError(f"列表中包含无效表达式: {sub_expr}")

            if granularity is None:
                granularity = result["granularity"]
            elif granularity != result["granularity"]:
                raise ValueError("列表中的表达式粒度必须一致")

            all_periods.extend(result["periods"])
            all_year_nodes.update(result["db_mapping"]["year_nodes"])
            all_month_nodes.extend(result["db_mapping"]["month_nodes"])

        return {
            "status": "success",
            "type": "list",
            "granularity": granularity,
            "periods": all_periods,
            "db_mapping": {
                "year_nodes": list(all_year_nodes),
                "month_nodes": all_month_nodes
            }
        }

    def _parse_current(self, expr: str) -> Dict[str, Any]:
        """解析当前时间表达式"""
        if expr == "CYTD":  # Current Year To Date
            current_year = self.current_year
            current_month = self.current_month
            months = [f"{current_year}{i:02d}" for i in range(1, current_month + 1)]
            return {
                "status": "success",
                "type": "current",
                "granularity": "month",
                "periods": months,
                "db_mapping": {"year_nodes": [current_year], "month_nodes": months}
            }
        elif expr == "CMTD":  # Current Month To Date
            current_ym = f"{self.current_year}{self.current_month:02d}"
            return {
                "status": "success",
                "type": "current",
                "granularity": "month",
                "periods": [current_ym],
                "db_mapping": {"year_nodes": [self.current_year], "month_nodes": [current_ym]}
            }

        raise ValueError(f"不支持的当前时间表达式: {expr}")

    def _parse_aggregate(self, expr: str) -> Dict[str, Any]:
        """解析聚合表达式"""
        # AY2024_Q: 2024年的每个季度
        if expr.startswith("AY") and expr.endswith("_Q"):
            year = int(expr[2:6])
            quarters = [f"{year}Q{i}" for i in range(1, 5)]
            all_months = []
            for q in range(1, 5):
                all_months.extend(self._quarter_to_months(year, q))

            return {
                "status": "success",
                "type": "aggregate",
                "granularity": "quarter",
                "periods": quarters,
                "db_mapping": {"year_nodes": [year], "month_nodes": all_months}
            }

        # AY2024_M: 2024年的每个月
        if expr.startswith("AY") and expr.endswith("_M"):
            year = int(expr[2:6])
            months = [f"{year}{i:02d}" for i in range(1, 13)]
            return {
                "status": "success",
                "type": "aggregate",
                "granularity": "month",
                "periods": months,
                "db_mapping": {"year_nodes": [year], "month_nodes": months}
            }

        raise ValueError(f"不支持的聚合表达式: {expr}")

    def _quarter_to_months(self, year: int, quarter: int) -> List[str]:
        """将季度转换为月份列表"""
        start_month = (quarter - 1) * 3 + 1
        return [f"{year}{(start_month + i):02d}" for i in range(3)]
    
    def _generate_range_periods(self, start_result: Dict, end_result: Dict) -> Dict[str, Any]:
        """生成区间内的所有时间点"""
        if start_result["granularity"] != end_result["granularity"]:
            raise ValueError("区间表达式的起止时间粒度必须一致")
        
        granularity = start_result["granularity"]
        
        if granularity == "month":
            start_ym = start_result["periods"][0]
            end_ym = end_result["periods"][0]
            
            start_year, start_month = int(start_ym[:4]), int(start_ym[4:])
            end_year, end_month = int(end_ym[:4]), int(end_ym[4:])
            
            periods = []
            current_year, current_month = start_year, start_month
            
            while current_year < end_year or (current_year == end_year and current_month <= end_month):
                periods.append(f"{current_year}{current_month:02d}")
                current_month += 1
                if current_month > 12:
                    current_month = 1
                    current_year += 1
            
            return {
                "periods": periods,
                "db_mapping": {
                    "year_nodes": list(set([int(p[:4]) for p in periods])),
                    "month_nodes": periods
                }
            }
        
        # 其他粒度的实现...
        raise ValueError(f"暂不支持{granularity}粒度的区间查询")


class EntityResolver:
    """实体解析器 - 支持模糊匹配和多标识符解析"""
    
    def __init__(self):
        self.logger = logger
        
    def resolve(self, entity_type: str, identifier: Union[str, List[str]], 
                resolution_strategy: str = "fuzzy") -> Dict[str, Any]:
        """
        解析实体标识符
        
        Args:
            entity_type: 实体类型
            identifier: 实体标识符
            resolution_strategy: 解析策略
            
        Returns:
            解析结果
        """
        try:
            if isinstance(identifier, list):
                return self._resolve_multiple(entity_type, identifier, resolution_strategy)
            else:
                return self._resolve_single(entity_type, identifier, resolution_strategy)
        except Exception as e:
            return {
                "status": "error",
                "error": f"实体解析失败: {str(e)}",
                "entity_type": entity_type,
                "identifier": identifier
            }
    
    def _resolve_single(self, entity_type: str, identifier: str, strategy: str) -> Dict[str, Any]:
        """解析单个实体"""
        # 这里应该调用实际的实体搜索API
        # 暂时返回模拟结果
        return {
            "status": "success",
            "entity_type": entity_type,
            "identifier": identifier,
            "resolved_entities": [{
                "name": identifier,
                "type": entity_type,
                "confidence": 0.95,
                "strategy_used": strategy
            }]
        }
    
    def _resolve_multiple(self, entity_type: str, identifiers: List[str], strategy: str) -> Dict[str, Any]:
        """解析多个实体"""
        resolved_entities = []
        for identifier in identifiers:
            result = self._resolve_single(entity_type, identifier, strategy)
            if result["status"] == "success":
                resolved_entities.extend(result["resolved_entities"])
        
        return {
            "status": "success",
            "entity_type": entity_type,
            "identifier": identifiers,
            "resolved_entities": resolved_entities
        }


class APIClient:
    """API客户端 - 统一的HTTP请求处理"""
    
    def __init__(self, base_url: str = "http://localhost:8000/api/v3.1"):
        self.base_url = base_url
        self.session = requests.Session()
        self.logger = logger
        
    def query(self, endpoint: str, data: Dict[str, Any], timeout: int = 30) -> Dict[str, Any]:
        """发送查询请求"""
        try:
            url = f"{self.base_url}/{endpoint}"
            self.logger.info(f"API请求: {url}, 数据: {json.dumps(data, ensure_ascii=False)}")
            
            response = self.session.post(url, json=data, timeout=timeout)
            
            if response.status_code == 200:
                result = response.json()
                self.logger.info(f"API响应成功: {response.status_code}")
                return result
            else:
                error_msg = f"API请求失败，状态码: {response.status_code}"
                self.logger.error(error_msg)
                return {
                    "status": "error",
                    "error": error_msg,
                    "status_code": response.status_code
                }
                
        except Exception as e:
            error_msg = f"API请求异常: {str(e)}"
            self.logger.error(error_msg)
            return {
                "status": "error",
                "error": error_msg
            }


class ShippingDataToolKit:
    """长江航运数据工具包 - 基于API设计文档v3.1"""

    def __init__(self, base_url: str = "http://localhost:8000/api/v3.1"):
        """初始化工具包"""
        self.cstel_parser = CSTELParser()
        self.entity_resolver = EntityResolver()
        self.api_client = APIClient(base_url)
        self.logger = logger

        # 支持的查询类型
        self.supported_query_types = ["POINT", "PROFILE", "TREND", "COMPARE", "RANK", "COMPOSE"]

        # 支持的实体类型 - 符合API设计文档v3.1
        self.supported_entity_types = ["Ship", "Port", "Province", "Basin", "ShippingRoute", "CargoType"]

        # 常用指标映射 - 符合API设计文档v3.1
        self.metric_mapping = {
            # Ship实体指标
            "有效营运率": "opRatio",
            "航次数": "voyages",
            "载重率": "loadRatio",
            "载货量": "capacity_ton",
            "周转量": "turnover_tonkm",
            "分货类运量": "cargo_ton",
            # Port实体指标
            "进港艘次": "inShipCount",
            "出港艘次": "outShipCount",
            "进港货量": "inCargo_ton",
            "出港货量": "outCargo_ton",
            "总吞吐量": "totalThroughput_ton",
            "锚泊时间": "anchorTime_days",
            # Province实体指标
            "省内货运量": "cargo_ton",
            "省内船舶数": "ship_count",
            "港口数量": "port_count",
            # Basin实体指标
            "流域货运量": "cargo_ton",
            "流域船舶数": "ship_count",
            "流域港口数": "port_count",
            # CargoType实体指标
            "运输量": "cargo_ton",
            "承运船舶数": "ship_count",
            "主要航线": "main_routes",
            "月度增长率": "growth_rate",
            # ShippingRoute实体指标
            "总货运量": "cargo_ton",
            "航行船舶数": "ship_count",
            "平均载重率": "avg_load_ratio",
            "航线利用率": "utilizationRate",
            "平均航行时间": "avg_voyage_time"
        }

    def query_shipping_data(self, query_type: str, entity: Dict[str, Any],
                          metric: Optional[str] = None, time_expression: Optional[str] = None,
                          filters: Optional[Dict[str, Any]] = None,
                          options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        统一查询长江航运数据 - 符合API设计文档v3.1

        Args:
            query_type: 查询类型 (POINT|PROFILE|TREND|COMPARE|RANK|COMPOSE)
            entity: 实体信息 {"type": "Ship", "identifier": "汉海5号"}
            metric: 业务指标名称，可选。PROFILE查询时可省略返回全部指标
            time_expression: C-STEL时间表达式，可选。省略时使用默认值（去年数据）
            filters: 可选过滤条件
            options: 可选查询选项

        Returns:
            查询结果字典
        """
        try:
            # 1. 验证输入参数
            validation_result = self._validate_query_params(query_type, entity, metric, time_expression)
            if validation_result["status"] != "success":
                return validation_result

            # 2. 处理默认时间表达式
            if not time_expression:
                current_year = datetime.now().year
                time_expression = f"Y{current_year - 1}"
                self.logger.info(f"使用默认时间表达式: {time_expression}")

            # 3. 解析时间表达式
            time_result = self.cstel_parser.parse(time_expression)
            if time_result["status"] != "success":
                return time_result

            # 4. 解析实体标识符
            entity_result = self.entity_resolver.resolve(
                entity["type"],
                entity["identifier"],
                entity.get("resolution_strategy", "fuzzy")
            )
            if entity_result["status"] != "success":
                return entity_result

            # 5. 构建API请求
            api_request = self._build_api_request(
                query_type, entity_result, metric, time_result, filters, options
            )

            # 6. 选择API端点
            endpoint = self._select_endpoint(query_type)

            # 7. 发送API请求
            api_response = self.api_client.query(endpoint, api_request)

            # 8. 处理响应
            return self._process_response(api_response, query_type, entity, metric, time_expression)

        except Exception as e:
            self.logger.error(f"查询失败: {str(e)}")
            return {
                "status": "error",
                "error": f"查询失败: {str(e)}",
                "query_params": {
                    "query_type": query_type,
                    "entity": entity,
                    "metric": metric,
                    "time_expression": time_expression
                }
            }

    def _validate_query_params(self, query_type: str, entity: Dict[str, Any],
                             metric: Optional[str], time_expression: Optional[str]) -> Dict[str, Any]:
        """验证查询参数 - 符合API设计文档v3.1"""
        # 验证查询类型
        if query_type not in self.supported_query_types:
            return {
                "status": "error",
                "error": f"不支持的查询类型: {query_type}，支持的类型: {self.supported_query_types}"
            }

        # 验证实体类型
        if "type" not in entity or entity["type"] not in self.supported_entity_types:
            return {
                "status": "error",
                "error": f"不支持的实体类型: {entity.get('type')}，支持的类型: {self.supported_entity_types}"
            }

        # 验证实体标识符
        if "identifier" not in entity:
            return {
                "status": "error",
                "error": "缺少实体标识符"
            }

        # 根据API设计文档v3.1，metric和time_expression都是可选的
        # PROFILE查询时metric可以为空，返回全部指标
        if query_type == "PROFILE" and not metric:
            self.logger.info("PROFILE查询未指定metric，将返回全部指标")

        # 如果没有指定时间表达式，使用默认值（去年数据）
        if not time_expression:
            current_year = datetime.now().year
            default_time = f"Y{current_year - 1}"
            self.logger.info(f"未指定时间表达式，使用默认值: {default_time}")

        return {"status": "success"}

    def _build_api_request(self, query_type: str, entity_result: Dict,
                          metric: Optional[str], time_result: Dict,
                          filters: Optional[Dict], options: Optional[Dict]) -> Dict[str, Any]:
        """构建API请求 - 符合API设计文档v3.1"""
        request = {
            "query_type": query_type,
            "entity": {
                "type": entity_result["entity_type"],
                "identifier": entity_result["identifier"],
                "resolved_entities": entity_result["resolved_entities"]
            },
            "time_expression": time_result.get("periods", []),
            "time_metadata": {
                "original_expression": time_result.get("expression"),
                "granularity": time_result.get("granularity"),
                "db_mapping": time_result.get("db_mapping")
            }
        }

        # metric是可选的，PROFILE查询时可以省略
        if metric:
            request["metric"] = metric

        if filters:
            request["filters"] = filters
        if options:
            request["options"] = options

        return request

    def _select_endpoint(self, query_type: str) -> str:
        """选择API端点"""
        if query_type in ["POINT", "PROFILE", "TREND"]:
            return "core/query"
        else:
            return "analytics/query"

    def _process_response(self, api_response: Dict, query_type: str,
                         entity: Dict, metric: Optional[str], time_expression: str) -> Dict[str, Any]:
        """处理API响应 - 符合API设计文档v3.1"""
        if api_response.get("status") == "success":
            # 添加查询元数据
            api_response["query_metadata"] = {
                "query_type": query_type,
                "entity": entity,
                "metric": metric,
                "time_expression": time_expression,
                "timestamp": datetime.now().isoformat()
            }

        return api_response

    def search_entities(self, entity_type: str, search_term: str,
                       limit: int = 10) -> Dict[str, Any]:
        """
        搜索实体

        Args:
            entity_type: 实体类型
            search_term: 搜索关键词
            limit: 返回数量限制

        Returns:
            搜索结果
        """
        try:
            request = {
                "entity_type": entity_type,
                "search_term": search_term,
                "limit": limit,
                "include_similarity": True
            }

            response = self.api_client.query("search/entities", request)
            return response

        except Exception as e:
            self.logger.error(f"实体搜索失败: {str(e)}")
            return {
                "status": "error",
                "error": f"实体搜索失败: {str(e)}",
                "search_params": {
                    "entity_type": entity_type,
                    "search_term": search_term
                }
            }

    def get_metadata(self, metadata_type: str) -> Dict[str, Any]:
        """
        获取元数据

        Args:
            metadata_type: 元数据类型 (entities|metrics|cargo_types|ship_types)

        Returns:
            元数据信息
        """
        try:
            response = self.api_client.query(f"metadata/{metadata_type}", {})
            return response

        except Exception as e:
            self.logger.error(f"获取元数据失败: {str(e)}")
            return {
                "status": "error",
                "error": f"获取元数据失败: {str(e)}",
                "metadata_type": metadata_type
            }

    def get_realtime_data(self, entity_type: str, entity_id: str) -> Dict[str, Any]:
        """
        获取实时数据

        Args:
            entity_type: 实体类型
            entity_id: 实体ID

        Returns:
            实时数据
        """
        try:
            response = self.api_client.query(f"realtime/{entity_type}/{entity_id}", {})
            return response

        except Exception as e:
            self.logger.error(f"获取实时数据失败: {str(e)}")
            return {
                "status": "error",
                "error": f"获取实时数据失败: {str(e)}",
                "entity_type": entity_type,
                "entity_id": entity_id
            }


# 创建全局工具包实例
shipping_toolkit = ShippingDataToolKit()

# 导出主要工具函数
def query_shipping_data(query_type: str, entity: Dict[str, Any],
                       metric: Optional[str] = None, time_expression: Optional[str] = None,
                       filters: Optional[Dict[str, Any]] = None,
                       options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    查询长江航运数据 - 统一查询接口

    支持的查询类型:
    - POINT: 单点查询 (查询特定实体在特定时间的指标值)
    - PROFILE: 画像查询 (获取实体的完整画像数据)
    - TREND: 趋势分析 (分析指标的时间序列变化)
    - COMPARE: 对比分析 (对比多个实体的指标表现)
    - RANK: 排名分析 (按指标对实体进行排序)
    - COMPOSE: 构成分析 (分析实体的内部构成)

    支持的实体类型:
    - Ship: 船舶
    - Port: 港口
    - Province: 省份
    - Basin: 流域
    - ShippingRoute: 航线

    Args:
        query_type: 查询类型
        entity: 实体信息，格式: {"type": "Ship", "identifier": "汉海5号"}
        metric: 业务指标名称，如"有效营运率"、"总吞吐量"等
        time_expression: C-STEL时间表达式，如"M202506"、"M202501_M202506"等
        filters: 可选的过滤条件
        options: 可选的查询选项

    Returns:
        查询结果字典
    """
    return shipping_toolkit.query_shipping_data(
        query_type, entity, metric, time_expression, filters, options
    )

def search_entities(entity_type: str, search_term: str, limit: int = 10) -> Dict[str, Any]:
    """
    搜索实体

    Args:
        entity_type: 实体类型 (Ship|Port|Province|Basin|ShippingRoute)
        search_term: 搜索关键词
        limit: 返回数量限制

    Returns:
        搜索结果
    """
    return shipping_toolkit.search_entities(entity_type, search_term, limit)

def get_metadata(metadata_type: str) -> Dict[str, Any]:
    """
    获取系统元数据

    Args:
        metadata_type: 元数据类型 (entities|metrics|cargo_types|ship_types)

    Returns:
        元数据信息
    """
    return shipping_toolkit.get_metadata(metadata_type)

def get_realtime_data(entity_type: str, entity_id: str) -> Dict[str, Any]:
    """
    获取实体实时数据

    Args:
        entity_type: 实体类型
        entity_id: 实体ID

    Returns:
        实时数据
    """
    return shipping_toolkit.get_realtime_data(entity_type, entity_id)


# 工具函数注册表
TOOL_REGISTRY: Dict[str, Any] = {
    "query_shipping_data": query_shipping_data,
    "search_entities": search_entities,
    "get_metadata": get_metadata,
    "get_realtime_data": get_realtime_data,
}

# LLM Function Calling 配置 - 基于API设计文档v3.1
FUNCTION_SCHEMAS: List[Dict[str, Any]] = [
    {
        "type": "function",
        "function": {
            "name": "query_shipping_data",
            "description": "查询长江航运数据的统一接口，支持单点查询、画像查询、趋势分析、对比分析、排名分析和构成分析。基于C-STEL时间表达语言和多实体类型支持。",
            "parameters": {
                "type": "object",
                "properties": {
                    "query_type": {
                        "type": "string",
                        "enum": ["POINT", "PROFILE", "TREND", "COMPARE", "RANK", "COMPOSE"],
                        "description": "查询类型：POINT(单点查询-查询具体数值)、PROFILE(画像查询-获取详细情况)、TREND(趋势分析-分析变化趋势)、COMPARE(对比分析-对比多个实体)、RANK(排名分析-按指标排序)、COMPOSE(构成分析-分析内部构成)"
                    },
                    "entity": {
                        "type": "object",
                        "properties": {
                            "type": {
                                "type": "string",
                                "enum": ["Ship", "Port", "Province", "Basin", "ShippingRoute", "CargoType"],
                                "description": "实体类型：Ship(船舶)、Port(港口)、Province(省份)、Basin(流域)、ShippingRoute(航线)、CargoType(货物类型)"
                            },
                            "identifier": {
                                "type": ["string", "array"],
                                "description": "实体标识符，可以是单个实体名称或多个实体名称的数组。船舶如'汉海5号'，港口如'武汉港'，航线如'武汉-南京航线'，货物如'煤炭及制品'"
                            },
                            "resolution_strategy": {
                                "type": "string",
                                "enum": ["exact", "fuzzy", "multi"],
                                "description": "实体解析策略，默认为fuzzy(模糊匹配)"
                            }
                        },
                        "required": ["type", "identifier"]
                    },
                    "metric": {
                        "type": "string",
                        "description": "业务指标名称，如'有效营运率'、'总吞吐量'、'进港艘次'、'出港艘次'、'总货运量'、'航线利用率'、'载重率'、'周转量'、'运输量'等。PROFILE查询时可省略，返回全部指标"
                    },
                    "time_expression": {
                        "type": "string",
                        "description": "C-STEL时间表达式。格式：Y2024(年)、Q2024Q1(季度)、M202506(月)、M202501_M202506(月份区间)、Y2024,Y2025(年份列表)、R6M(最近6个月)等。可省略，默认使用去年数据"
                    },
                    "filters": {
                        "type": "object",
                        "properties": {
                            "cargo_type": {"type": "string", "description": "货物类型过滤，如'煤炭及制品'、'金属矿石'"},
                            "ship_type": {"type": "string", "description": "船舶类型过滤，如'散货船'、'集装箱船'"},
                            "route": {"type": "string", "description": "航线过滤，如'武汉-南京'、'武汉-上海'"},
                            "port": {"type": "string", "description": "港口过滤，如'武汉港'、'南京港'"}
                        },
                        "description": "可选的过滤条件，用于细化查询范围"
                    }
                },
                "required": ["query_type", "entity"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "search_entities",
            "description": "搜索长江航运实体，支持船舶、港口、省份、流域、航线的模糊搜索，返回匹配的实体列表及相似度评分。",
            "parameters": {
                "type": "object",
                "properties": {
                    "entity_type": {
                        "type": "string",
                        "enum": ["Ship", "Port", "Province", "Basin", "ShippingRoute", "CargoType"],
                        "description": "要搜索的实体类型"
                    },
                    "search_term": {
                        "type": "string",
                        "description": "搜索关键词，支持部分匹配"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "返回结果数量限制，默认10",
                        "default": 10
                    }
                },
                "required": ["entity_type", "search_term"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_metadata",
            "description": "获取系统元数据信息，包括支持的实体类型、指标列表、货物分类、船舶类型等。",
            "parameters": {
                "type": "object",
                "properties": {
                    "metadata_type": {
                        "type": "string",
                        "enum": ["entities", "metrics", "cargo_types", "ship_types"],
                        "description": "元数据类型：entities(实体类型)、metrics(指标列表)、cargo_types(货物分类)、ship_types(船舶类型)"
                    }
                },
                "required": ["metadata_type"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_realtime_data",
            "description": "获取实体的实时状态数据，如船舶AIS位置、港口实时状态等。",
            "parameters": {
                "type": "object",
                "properties": {
                    "entity_type": {
                        "type": "string",
                        "enum": ["Ship", "Port"],
                        "description": "实体类型，目前支持Ship(船舶)和Port(港口)"
                    },
                    "entity_id": {
                        "type": "string",
                        "description": "实体唯一标识符，如船舶MMSI或港口ID"
                    }
                },
                "required": ["entity_type", "entity_id"]
            }
        }
    }
]


# 使用示例和测试函数
def _test_cstel_parser():
    """测试C-STEL解析器"""
    parser = CSTELParser("2025-07-30")

    test_cases = [
        "Y2024",
        "Q2024Q1",
        "M202506",
        "M202501_M202506",
        "R6M",
        "Y2024,Y2025"
    ]

    for case in test_cases:
        result = parser.parse(case)
        print(f"表达式: {case}")
        print(f"结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        print("-" * 50)


if __name__ == "__main__":
    # 运行测试
    print("=== C-STEL解析器测试 ===")
    _test_cstel_parser()

    print("\n=== 工具函数测试 ===")
    # 测试POINT查询
    test_result = query_shipping_data(
        query_type="POINT",
        entity={"type": "Ship", "identifier": "汉海5号"},
        metric="有效营运率",
        time_expression="M202506"
    )
    print(f"POINT查询测试结果: {json.dumps(test_result, ensure_ascii=False, indent=2)}")

    # 测试PROFILE查询（无metric参数）
    profile_result = query_shipping_data(
        query_type="PROFILE",
        entity={"type": "Ship", "identifier": "汉海5号"}
    )
    print(f"\nPROFILE查询测试结果: {json.dumps(profile_result, ensure_ascii=False, indent=2)}")

    # 测试CargoType查询
    cargo_result = query_shipping_data(
        query_type="TREND",
        entity={"type": "CargoType", "identifier": "煤炭及制品"},
        metric="运输量",
        time_expression="M202501_M202506"
    )
    print(f"\nCargoType查询测试结果: {json.dumps(cargo_result, ensure_ascii=False, indent=2)}")

    print("\n=== 工具注册表 ===")
    print(f"已注册工具: {list(TOOL_REGISTRY.keys())}")

    print("\n=== Function Schemas ===")
    print(f"LLM工具数量: {len(FUNCTION_SCHEMAS)}")
    for schema in FUNCTION_SCHEMAS:
        print(f"- {schema['function']['name']}: {schema['function']['description'][:50]}...")
