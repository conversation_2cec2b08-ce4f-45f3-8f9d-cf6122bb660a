from __future__ import annotations
from typing import List, Dict, Any, Optional
from abc import ABC, abstractmethod

import requests
from loguru import logger
from ..config import settings


class BaseLLMClient(ABC):
    """LLM客户端抽象基类"""
    
    def __init__(self, model: Optional[str] = None, timeout: int = 60):
        self.model = model
        self.timeout = timeout
        self.base_url = ""
    
    @abstractmethod
    def chat(self, messages: List[Dict[str, str]], temperature: float = 0, tools: Optional[List[Dict]] = None) -> Any:
        """统一的对话接口"""
        pass


class QwenCloudClient(BaseLLMClient):
    """千问云端客户端"""
    
    def __init__(self, model: Optional[str] = None, timeout: int = 60):
        super().__init__(model, timeout)
        
        # 从配置文件读取配置
        if not settings or not settings.llm:
            raise EnvironmentError("配置系统初始化失败，请检查config.env文件是否存在")
        
        self.api_key = settings.llm.qwen_api_key
        self.base_url = settings.llm.qwen_api_base_url
        self.model = model or settings.llm.qwen_model_name
        print(f"千问云端客户端初始化: {self.api_key}, {self.base_url}, {self.model}")

    def chat(self, messages: List[Dict[str, str]], temperature: float = 0, tools: Optional[List[Dict]] = None) -> Any:
        payload = {
            "model": self.model,
            "input": {"messages": messages},
            "parameters": {
                "temperature": temperature,
                "result_format": "message"  # 改为message格式以支持function calling
            },
        }

        # 如果提供了tools，添加到parameters中
        if tools:
            payload["parameters"]["tools"] = tools

        logger.debug(f"Qwen 请求载荷: {payload}")
        resp = requests.post(
            self.base_url,
            headers={"Authorization": f"Bearer {self.api_key}"},
            json=payload,
            timeout=self.timeout,
        )
        if resp.status_code != 200:
            logger.error(f"Qwen API 请求失败 {resp.status_code}: {self.api_key}")
            raise RuntimeError(f"Qwen API error {resp.status_code}")

        data = resp.json()
        logger.debug(f"Qwen API 原始返回: {data}")
        
        try:
            if "output" in data and "choices" in data["output"]:
                choice = data["output"]["choices"][0]
                message = choice["message"]
                
                logger.debug(f"处理消息: {message}")
                
                # 检查是否有function call
                if "tool_calls" in message:
                    tool_calls = message["tool_calls"]
                    logger.debug(f"检测到tool_calls: {tool_calls}")
                    
                    if tool_calls and len(tool_calls) > 0:
                        # 返回function call信息
                        tool_call = tool_calls[0]
                        result = {
                            "function_call": {
                                "name": tool_call["function"]["name"],
                                "arguments": tool_call["function"]["arguments"]
                            }
                        }
                        logger.debug(f"返回function call: {result}")
                        return result
                        
                # 普通文本回复
                content = message.get("content", "")
                
                # 过滤掉 <think>...</think> 内容
                import re
                content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)
                content = content.strip()
                
                logger.debug(f"返回普通文本: {content}")
                return content
            else:
                raise ValueError("无法识别的返回格式")
        except Exception as e:
            logger.error(f"无法解析 Qwen 返回: {e} | 原始: {data}")
            raise


class LocalOpenAIClient(BaseLLMClient):
    """本地OpenAI兼容客户端"""
    
    def __init__(self, model: Optional[str] = None, timeout: int = 60):
        super().__init__(model, timeout)
        
        # 从配置文件读取配置
        if not settings or not settings.llm:
            raise EnvironmentError("配置系统初始化失败，请检查config.env文件是否存在")
        
        self.base_url = settings.llm.local_base_url
        self.model = model or settings.llm.local_model_name
        self.api_key = settings.llm.local_api_key
        
        # 检测API类型
        self.api_type = self._detect_api_type()
        print(f"本地OpenAI客户端初始化: {self.base_url}, {self.model}, API类型: {self.api_type}")

    def _detect_api_type(self) -> str:
        """检测API类型"""
        try:
            # 检查是否支持标准OpenAI API
            response = requests.get(f"{self.base_url.rstrip('/')}/v1/models", timeout=5)
            if response.status_code == 200:
                return "openai"
            
            # 检查是否支持/generate端点
            response = requests.get(f"{self.base_url.rstrip('/')}/health", timeout=5)
            if response.status_code == 200:
                return "vllm_generate"
            
            return "unknown"
        except:
            return "unknown"

    def _messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """将消息列表转换为单个提示字符串"""
        prompt_parts = []
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            
            if role == "system":
                prompt_parts.append(f"System: {content}")
            elif role == "user":
                prompt_parts.append(f"User: {content}")
            elif role == "assistant":
                prompt_parts.append(f"Assistant: {content}")
        
        # 添加Assistant提示
        prompt_parts.append("Assistant:")
        return "\n".join(prompt_parts)

    def chat(self, messages: List[Dict[str, str]], temperature: float = 0, tools: Optional[List[Dict]] = None) -> Any:
        if self.api_type == "openai":
            return self._chat_openai(messages, temperature, tools)
        elif self.api_type == "vllm_generate":
            return self._chat_vllm_generate(messages, temperature, tools)
        else:
            # 默认尝试OpenAI格式
            try:
                return self._chat_openai(messages, temperature, tools)
            except:
                return self._chat_vllm_generate(messages, temperature, tools)

    def _chat_openai(self, messages: List[Dict[str, str]], temperature: float = 0, tools: Optional[List[Dict]] = None) -> Any:
        """使用标准OpenAI API格式"""
        # OpenAI兼容格式
        payload = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
        }

        # 如果提供了tools，添加到payload中
        if tools:
            payload["tools"] = tools

        logger.debug(f"本地OpenAI 请求载荷: {payload}")
        
        # 构建请求头
        headers = {"Content-Type": "application/json"}
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
        
        # 构建完整的API端点
        url = f"{self.base_url.rstrip('/')}/v1/chat/completions"
        
        resp = requests.post(
            url,
            headers=headers,
            json=payload,
            timeout=self.timeout,
        )
        
        # 记录是否进行了工具调用降级
        tools_downgraded = False
        
        # 如果是工具调用相关错误，尝试不使用工具重新请求
        if resp.status_code == 400 and tools:
            error_text = resp.text
            if "tool" in error_text.lower() or "auto" in error_text.lower():
                logger.warning(f"工具调用失败，尝试不使用工具重新请求: {error_text}")
                tools_downgraded = True
                # 重新请求，不使用工具
                payload_no_tools = {
                    "model": self.model,
                    "messages": messages,
                    "temperature": temperature,
                }
                resp = requests.post(
                    url,
                    headers=headers,
                    json=payload_no_tools,
                    timeout=self.timeout,
                )
        
        if resp.status_code != 200:
            logger.error(f"本地OpenAI API 请求失败 {resp.status_code}: {resp.text}")
            raise RuntimeError(f"本地OpenAI API error {resp.status_code}: {resp.text}")

        data = resp.json()
        logger.debug(f"本地OpenAI API 原始返回: {data}")
        
        try:
            if "choices" in data and len(data["choices"]) > 0:
                choice = data["choices"][0]
                message = choice["message"]
                
                logger.debug(f"处理消息: {message}")
                
                # 检查是否有function call
                if "tool_calls" in message and message["tool_calls"]:
                    tool_calls = message["tool_calls"]
                    logger.debug(f"检测到tool_calls: {tool_calls}")
                    
                    if len(tool_calls) > 0:
                        # 返回function call信息
                        tool_call = tool_calls[0]
                        result = {
                            "function_call": {
                                "name": tool_call["function"]["name"],
                                "arguments": tool_call["function"]["arguments"]
                            }
                        }
                        logger.debug(f"返回function call: {result}")
                        return result
                        
                # 普通文本回复
                content = message.get("content", "")
                
                # 过滤掉 <think>...</think> 内容
                import re
                content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)
                content = content.strip()
                
                logger.debug(f"返回普通文本: {content}")
                
                # 如果原来请求了工具但降级了，返回特殊格式表示没有function_call
                if tools_downgraded:
                    logger.info("工具调用降级成功，返回文本内容")
                    # 返回一个dict格式但不包含function_call，让agent_x.py知道没有工具调用
                    return {
                        "content": content,
                        "function_call": None,
                        "downgraded": True
                    }
                
                return content
            else:
                raise ValueError("无法识别的返回格式")
        except Exception as e:
            logger.error(f"无法解析本地OpenAI返回: {e} | 原始: {data}")
            raise

    def _chat_vllm_generate(self, messages: List[Dict[str, str]], temperature: float = 0, tools: Optional[List[Dict]] = None) -> Any:
        """使用vLLM的/generate端点"""
        # 在vLLM generate模式下，如果有工具调用请求，先尝试生成文本回复
        has_tools = tools is not None and len(tools) > 0
        if has_tools:
            logger.warning("vLLM Generate模式不支持工具调用，将生成文本回复")
        
        # 将消息转换为提示
        prompt = self._messages_to_prompt(messages)
        
        # vLLM generate格式
        payload = {
            "model": self.model,
            "prompt": prompt,
            "temperature": temperature,
            "max_tokens": 1000,  # 默认最大token数
            "stop": ["User:", "System:"],  # 停止词
        }

        logger.debug(f"vLLM Generate 请求载荷: {payload}")
        
        # 构建请求头
        headers = {"Content-Type": "application/json"}
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
        
        # 构建完整的API端点
        url = f"{self.base_url.rstrip('/')}/generate"
        
        resp = requests.post(
            url,
            headers=headers,
            json=payload,
            timeout=self.timeout,
        )
        
        if resp.status_code != 200:
            logger.error(f"vLLM Generate API 请求失败 {resp.status_code}: {resp.text}")
            raise RuntimeError(f"vLLM Generate API error {resp.status_code}: {resp.text}")

        data = resp.json()
        logger.debug(f"vLLM Generate API 原始返回: {data}")
        
        try:
            # vLLM generate API的返回格式可能不同，需要适配
            if "text" in data:
                content = data["text"]
            elif "choices" in data and len(data["choices"]) > 0:
                content = data["choices"][0].get("text", "")
            elif "generated_text" in data:
                content = data["generated_text"]
            else:
                # 如果格式不识别，尝试获取第一个字符串值
                content = str(data)
            
            # 过滤掉 <think>...</think> 内容
            import re
            content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)
            content = content.strip()
            
            logger.debug(f"返回普通文本: {content}")
            
            # 如果原来请求了工具但vLLM不支持，返回特殊格式表示没有function_call
            if has_tools:
                logger.info("vLLM Generate模式不支持工具调用，返回文本内容")
                # 返回一个dict格式但不包含function_call，让agent_x.py知道没有工具调用
                return {
                    "content": content,
                    "function_call": None,
                    "downgraded": True
                }
            
            return content
        except Exception as e:
            logger.error(f"无法解析vLLM Generate返回: {e} | 原始: {data}")
            raise


_llm_client_instance: Optional[BaseLLMClient] = None


def get_qwen_client() -> BaseLLMClient:
    """获取LLM客户端实例（工厂方法）"""
    global _llm_client_instance
    if _llm_client_instance is None:
        if not settings or not settings.llm:
            raise EnvironmentError("配置系统初始化失败，请检查config.env文件是否存在")
        
        provider = settings.llm.provider
        logger.info(f"初始化LLM客户端，提供商: {provider}")
        
        if provider == "local_openai":
            _llm_client_instance = LocalOpenAIClient()
        elif provider == "qwen_cloud":
            _llm_client_instance = QwenCloudClient()
        else:
            logger.warning(f"未知的LLM提供商: {provider}，使用默认的千问云端客户端")
            _llm_client_instance = QwenCloudClient()
    
    return _llm_client_instance 