from __future__ import annotations

import json
from pathlib import Path
from typing import Dict, Any, List

from loguru import logger

from .llm import get_qwen_client

# ──────────────────────────────────────────────────────────────
# 常量
# ──────────────────────────────────────────────────────────────
PROMPT_FILE = Path(__file__).parent.parent / "prompt" / "nlu_tool_prompt.txt"

# 需要快速路径的意图集合：无需查询知识图谱，或可直接走缓存
QUICK_INTENTS = {
    "0.Help-FAQ",
    "0.Chitchat",
    "0.Reset",
    "8.MetricDef",
    "8.EntityInfo",
}


class NLU:  # noqa: D101
    def __init__(self):
        self.llm_client = get_qwen_client()
        self.system_prompt = self._load_prompt()

    def _load_prompt(self) -> str:
        if not PROMPT_FILE.exists():
            raise FileNotFoundError(f"NLU prompt 文件不存在: {PROMPT_FILE}")
        return PROMPT_FILE.read_text(encoding="utf-8")

    # ──────────────────────────────────────────────────────────
    # Public API
    # ──────────────────────────────────────────────────────────
    def process(self, question: str, context: str | None = None) -> Dict[str, Any]:
        """核心入口：给用户问题，返回统一 NLU 结果。"""
        messages: List[Dict[str, str]] = [
            {"role": "system", "content": self.system_prompt},
        ]

        if context:
            context_block = (
                "<CONTEXT>\n"  # 显式标记，方便 few-shot 示范
                f"{context.strip()}\n"
                "</CONTEXT>"
            )
            messages.append({"role": "system", "content": context_block})

        messages.append({"role": "user", "content": question})

        try:
            raw_output = self.llm_client.chat(messages, temperature=0)
            logger.debug(f"NLU LLM raw output: {raw_output}")
            parsed = json.loads(raw_output)
        except json.JSONDecodeError as e:
            logger.error(f"NLU JSON 解析失败: {e}; 原始输出: {raw_output}")
            raise
        except Exception as e:
            logger.error(f"NLU 调用失败: {e}")
            raise

        intent = parsed.get("intent", "general")
        entities = parsed.get("entities", {}) or {}
        tool_call = parsed.get("tool_call", {}) or {}

        mode = "quick" if intent in QUICK_INTENTS else "full"
        cache_key = None
        try:
            if entities.get("ports"):
                cache_key = entities["ports"][0]
        except Exception:
            pass

        # 判断是否需要实时数据 - 优先使用tool_call的判断
        realtime = tool_call.get("needed", False)
        
        # 🔄 如果tool_call没有判断需要，使用传统逻辑判断
        if not realtime:
            realtime = self._is_realtime_query(question, entities, intent)
        
        if intent.startswith("8."):
            realtime = False

        nlu_result = {
            "entities": entities,
            "intent": intent,
            "mode": mode,
            "cache_key": cache_key,
            "realtime": realtime,
            "tool_call": tool_call,  # 新增工具调用信息
        }
        return nlu_result
    
    def _is_realtime_query(self, question: str, entities: Dict[str, Any], intent: str) -> bool:
        """判断是否为实时查询"""
        # 船舶位置相关关键词
        ship_keywords = ["船舶", "船只", "船", "货船", "客船", "轮船"]
        position_keywords = ["位置", "在哪", "哪里", "坐标", "经纬度", "停靠"]
        realtime_keywords = [
            "现在", "当前", "实时", "最新", 
            "目前", "此刻", "即时"
        ]
        
        question_lower = question.lower()
        
        # 必须包含船舶相关词汇才考虑实时查询
        has_ship_keyword = any(keyword in question_lower for keyword in ship_keywords)
        if not has_ship_keyword:
            return False
        
        # 包含位置关键词
        has_position_keyword = any(keyword in question_lower for keyword in position_keywords)
        
        # 包含实时关键词
        has_realtime_keyword = any(keyword in question_lower for keyword in realtime_keywords)
        
        # 船舶+位置+实时 或者 船舶+位置 都认为是实时查询
        if has_position_keyword and (has_realtime_keyword or True):  # 只要涉及船舶位置就走实时
            logger.info(f"检测到船舶位置查询")
            return True
            
        # 基于实体和意图的判断
        metrics: List[str] = [str(m) for m in entities.get("metrics", [])]
        times: List[str] = [str(t) for t in entities.get("times", [])]
        
        # 若指标或时间表达出现"实时"等字样，则视为实时查询
        if any("实时" in m for m in metrics):
            return True
        elif any(t in ["实时", "现在", "当前", "此刻", "即时", "最新"] for t in times):
            return True
        # 船舶实体 + PointQuery 场景默认走实时
        elif entities.get("ships") and intent.startswith("1."):
            return True
        # 如果指标包含 AIS 或者指标为 ALL 且存在船舶实体，默认走实时
        elif any("ais" in m.lower() for m in metrics):
            return True
        elif metrics == ["ALL"] and entities.get("ships") and not intent.startswith("8."):
            return True
        # 船舶实体 + 指标包含"位置"
        elif entities.get("ships") and any("位置" in m for m in metrics):
            return True
            
        return False

    def process_question(self, question: str) -> Dict[str, Any]:
        """向后兼容旧测试脚本里的方法名"""
        return self.process(question)


# 兼容旧接口的默认实例
default_nlu = NLU()

# Backward compatibility for legacy tests and imports
ShippingNLU = NLU
