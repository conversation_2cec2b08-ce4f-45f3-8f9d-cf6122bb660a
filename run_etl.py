from typing import Dict, Any, Optional
from datetime import datetime
from loguru import logger
from concurrent.futures import ThreadPoolExecutor, as_completed
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))
from src.kgnode.database import DatabaseManager
from src.kgnode.node_static import StaticNodeManager
from src.kgnode.node_route import RouteETL
from src.kgnode.node_ship import ShipDynamicETL
from src.kgnode.node_port import PortDynamicETL
from src.kgnode.time_aggregator import TimeAggregator


class ETLManager:
    """ETL流程管理器 - 简化版"""
    
    def __init__(self, database_manager: DatabaseManager = None):
        """
        初始化ETL管理器
        
        Args:
            database_manager: 数据库管理器实例
        """
        self.db_manager = database_manager or DatabaseManager()
        
        # 初始化各个ETL组件
        self.static_manager = StaticNodeManager(self.db_manager)
        self.route_etl = RouteETL(self.db_manager, batch_size=5000)
        self.ship_etl = ShipDynamicETL(self.db_manager, batch_size=5000)
        self.port_etl = PortDynamicETL(self.db_manager, batch_size=5000)
        self.time_aggregator = TimeAggregator(self.db_manager)
    
    def setup_static_nodes(self) -> Dict[str, Any]:
        """
        设置静态节点
        
        Returns:
            Dict: 静态节点创建结果
        """
        logger.info("开始设置静态节点")
        return self.static_manager.create_all_static_nodes()
    
    def load_ship_data(self, ship_filter: str = None, time_filter: str = None, limit: int = 10000) -> Dict[str, Any]:
        """
        加载船舶数据
        
        Args:
            ship_filter: 船舶过滤条件
            time_filter: 时间过滤条件
            limit: 记录数限制
            
        Returns:
            Dict: 船舶数据加载结果
        """
        logger.info("开始加载船舶数据")
        
        results = {
            "ships": {"created": 0, "errors": 0},
            "ship_stats": {"created": 0, "errors": 0},
            "total_created": 0,
            "total_errors": 0,
            "success": True
        }
        
        try:
            # 1. 加载船舶档案
            logger.info("1. 加载船舶档案")
            ship_result = self.ship_etl.load_ships(ship_filter, time_filter, limit)
            results["ships"] = {
                "created": ship_result.get("ships", 0),
                "errors": 0,  # load_ships方法目前不返回错误计数
                "ship_type_relations": ship_result.get("ship_type_relations", 0)
            }

            # 2. 加载船舶统计
            logger.info("2. 加载船舶统计")
            stats_result = self.ship_etl.execute_etl(ship_filter, time_filter, limit)
            results["ship_stats"] = {
                "created": stats_result.get("total_created", 0),
                "errors": stats_result.get("total_errors", 0)
            }

            # 汇总结果
            results["total_created"] = results["ships"]["created"] + results["ship_stats"]["created"]
            results["total_errors"] = results["ships"]["errors"] + results["ship_stats"]["errors"]
            results["success"] = results["total_errors"] == 0
            
        except Exception as e:
            logger.error(f"加载船舶数据失败: {e}")
            results["success"] = False
            results["error"] = str(e)
        
        return results

    def load_route_data(self, ship_filter: str = None, time_filter: str = None, limit: int = 10000) -> Dict[str, Any]:
        """
        加载航线数据

        Args:
            ship_filter: 船舶过滤条件
            time_filter: 时间过滤条件
            limit: 记录数限制

        Returns:
            Dict: 航线数据加载结果
        """
        logger.info("开始加载航线数据")

        try:
            logger.info(f"调用航线ETL，参数: ship_filter={ship_filter}, time_filter={time_filter}, limit={limit}")
            result = self.route_etl.execute_full_route_etl(ship_filter, time_filter, limit)
            logger.info(f"航线ETL执行完成，结果: {result}")
            result["success"] = result.get("total_errors", 0) == 0
            return result

        except Exception as e:
            logger.error(f"加载航线数据失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return {
                "success": False,
                "error": str(e),
                "total_created": 0,
                "total_errors": 1
            }

    def load_port_data(self, port_filter: str = None, time_filter: str = None, limit: int = 10000) -> Dict[str, Any]:
        """
        加载港口数据
        
        Args:
            port_filter: 港口过滤条件
            time_filter: 时间过滤条件
            limit: 记录数限制
            
        Returns:
            Dict: 港口数据加载结果
        """
        logger.info("开始加载港口数据")
        
        try:
            result = self.port_etl.execute_etl(port_filter, time_filter, limit)
            result["success"] = result.get("total_errors", 0) == 0
            return result
            
        except Exception as e:
            logger.error(f"加载港口数据失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "total_created": 0,
                "total_errors": 1
            }
    
    def aggregate_time_data(self, year: int = 2024) -> Dict[str, Any]:
        """
        聚合时间维度数据
        
        Args:
            year: 目标年份
            
        Returns:
            Dict: 时间聚合结果
        """
        logger.info(f"开始聚合{year}年时间维度数据")
        
        try:
            return self.time_aggregator.execute_full_aggregation(year)
            
        except Exception as e:
            logger.error(f"时间数据聚合失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "total_created": 0,
                "total_errors": 1
            }
    
    def run_full_etl(self, 
                     ship_filter: str = None, 
                     port_filter: str = None, 
                     time_filter: str = None, 
                     limit: int = 10000,
                     parallel: bool = True) -> Dict[str, Any]:
        """
        执行完整的ETL流程
        
        Args:
            ship_filter: 船舶过滤条件
            port_filter: 港口过滤条件
            time_filter: 时间过滤条件
            limit: 记录数限制
            parallel: 是否并行执行
            
        Returns:
            Dict: 完整ETL结果
        """
        logger.info("开始执行完整ETL流程")
        start_time = datetime.now()
        
        results = {
            "static_nodes": {},
            "route_data": {},
            "ship_data": {},
            "port_data": {},
            "time_aggregation": {},
            "total_created": 0,
            "total_errors": 0,
            "execution_time": 0,
            "success": True
        }
        
        try:
            # 1. 设置静态节点
            logger.info("=== 阶段1: 设置静态节点 ===")
            results["static_nodes"] = self.setup_static_nodes()

            # 2. 加载航线数据（必须在船舶数据之前）
            logger.info("=== 阶段2: 加载航线数据 ===")
            results["route_data"] = self.load_route_data(ship_filter, time_filter, limit)

            # 3. 加载动态数据
            logger.info("=== 阶段3: 加载动态数据 ===")
            if parallel:
                # 并行加载船舶和港口数据
                with ThreadPoolExecutor(max_workers=2) as executor:
                    ship_future = executor.submit(self.load_ship_data, ship_filter, time_filter, limit)
                    port_future = executor.submit(self.load_port_data, port_filter, time_filter, limit)

                    results["ship_data"] = ship_future.result()
                    results["port_data"] = port_future.result()
            else:
                # 顺序加载
                results["ship_data"] = self.load_ship_data(ship_filter, time_filter, limit)
                results["port_data"] = self.load_port_data(port_filter, time_filter, limit)

            # 4. 时间维度聚合
            logger.info("=== 阶段4: 时间维度聚合 ===")
            results["time_aggregation"] = self.aggregate_time_data(2024)
            
            # 汇总结果
            for stage in ["static_nodes", "route_data", "ship_data", "port_data", "time_aggregation"]:
                stage_result = results[stage]
                if isinstance(stage_result, dict):
                    results["total_created"] += stage_result.get("total_created", 0)
                    results["total_errors"] += stage_result.get("total_errors", 0)
            
            results["execution_time"] = (datetime.now() - start_time).total_seconds()
            results["success"] = results["total_errors"] == 0
            
            # 记录最终结果
            if results["success"]:
                logger.info(f"✅ 完整ETL流程成功: 创建 {results['total_created']} 个节点, "
                           f"耗时 {results['execution_time']:.2f} 秒")
            else:
                logger.error(f"❌ 完整ETL流程失败: 创建 {results['total_created']} 个节点, "
                            f"{results['total_errors']} 个错误")
            
        except Exception as e:
            logger.error(f"完整ETL流程异常: {e}")
            results["success"] = False
            results["error"] = str(e)
            results["execution_time"] = (datetime.now() - start_time).total_seconds()
        
        return results
    
    def get_system_statistics(self) -> Dict[str, Any]:
        """
        获取系统统计信息
        
        Returns:
            Dict: 系统统计信息
        """
        logger.info("获取系统统计信息")
        
        try:
            stats = {
                "static_nodes": self.static_manager.get_statistics(),
                "route_nodes": self.route_etl.get_route_statistics(),
                "ship_nodes": self.ship_etl.get_statistics(),
                "port_nodes": self.port_etl.get_statistics(),
                "timestamp": datetime.now().isoformat()
            }
            
            # 计算总节点数
            total_nodes = 0
            for category in stats.values():
                if isinstance(category, dict):
                    for count in category.values():
                        if isinstance(count, int):
                            total_nodes += count
            
            stats["total_nodes"] = total_nodes
            return stats
            
        except Exception as e:
            logger.error(f"获取系统统计失败: {e}")
            return {"error": str(e)}
    
    def health_check(self) -> Dict[str, Any]:
        """
        系统健康检查
        
        Returns:
            Dict: 健康检查结果
        """
        logger.info("执行系统健康检查")
        
        health = {
            "database_connections": {},
            "node_counts": {},
            "system_status": "unknown",
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            # 检查数据库连接
            health["database_connections"] = self.db_manager.check_connections()
            
            # 检查节点数量
            health["node_counts"] = self.get_system_statistics()
            
            # 判断系统状态
            neo4j_ok = health["database_connections"].get("neo4j", False)
            oracle_ok = health["database_connections"].get("oracle", False)
            
            if neo4j_ok and oracle_ok:
                health["system_status"] = "healthy"
            elif neo4j_ok:
                health["system_status"] = "partial"  # Neo4j正常，Oracle异常
            else:
                health["system_status"] = "unhealthy"
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            health["system_status"] = "error"
            health["error"] = str(e)
        
        return health
    
    def delete_nodes_by_type(self, node_type: str, condition: str = None, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        按节点类型删除节点

        Args:
            node_type: 节点类型 (Ship, Port, Province, Year, YearMonth等)
            condition: 删除条件 (WHERE子句，不包含WHERE关键字)
            params: 查询参数

        Returns:
            Dict: 删除结果
        """
        logger.info(f"开始删除 {node_type} 节点")

        result = {
            "node_type": node_type,
            "deleted_count": 0,
            "success": True,
            "error": None
        }

        try:
            with self.db_manager.neo4j.get_session() as session:
                # 构建删除查询
                if condition:
                    query = f"""
                        MATCH (n:{node_type})
                        WHERE {condition}
                        DETACH DELETE n
                        RETURN count(n) as deleted_count
                    """
                else:
                    query = f"""
                        MATCH (n:{node_type})
                        DETACH DELETE n
                        RETURN count(n) as deleted_count
                    """

                delete_result = session.run(query, params or {})
                result["deleted_count"] = delete_result.single()["deleted_count"]

                logger.info(f"✅ 删除 {node_type} 节点: {result['deleted_count']} 个")

        except Exception as e:
            logger.error(f"❌ 删除 {node_type} 节点失败: {e}")
            result["success"] = False
            result["error"] = str(e)

        return result


    def delete_all_dynamic_nodes(self) -> Dict[str, Any]:
        """
        删除所有动态节点（保留静态节点）

        Returns:
            Dict: 删除结果统计
        """
        logger.info("开始删除所有动态节点")

        results = {
            "deleted_types": [],
            "total_deleted": 0,
            "success": True,
            "errors": []
        }

        # 定义动态节点类型（按依赖顺序删除）
        dynamic_node_types = [
            "RouteMonthCargoStat",
            "RouteMonthStat",
            "ShipMonthLineStat",
            "ShipMonthCargoStat",
            "ShipMonthStat",
            "PortMonthCargoStat",
            "PortMonthStat",
            "ShipQuarterStat",
            "ShipYearStat",
            "PortQuarterStat",
            "PortYearStat",
            "ShippingRoute",
            "Ship"
        ]

        try:
            for node_type in dynamic_node_types:
                delete_result = self.delete_nodes_by_type(node_type)

                if delete_result["success"]:
                    results["deleted_types"].append({
                        "type": node_type,
                        "count": delete_result["deleted_count"]
                    })
                    results["total_deleted"] += delete_result["deleted_count"]
                else:
                    results["errors"].append(f"{node_type}: {delete_result['error']}")

            if results["errors"]:
                results["success"] = False

            logger.info(f"✅ 动态节点删除完成: 总计 {results['total_deleted']} 个节点")

        except Exception as e:
            logger.error(f"❌ 删除动态节点失败: {e}")
            results["success"] = False
            results["errors"].append(str(e))

        return results

    def delete_all_nodes(self) -> Dict[str, Any]:
        """
        删除所有节点（包括静态节点）

        Returns:
            Dict: 删除结果统计
        """
        logger.info("⚠️  开始删除所有节点（包括静态节点）")

        results = {
            "deleted_types": [],
            "total_deleted": 0,
            "success": True,
            "errors": []
        }

        try:
            with self.db_manager.neo4j.get_session() as session:
                # 获取删除前的节点统计（不使用APOC）
                try:
                    # 尝试使用APOC
                    before_result = session.run("""
                        CALL db.labels() YIELD label
                        CALL apoc.cypher.run('MATCH (n:' + label + ') RETURN count(n) as count', {}) YIELD value
                        RETURN label, value.count as count
                    """)
                    before_stats = {record["label"]: record["count"] for record in before_result}
                except Exception:
                    # APOC不可用，使用简单统计
                    logger.warning("APOC插件不可用，使用简单节点统计")
                    total_before = session.run("MATCH (n) RETURN count(n) as count").single()["count"]
                    before_stats = {"ALL": total_before}

                logger.info(f"删除前节点统计: {before_stats}")

                # 删除所有节点和关系
                session.run("MATCH (n) DETACH DELETE n")

                # 获取删除后的统计
                after_result = session.run("MATCH (n) RETURN count(n) as total_remaining")
                remaining_count = after_result.single()["total_remaining"]

                total_deleted = sum(before_stats.values())
                results["total_deleted"] = total_deleted
                results["deleted_types"] = [{"type": "ALL", "count": total_deleted}]

                if remaining_count == 0:
                    logger.info(f"✅ 所有节点删除完成: 总计 {total_deleted} 个节点")
                else:
                    logger.warning(f"⚠️  仍有 {remaining_count} 个节点未删除")
                    results["success"] = False
                    results["errors"].append(f"仍有 {remaining_count} 个节点未删除")

        except Exception as e:
            logger.error(f"❌ 删除所有节点失败: {e}")
            results["success"] = False
            results["errors"].append(str(e))

        return results

    def delete_test_nodes(self) -> Dict[str, Any]:
        """
        删除测试节点

        Returns:
            Dict: 删除结果统计
        """
        logger.info("开始删除测试节点")

        results = {
            "deleted_types": [],
            "total_deleted": 0,
            "success": True,
            "errors": []
        }

        # 定义测试节点删除条件
        test_conditions = [
            ("Ship", "n.name CONTAINS '测试' OR n.name CONTAINS '汉海5号'"),
            ("Port", "n.name CONTAINS '测试'"),
            ("Province", "n.name CONTAINS '测试'"),
            ("ShipMonthStat", "n.name CONTAINS '测试' OR n.name CONTAINS '413256960'"),
            ("ShipMonthCargoStat", "n.name CONTAINS '测试' OR n.name CONTAINS '413256960'"),
            ("ShipMonthLineStat", "n.name CONTAINS '测试' OR n.name CONTAINS '413256960'"),
            ("PortMonthStat", "n.name CONTAINS '测试'"),
            ("PortMonthCargoStat", "n.name CONTAINS '测试'")
        ]

        try:
            for node_type, condition in test_conditions:
                delete_result = self.delete_nodes_by_type(node_type, condition)

                if delete_result["success"]:
                    results["deleted_types"].append({
                        "type": node_type,
                        "count": delete_result["deleted_count"]
                    })
                    results["total_deleted"] += delete_result["deleted_count"]
                else:
                    results["errors"].append(f"{node_type}: {delete_result['error']}")

            if results["errors"]:
                results["success"] = False

            logger.info(f"✅ 测试节点删除完成: 总计 {results['total_deleted']} 个节点")

        except Exception as e:
            logger.error(f"❌ 删除测试节点失败: {e}")
            results["success"] = False
            results["errors"].append(str(e))

        return results

    def cleanup_database(self, cleanup_type: str = "test") -> Dict[str, Any]:
        """
        数据库清理

        Args:
            cleanup_type: 清理类型 ("test", "dynamic", "all")

        Returns:
            Dict: 清理结果
        """
        logger.info(f"开始数据库清理: {cleanup_type}")

        if cleanup_type == "test":
            return self.delete_test_nodes()
        elif cleanup_type == "dynamic":
            return self.delete_all_dynamic_nodes()
        elif cleanup_type == "all":
            return self.delete_all_nodes()
        else:
            logger.error(f"❌ 不支持的清理类型: {cleanup_type}")
            return {
                "success": False,
                "error": f"不支持的清理类型: {cleanup_type}",
                "total_deleted": 0
            }

    def close(self):
        """关闭所有连接"""
        try:
            self.static_manager.close()
            # route_etl 使用相同的 db_manager，不需要单独关闭
            self.ship_etl.close()
            self.port_etl.close()
            self.db_manager.close()
            logger.info("ETL管理器关闭完成")
        except Exception as e:
            logger.error(f"关闭ETL管理器失败: {e}")


# 便捷函数
def quick_setup() -> ETLManager:
    """快速设置ETL管理器"""
    return ETLManager()


def quick_etl(limit: int = 10000) -> Dict[str, Any]:
    """快速执行ETL（小数据量测试）"""
    etl = ETLManager()
    try:
        return etl.run_full_etl(
            ship_filter="ship_name_cn = '汉海5号'",
            time_filter="ym >= '202407'",
            limit=limit,
            parallel=True
        )
    finally:
        etl.close()

def quick_cleanup(cleanup_type: str = "test") -> Dict[str, Any]:
    """快速清理数据库"""
    etl = ETLManager()
    try:
        return etl.cleanup_database(cleanup_type)
    finally:
        etl.close()

def get_summary() -> Dict[str, Any]:
    """获取系统统计摘要"""
    etl = ETLManager()
    try:
        return etl.get_system_statistics()
    finally:
        etl.close()

def delete_dynamic_data() -> Dict[str, Any]:
    """删除动态数据（保留静态节点）"""
    logger.info("删除动态数据")
    return quick_cleanup("dynamic")

def delete_all_data() -> Dict[str, Any]:
    """删除所有数据"""
    logger.warning("⚠️  删除所有数据")
    return quick_cleanup("all")

def delete_test_data() -> Dict[str, Any]:
    """删除测试数据"""
    logger.info("删除测试数据")
    return quick_cleanup("test")

def quick_test() -> Dict[str, Any]:
    """快速测试ETL功能"""
    etl = ETLManager()
    try:
        return etl.health_check()
    finally:
        etl.close()

def reset_database() -> Dict[str, Any]:
    """重置数据库（删除所有数据后重新创建静态节点）"""
    logger.info("重置数据库")

    etl = ETLManager()
    try:
        # 1. 删除所有数据
        delete_result = etl.delete_all_nodes()
        logger.info(f"删除结果: {delete_result}")

        # 2. 重新创建静态节点
        if delete_result["success"]:
            setup_result = etl.setup_static_nodes()
            logger.info(f"静态节点创建结果: {setup_result}")

            return {
                "success": setup_result.get("success", True),
                "deleted": delete_result["total_deleted"],
                "created": setup_result.get("total_created", 0),
                "reset_complete": True
            }
        else:
            return {
                "success": False,
                "error": "删除数据失败",
                "deleted": delete_result["total_deleted"],
                "created": 0,
                "reset_complete": False
            }

    except Exception as e:
        logger.error(f"重置数据库失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "reset_complete": False
        }
    finally:
        etl.close()


if __name__ == "__main__":
    import argparse

    # 配置命令行参数
    parser = argparse.ArgumentParser(description="ETL管理器")
    parser.add_argument("--action", choices=["test", "etl", "cleanup", "reset","summry"],
                       default="test", help="执行的操作")
    parser.add_argument("--cleanup", choices=["test", "dynamic", "all"],
                       default="test", help="清理类型")
    parser.add_argument("--limit", type=int, default=1000, help="ETL记录数限制")

    args = parser.parse_args()

    if args.action == "test":
        logger.info("执行ETL管理器快速测试")
        result = quick_test()
        logger.info(f"测试结果: {result}")

    elif args.action == "etl":
        logger.info(f"执行ETL流程，限制 {args.limit} 条记录")
        result = quick_etl(args.limit)
        logger.info(f"ETL结果: {result}")
        
    elif args.action == "summry":
        result = get_summary()
        logger.info(f"ETL结果: {result}")        

    elif args.action == "cleanup":
        logger.info(f"执行数据库清理: {args.cleanup}")
        result = quick_cleanup(args.cleanup)
        logger.info(f"清理结果: {result}")

    elif args.action == "reset":
        logger.info("重置数据库")
        result = reset_database()
        logger.info(f"重置结果: {result}")

    else:
        logger.error(f"未知操作: {args.action}")

    # 显示系统统计摘要
    logger.info("=== 系统统计摘要 ===")
    summary = get_summary()
    logger.info(f"系统统计: {summary}")