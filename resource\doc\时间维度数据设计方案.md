# 船舶和港口时间维度数据设计方案

**版本**: 1.0  
**日期**: 2025年7月30日  
**范围**: 船舶和港口节点的月度、季度、年度数据架构

---

## 📋 概述

本文档详细阐述了船舶和港口节点在不同时间粒度（月度、季度、年度）下的数据产生机制和设计架构。设计遵循数据库设计文档v4.0的规范，确保数据一致性和查询效率。

## 🏗️ 时间维度架构

### **1. 时间层次结构**

```mermaid
graph TD
    A[原始月度数据] --> B[季度聚合数据]
    B --> C[年度聚合数据]
    
    A1[ShipMonthStat] --> B1[ShipQuarterStat]
    A2[PortMonthStat] --> B2[PortQuarterStat]
    A3[RouteMonthStat] --> B3[RouteQuarterStat]
    
    B1 --> C1[ShipYearStat]
    B2 --> C2[PortYearStat]
    B3 --> C3[RouteYearStat]
    
    D[时间节点体系]
    D --> D1[YearMonth]
    D --> D2[Quarter]
    D --> D3[Year]
    
    D1 --> D2
    D2 --> D3
```

### **2. 核心设计原则**

1. **数据源头**: 月度数据从Oracle源系统直接获取
2. **聚合策略**: 季度和年度数据通过Neo4j内部聚合生成
3. **时间节点**: 统一的时间节点体系支持多粒度查询
4. **增量更新**: 支持增量计算，避免全量重算
5. **数据一致性**: 确保各时间粒度数据的逻辑一致性

## 📊 节点类型设计

### **船舶时间维度节点**

#### **1. ShipMonthStat (月度统计) - 已实现**
- **数据源**: Oracle `yssjtj.dws_ship_index_i_m`
- **主键**: `{mmsi}_{ym}`
- **属性**: 按设计文档定义的17个指标
- **关系**: `[:STAT_FOR_SHIP]->Ship`, `[:STAT_FOR_MONTH]->YearMonth`

#### **2. ShipQuarterStat (季度统计) - 新增**
```cypher
(:ShipQuarterStat {
    name: "413256960_2024Q1",
    year: 2024,
    quarter: 1,
    // 求和类指标
    totalVoyages: 45,
    totalLoadVoyages: 38,
    totalTurnover_tonkm: 125000.0,
    totalMileage_km: 2500.0,
    totalAnchorTime_day: 15.5,
    totalSailTime_day: 75.2,
    // 平均类指标
    avgOpRatio: 0.85,
    avgLoadRatio: 0.78,
    avgSailRatio: 0.83,
    monthCount: 3,
    lastUpdated: datetime()
})
```

#### **3. ShipYearStat (年度统计) - 新增**
```cypher
(:ShipYearStat {
    name: "413256960_2024",
    year: 2024,
    // 年度总计指标
    annualTotalVoyages: 180,
    annualTotalLoadVoyages: 152,
    annualTotalTurnover_tonkm: 500000.0,
    annualTotalMileage_km: 10000.0,
    annualTotalAnchorTime_day: 62.0,
    annualTotalSailTime_day: 300.8,
    // 年度平均指标
    annualAvgOpRatio: 0.84,
    annualAvgLoadRatio: 0.76,
    annualAvgSailRatio: 0.82,
    quarterCount: 4,
    lastUpdated: datetime()
})
```

### **港口时间维度节点**

#### **1. PortMonthStat (月度统计) - 已实现**
- **数据源**: Oracle `yssjtj.dws_port_index_i_m`
- **主键**: `{port_name}_{ym}`
- **属性**: 按设计文档定义的7个指标
- **关系**: `[:STAT_FOR_PORT]->Port`, `[:STAT_FOR_MONTH]->YearMonth`

#### **2. PortQuarterStat (季度统计) - 新增**
```cypher
(:PortQuarterStat {
    name: "武汉港_2024Q1",
    year: 2024,
    quarter: 1,
    // 求和类指标
    totalInShipCount: 18576,
    totalOutShipCount: 18420,
    totalInCargo_ton: 28619086.17,
    totalOutCargo_ton: 27890234.56,
    // 平均类指标
    avgAnchorTime_days: 2.3,
    avgInLoadRatio: 0.78,
    avgOutLoadRatio: 0.82,
    monthCount: 3,
    lastUpdated: datetime()
})
```

#### **3. PortYearStat (年度统计) - 新增**
```cypher
(:PortYearStat {
    name: "武汉港_2024",
    year: 2024,
    // 年度总计指标
    annualTotalInShipCount: 74304,
    annualTotalOutShipCount: 73680,
    annualTotalInCargo_ton: 114476344.68,
    annualTotalOutCargo_ton: 111560938.24,
    // 年度平均指标
    annualAvgAnchorTime_days: 2.4,
    annualAvgInLoadRatio: 0.79,
    annualAvgOutLoadRatio: 0.81,
    quarterCount: 4,
    lastUpdated: datetime()
})
```

## 🔄 数据产生流程

### **1. 月度数据加载 (已实现)**

```python
# 船舶月度统计
ship_etl = ShipDynamicETL()
ship_etl.load_ship_month_stats(
    time_filter="ym >= '202401'",
    limit=50000
)

# 港口月度统计
port_etl = PortDynamicETL()
port_etl.load_port_month_stats(
    time_filter="ym >= '202401'",
    limit=50000
)
```

### **2. 季度数据聚合 (新增)**

```python
# 使用TimeAggregator进行季度聚合
time_aggregator = TimeAggregator(db_manager)

# 聚合2024年各季度数据
for quarter in range(1, 5):
    # 船舶季度统计
    ship_result = time_aggregator.aggregate_ship_quarter_stats(2024, quarter)
    
    # 港口季度统计
    port_result = time_aggregator.aggregate_port_quarter_stats(2024, quarter)
```

### **3. 年度数据聚合 (新增)**

```python
# 聚合2024年度数据
year_result = time_aggregator.aggregate_ship_year_stats(2024)
port_year_result = time_aggregator.aggregate_port_year_stats(2024)
```

## 📈 聚合策略详解

### **1. 求和类指标**
适用于可累加的指标：
- 航次数 (voyages)
- 载货航次数 (loadVoyages)
- 周转量 (turnover_tonkm)
- 里程 (mileage_km)
- 锚泊时间 (anchorTime_day)
- 航行时间 (sailTime_day)
- 进出港艘次 (inShipCount, outShipCount)
- 进出港货运量 (inCargo_ton, outCargo_ton)

**聚合公式**: `季度总计 = Σ(月度值)`, `年度总计 = Σ(季度值)`

### **2. 平均类指标**
适用于比率和效率指标：
- 有效营运率 (opRatio)
- 装载率 (loadRatio)
- 航行率 (sailRatio)
- 进出港装载率 (inLoadRatio, outLoadRatio)

**聚合公式**: `季度平均 = Σ(月度值) / 月数`, `年度平均 = Σ(季度值) / 季度数`

### **3. 特殊处理指标**
- **锚泊时间**: 按天累加
- **吞吐量**: 进港+出港货运量
- **利用率**: 基于时间窗口重新计算

## 🔗 关系设计

### **时间层次关系**
```cypher
// 月份 -> 季度
(ym:YearMonth)-[:BELONGS_TO_QUARTER]->(q:Quarter)

// 季度 -> 年份
(q:Quarter)-[:BELONGS_TO_YEAR]->(y:Year)

// 月份 -> 年份 (直接关系)
(ym:YearMonth)-[:BELONGS_TO_YEAR]->(y:Year)
```

### **统计节点关系**
```cypher
// 船舶统计关系
(sms:ShipMonthStat)-[:STAT_FOR_SHIP]->(s:Ship)
(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth)
(sqs:ShipQuarterStat)-[:STAT_FOR_SHIP]->(s:Ship)
(sqs:ShipQuarterStat)-[:STAT_FOR_QUARTER]->(q:Quarter)
(sys:ShipYearStat)-[:STAT_FOR_SHIP]->(s:Ship)
(sys:ShipYearStat)-[:STAT_FOR_YEAR]->(y:Year)

// 港口统计关系
(pms:PortMonthStat)-[:STAT_FOR_PORT]->(p:Port)
(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth)
(pqs:PortQuarterStat)-[:STAT_FOR_PORT]->(p:Port)
(pqs:PortQuarterStat)-[:STAT_FOR_QUARTER]->(q:Quarter)
(pys:PortYearStat)-[:STAT_FOR_PORT]->(p:Port)
(pys:PortYearStat)-[:STAT_FOR_YEAR]->(y:Year)
```

## 🚀 实施计划

### **阶段1: 时间节点体系 (1周)**
- [x] 创建TimeAggregator类
- [ ] 实现时间层次结构创建
- [ ] 建立时间节点关系

### **阶段2: 季度聚合 (2周)**
- [ ] 实现船舶季度统计聚合
- [ ] 实现港口季度统计聚合
- [ ] 添加数据验证和错误处理

### **阶段3: 年度聚合 (1周)**
- [ ] 实现船舶年度统计聚合
- [ ] 实现港口年度统计聚合
- [ ] 完善聚合算法

### **阶段4: 增量更新 (2周)**
- [ ] 实现增量聚合机制
- [ ] 添加变更检测
- [ ] 优化性能

## 📊 查询示例

### **多时间粒度查询**
```cypher
// 查询船舶的年度、季度、月度统计
MATCH (s:Ship {mmsi: "413256960"})
OPTIONAL MATCH (s)<-[:STAT_FOR_SHIP]-(sys:ShipYearStat {year: 2024})
OPTIONAL MATCH (s)<-[:STAT_FOR_SHIP]-(sqs:ShipQuarterStat {year: 2024, quarter: 1})
OPTIONAL MATCH (s)<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: "202401"})
RETURN s.name, 
       sys.annualTotalVoyages as year_voyages,
       sqs.totalVoyages as quarter_voyages,
       sms.voyages as month_voyages
```

### **时间趋势分析**
```cypher
// 查询港口季度吞吐量趋势
MATCH (p:Port {name: "武汉港"})<-[:STAT_FOR_PORT]-(pqs:PortQuarterStat)
MATCH (pqs)-[:STAT_FOR_QUARTER]->(q:Quarter)
WHERE q.year = 2024
RETURN q.quarter, 
       pqs.totalInCargo_ton + pqs.totalOutCargo_ton as total_throughput
ORDER BY q.quarter
```

## 🎯 预期收益

1. **查询性能提升**: 预聚合数据减少实时计算开销
2. **分析能力增强**: 支持多时间粒度的趋势分析
3. **数据一致性**: 统一的聚合逻辑确保数据准确性
4. **扩展性**: 易于添加新的时间维度和统计指标
5. **API支持**: 完美支持tool_.py中的时间表达式查询

通过这个设计方案，船舶和港口节点将具备完整的时间维度分析能力，为长江航运智能分析系统提供强大的数据支撑！
