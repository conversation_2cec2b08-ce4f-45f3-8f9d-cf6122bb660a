from fastapi import FastAP<PERSON>, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from langserve import add_routes
from loguru import logger
from typing import Dict, Any
import time

from .runnable import runnable_agent, AgentInput
from ..config import settings


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    
    app = FastAPI(
        title="CJHY-KGAgent API",
        description="长江航运智能指标问答Agent API服务",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 生产环境应该限制具体域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 静态文件服务
    app.mount("/static", StaticFiles(directory="resource/img"), name="static")
    
    # 请求日志中间件
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time
        
        logger.info(
            f"{request.method} {request.url.path} - "
            f"Status: {response.status_code} - "
            f"Time: {process_time:.3f}s"
        )
        return response
    
    # 健康检查端点
    @app.get("/health")
    async def health_check():
        """健康检查"""
        status = runnable_agent.get_health_status()
        return status
    
    # 服务信息端点
    @app.get("/info")
    async def service_info():
        """服务信息"""
        return {
            "service": "Agent API",
            "version": "1.0.0",
            "description": "长江航运智能指标问答Agent",
            "endpoints": {
                "invoke": "/agent/invoke",
                "stream": "/agent/stream", 
                "batch": "/agent/batch",
                "docs": "/docs",
                "health": "/health"
            },
            "features": [
                "知识图谱问答",
                "缓存加速",
                "流式输出",
                "批量处理"
            ]
        }
    
    # API演示页面 - 使用landpage设计
    @app.get("/", response_class=HTMLResponse)
    async def demo_page():
        """API演示页面 - 复用landpage设计"""
        html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ship Agent - 船货匹配智能助手</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@500;600;700&display=swap');
        
        :root {
            --brand-color: #008a9a;
            --purple-dot: #7950f2;
            --border-color: #e6e6e6;
            --hover-border: #d0d0d0;
            --focus-border: #008a9a;
            --text-gray: #666;
            --text-dark: #555;
            --bg-light: #fafafa;
            --shadow-light: 0 2px 8px rgba(0,0,0,.12);
            --shadow-hover: 0 4px 16px rgba(0,0,0,.15);
            
            /* 新增尺寸变量 */
            --base-font: 18px;
            --logo-size: 48px;
            --logo-font: 46px;
            --subtitle-font: 26px;
            --input-height: 160px;
            --input-font: 22px;
            --btn-circle: 44px;
            --btn-icon: 24px;
            --card-padding: 32px;
            --title-spacing: 40px;
            --search-spacing: 32px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'IBM Plex Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            font-size: var(--base-font);
            line-height: 1.6;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            padding: 120px 40px 80px;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
        }

        .logo-section {
            margin-bottom: var(--title-spacing);
            text-align: left;
            position: relative;
        }

        .logo {
            display: inline-flex;
            align-items: center;
            gap: 16px;
            font-size: var(--logo-font);
            font-weight: 800;
            color: #333;
            text-decoration: none;
            letter-spacing: -0.5px;
        }

        .logo-icon {
            width: var(--logo-size);
            height: var(--logo-size);
            border-radius: 12px;
            # background-color: #f5f5f5;
            padding: 4px;
        }

        .logo-text {
            color: #000;
            font-family: 'IBM Plex Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-weight: 800;
        }

        .status-dot {
            position: absolute;
            top: -12px;
            right: -24px;
            width: 14px;
            height: 14px;
            background: var(--purple-dot);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .subtitle {
            margin-top: 20px;
            font-size: var(--subtitle-font);
            font-weight: 500;
            color: var(--text-dark);
            text-align: left;
            letter-spacing: 0.5px;
            line-height: 1.4;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.6; }
            100% { opacity: 1; }
        }

        .search-section {
            width: 100%;
            max-width: 900px;
            position: relative;
        }

        .search-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            background-color: #ffffff;
            border: 1px solid var(--border-color);
            border-radius: 28px;
            padding: 16px 28px;
            box-shadow: none;
            transition: all 0.3s ease;
            margin-bottom: 24px;
            width: 100%;
            min-height: var(--input-height);
        }

        .search-input-wrapper:hover {
            border-color: var(--hover-border);
            box-shadow: var(--shadow-light);
            transform: translateY(-1px);
        }

        .search-input-wrapper:focus-within {
            border-color: var(--focus-border);
            box-shadow: var(--shadow-hover);
            transform: translateY(-2px);
        }

        .search-input {
            flex: 1;
            background: transparent;
            border: none;
            color: #333;
            font-size: var(--input-font);
            outline: none;
            padding: 0;
            font-family: inherit;
            font-weight: 400;
            z-index: 3;
            position: relative;
        }

        .search-input::placeholder {
            color: var(--text-gray);
        }

        .search-input:focus::placeholder {
            color: var(--text-dark);
        }

        .search-tools-right {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-left: 20px;
        }

        .search-mode-toggle {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            background: #f8f9fa;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
        }

        .search-mode-toggle:hover {
            background: #f0f1f2;
            border-color: var(--brand-color);
            transform: translateY(-1px);
            box-shadow: var(--shadow-light);
        }

        .search-mode-toggle.active {
            background: var(--brand-color);
            color: white;
            border-color: var(--brand-color);
            transform: translateY(-1px);
        }

        .search-icon-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: transparent;
            color: var(--text-gray);
            cursor: pointer;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .search-icon-btn:hover {
            background-color: #f5f5f5;
            color: var(--brand-color);
            transform: translateY(-1px);
            box-shadow: var(--shadow-light);
        }

        .search-submit-btn {
            width: var(--btn-circle);
            height: var(--btn-circle);
            border: 1px solid var(--border-color);
            background: var(--brand-color);
            color: white;
            cursor: pointer;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .search-submit-btn:hover {
            background: #007080;
            border-color: #007080;
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover);
        }

        .search-submit-btn:active {
            transform: translateY(1px);
        }

        .search-submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            filter: grayscale(40%);
        }

        .submit-icon {
            width: var(--btn-icon);
            height: var(--btn-icon);
            object-fit: contain;
            filter: brightness(0) invert(1);
        }

        /* 下拉列表样式 */
        .suggestions-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background-color: #ffffff;
            border: 1px solid var(--border-color);
            border-radius: 0 0 20px 20px;
            border-top: 1px solid var(--border-color);
            box-shadow: var(--shadow-hover);
            z-index: 1000;
            margin-top: 0;
            max-height: 420px;
            overflow-y: auto;
            display: none;
            animation: slideDown 0.3s ease;
        }

        .suggestions-dropdown.active {
            display: block;
        }

        .search-input-wrapper.dropdown-active {
            border-radius: 28px 28px 0 0;
            border-bottom: none;
            box-shadow: var(--shadow-hover);
        }

        .suggestions-category {
            padding: 24px 24px 20px;
            border-bottom: 1px solid var(--border-color);
        }

        .suggestions-category:last-child {
            border-bottom: none;
            padding-bottom: 24px;
        }

        .category-title {
            font-size: 15px;
            font-weight: 600;
            color: #666;
            margin-bottom: 16px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .suggestions-list {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .suggestion-item {
            background-color: transparent;
            border: none;
            border-radius: 12px;
            padding: 16px 20px;
            font-size: 16px;
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 12px;
            line-height: 1.4;
            min-height: 52px;
            text-align: left;
            font-weight: 400;
        }

        .suggestion-item:hover {
            background-color: #f5f5f5;
            color: var(--brand-color);
            transform: translateX(4px);
        }

        .suggestion-item.active {
            background-color: var(--brand-color);
            color: white;
            transform: scale(0.98);
        }

        @keyframes slideDown {
            from { 
                opacity: 0;
                transform: translateY(-10px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }

        .results-section {
            display: none;
            width: 100%;
            max-width: 900px;
            margin-top: var(--search-spacing);
        }

        .results-section.active {
            display: block;
        }

        .result-item {
            background-color: #ffffff;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            padding: var(--card-padding);
            margin-bottom: 24px;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
        }

        .result-item:hover {
            box-shadow: var(--shadow-hover);
            border-color: var(--hover-border);
            transform: translateY(-2px);
        }

        .result-question {
            font-size: 22px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
            line-height: 1.4;
        }

        .result-answer {
            font-size: 18px;
            line-height: 1.75;
            color: #555;
            margin-bottom: 20px;
            white-space: pre-wrap;
        }

        .result-sources {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }

        .source-link {
            padding: 8px 16px;
            background-color: #f5f5f5;
            border-radius: 16px;
            color: #666;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .source-link:hover {
            background-color: #e0e0e0;
            color: #333;
            border-color: var(--brand-color);
            transform: translateY(-1px);
            box-shadow: var(--shadow-light);
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 48px;
            color: var(--text-dark);
            font-size: 16px;
        }

        .spinner {
            width: 24px;
            height: 24px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--brand-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .user-avatars {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .help-btn {
            position: fixed;
            bottom: 20px;
            left: 20px;
            width: 52px;
            height: 52px;
            background: var(--brand-color);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 22px;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(0, 138, 154, 0.3);
            transition: all 0.3s ease;
        }

        .help-btn:hover {
            background: #007080;
            transform: scale(1.1) translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 138, 154, 0.4);
        }

        .help-btn:active {
            transform: scale(1.05) translateY(0);
        }

        @media (max-width: 640px) {
            :root {
                --base-font: 16px;
                --logo-size: 36px;
                --logo-font: 38px;
                --subtitle-font: 20px;
                --input-height: 90px;
                --input-font: 19px;
                --btn-circle: 40px;
                --btn-icon: 20px;
                --card-padding: 24px;
                --title-spacing: 20px;
                --search-spacing: 20px;
            }

            .main-content {
                padding: 40px 16px 32px;
            }

            .logo {
                gap: 12px;
            }

            .search-section {
                max-width: 100%;
            }

            .results-section {
                max-width: 100%;
            }

            .search-input-wrapper {
                padding: 12px 24px;
                border-radius: 24px;
            }

            #placeholderText {
                top: 32px !important;
                left: 24px !important;
                font-size: 14px !important;
            }

            .search-input {
                padding-top: 20px !important;
                padding-bottom: 36px !important;
                z-index: 3 !important;
                position: relative !important;
            }

            .search-tools-right {
                gap: 8px;
                margin-left: 16px;
                z-index: 4 !important;
            }

            .search-icon-btn {
                width: 40px;
                height: 40px;
                font-size: 20px;
            }

            .search-mode-toggle {
                font-size: 16px;
                padding: 6px 16px;
                border-radius: 16px;
            }

            .result-item {
                border-radius: 16px;
                margin-bottom: 20px;
            }

            .result-question {
                font-size: 20px;
                margin-bottom: 14px;
            }

            .result-answer {
                font-size: 16px;
                line-height: 1.6;
                margin-bottom: 18px;
            }

            .user-avatars {
                bottom: 80px;
                right: 16px;
            }

            .help-btn {
                bottom: 16px;
                left: 16px;
            }

            .suggestions-dropdown {
                max-height: 340px;
                border-radius: 0 0 16px 16px;
                margin-top: 0;
                border-top: 1px solid var(--border-color);
            }

            .search-input-wrapper.dropdown-active {
                border-radius: 24px 24px 0 0;
                border-bottom: none;
                box-shadow: var(--shadow-hover);
            }

            .suggestions-category {
                padding: 20px 20px 16px;
            }

            .suggestions-category:last-child {
                padding-bottom: 20px;
            }

            .category-title {
                font-size: 14px;
                margin-bottom: 14px;
            }

            .suggestion-item {
                font-size: 15px;
                padding: 14px 16px;
                min-height: 48px;
                gap: 10px;
                border-radius: 10px;
            }
        }

        /* 体积光背景canvas样式 */
        #godray-bg {
            position: fixed;
            left: 0; top: 0;
            width: 100vw; height: 100vh;
            z-index: 0;
            pointer-events: none;
            display: block;
        }
        .main-content {
            position: relative;
            z-index: 1;
            background: transparent;
        }
    </style>
</head>
<body>
    <canvas id="godray-bg"></canvas>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        // 体积光Three.js代码，canvas绑定到#godray-bg
        const canvas = document.getElementById('godray-bg');
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.set(0, 0, 8);
        const renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setPixelRatio(window.devicePixelRatio);
        renderer.setClearColor(0xf5f5f5, 1.0);
        renderer.outputEncoding = THREE.sRGBEncoding;
        renderer.toneMapping = THREE.ACESFilmicToneMapping;
        renderer.toneMappingExposure = 1.2;
        renderer.physicallyCorrectLights = true;

        // GodRay Shader
        const godRayVertexShader = `
            varying vec2 vUv;
            void main() {
                vUv = uv;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
        `;
        const godRayFragmentShader = `
            precision highp float;
            uniform float time;
            uniform float intensity;
            varying vec2 vUv;
            float hash(vec2 p) { return fract(sin(dot(p, vec2(127.1, 311.7))) * 43758.5453); }
            float noise(vec2 p) {
                vec2 i = floor(p);
                vec2 f = fract(p);
                float a = hash(i);
                float b = hash(i + vec2(1.0, 0.0));
                float c = hash(i + vec2(0.0, 1.0));
                float d = hash(i + vec2(1.0, 1.0));
                vec2 u = f * f * (3.0 - 2.0 * f);
                return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
            }
            void main() {
                vec2 center = vec2(0.21, 0.71); // 左上角
                vec2 ray = normalize(vUv - center);
                float distance = length(vUv - center);
                float volumeStrength = 0.0;
                for(int i = 0; i < 8; i++) {
                    float t = float(i) / 8.0;
                    vec2 samplePos = vUv - ray * t * 2.0;
                    float density = noise(samplePos * 3.0 + time * 0.1);
                    density *= noise(samplePos * 8.0 + time * 0.2) * 0.5 + 0.5;
                    float falloff = 1.0 - smoothstep(0.0, 0.8, distance + t * 0.2);
                    falloff = pow(falloff, 1.5);
                    volumeStrength += density * falloff * (1.0 - t * 0.3);
                }
                volumeStrength /= 8.0;
                float angle = atan(ray.y, ray.x);
                float rayMask = abs(sin(angle * 6.0 + time * 0.3)) * 0.5 + 0.5;
                float finalIntensity = volumeStrength * rayMask * intensity;
                finalIntensity = smoothstep(0.0, 1.0, finalIntensity);
                vec3 color = vec3(1.0, 1.0, 1.0) * finalIntensity;
                gl_FragColor = vec4(color, finalIntensity * 0.9);
            }
        `;
        const godRayMaterial = new THREE.ShaderMaterial({
            vertexShader: godRayVertexShader,
            fragmentShader: godRayFragmentShader,
            uniforms: {
                time: { value: 0 },
                intensity: { value: 1.0 },
            },
            transparent: true,
            blending: THREE.NormalBlending,
        });
        const planeGeometry = new THREE.PlaneGeometry(70, 50); // 更大
        const godRayPlane = new THREE.Mesh(planeGeometry, godRayMaterial);
        godRayPlane.position.set(0, 0, -5); // 居中
        scene.add(godRayPlane);
        // 灯光
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.2);
        scene.add(ambientLight);
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.2);
        directionalLight.position.set(-3, 5, 2);
        scene.add(directionalLight);
        const pointLight = new THREE.PointLight(0xffffff, 0.9, 12);
        pointLight.position.set(-2, 3, 1);
        scene.add(pointLight);
        // 动画
        const clock = new THREE.Clock();
        function animate() {
            requestAnimationFrame(animate);
            godRayMaterial.uniforms.time.value = clock.getElapsedTime();
            renderer.render(scene, camera);
        }
        animate();
        // 窗口自适应
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
    </script>
    <main class="main-content">
        <section class="logo-section">
            <a href="#" class="logo" style="display: flex; justify-content: center; align-items: center; width: 100%; margin-bottom: 8px;">
                <img src="/static/fish.png" alt="Fish Logo" class="logo-icon" style="max-width: 400px; width: 22.4%; height: 98px; border-radius: 16px; display: inline-block; margin: 0;">
                <img src="/static/subtitle.png" alt="Subtitle Logo" class="logo-icon" style="max-width: 400px; width: 68%; height: 140px; border-radius: 16px; display: inline-block; margin: 0;">
            </a>
            <div class="status-dot" style="left: 50%; transform: translateX(-50%);"></div>
            <p class="subtitle" style="text-align: center; margin-top: 8px;">长江航运智能指标问答助手 | 船货匹配</p>
        </section>

        <section class="search-section">
            <div class="search-input-wrapper" style="position: relative;">
                <span id="placeholderText" style="position: absolute; top: 44px; left: 28px; font-size: 16px; color: #888; pointer-events: none; z-index: 1; transition: all 0.3s ease-in-out; transform-origin: left center;">
                    询问船货匹配相关问题
                </span>
                <input 
                    type="text" 
                    class="search-input" 
                    id="searchInput"
                    style="padding-top: 28px; padding-bottom: 40px; z-index: 3; position: relative;"
                >
         
                <div style="position: absolute; left: 18px; bottom: 12px; display: flex; align-items: center; gap: 12px; z-index: 5;">
                    <div class="search-mode-toggle" id="searchModeToggle" style="margin-left: 0;">
                        <span id="currentMode">同步</span>
                        <span>▼</span>
                    </div>
                    <button class="search-icon-btn" id="suggestionsBtn" title="常用问题">
                        <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" color="currentColor" class="transition-colors duration-300 text-super" fill="currentColor" fill-rule="evenodd" style="vertical-align: middle;">
                            <path d="M8.175 13.976a.876.876 0 0 1 1.172-.04l.065.061.582.59c.196.194.395.388.596.576l.39.358c1.942 1.753 3.844 2.937 5.357 3.477.81.29 1.444.369 1.884.31.404-.055.61-.216.731-.446.135-.256.209-.678.116-1.31-.08-.546-.275-1.191-.59-1.91l-.141-.313-.034-.083a.875.875 0 0 1 1.575-.741l.042.079.161.353c.36.823.61 1.623.719 2.362.122.836.071 1.675-.3 2.38-.431.818-1.186 1.247-2.044 1.363-.823.111-1.756-.056-2.707-.396-1.912-.681-4.17-2.154-6.357-4.207a30.378 30.378 0 0 1-.63-.61l-.608-.615-.058-.068a.875.875 0 0 1 .079-1.17Zm.624-5.822a.876.876 0 0 1 1.216 1.258c-.396.383-.788.775-1.165 1.178-1.95 2.077-3.26 4.133-3.835 5.747-.29.81-.37 1.444-.31 1.884.055.404.215.61.444.731l.104.048c.261.103.654.149 1.207.068.623-.09 1.378-.333 2.224-.731a.875.875 0 0 1 .745 1.583c-.948.446-1.871.756-2.716.88-.784.114-1.57.078-2.246-.234l-.134-.066c-.817-.431-1.246-1.186-1.362-2.044-.112-.823.056-1.756.395-2.707.64-1.792 1.973-3.889 3.83-5.945l.377-.411c.402-.43.816-.843 1.226-1.239Zm8.5-4.954c.832-.122 1.67-.073 2.372.302h-.001c.814.432 1.243 1.185 1.36 2.042.11.823-.057 1.756-.396 2.707-.682 1.911-2.154 4.17-4.207 6.356h-.001c-.403.429-.818.846-1.236 1.236l-.068.057a.875.875 0 0 1-1.127-1.336l.582-.562c.193-.193.385-.39.573-.592l.359-.39c1.752-1.942 2.937-3.844 3.476-5.357.29-.811.37-1.444.31-1.884-.055-.404-.216-.61-.446-.731l-.003-.002c-.248-.132-.663-.207-1.293-.114-.62.09-1.37.332-2.208.73l-.083.034a.876.876 0 0 1-.667-1.615l.351-.161c.819-.36 1.616-.612 2.353-.72Zm-5.292 7.507a1.3 1.3 0 1 1 0 2.6 1.3 1.3 0 0 1 0-2.6ZM5.544 2.971c.823-.112 1.756.056 2.707.395 1.911.682 4.17 2.154 6.356 4.207.214.201.426.406.632.612l.604.625.057.068a.875.875 0 0 1-1.271 1.19l-.065-.063-.562-.582c-.193-.193-.39-.385-.592-.573-2.077-1.95-4.133-3.26-5.747-3.835-.811-.29-1.444-.37-1.884-.31-.404.055-.61.215-.731.444l-.002.004c-.132.248-.207.664-.114 1.294.08.543.275 1.184.588 1.898l.142.31.034.083a.875.875 0 0 1-1.572.746l-.043-.079-.161-.352c-.36-.819-.612-1.615-.72-2.352-.122-.832-.073-1.67.302-2.372.431-.814 1.185-1.242 2.042-1.358Z"></path>
                        </svg>
                    </button>
                </div>
                <div class="search-tools-right" style="z-index: 4;">
                    <button class="search-submit-btn" id="searchBtn" title="搜索">
                        <img src="/static/submit.png" alt="搜索" class="submit-icon">
                    </button>
                </div>
            </div>
            
            <!-- 建议问题下拉列表 -->
            <div class="suggestions-dropdown" id="suggestionsDropdown">
                <div class="suggestions-category">
                    <div class="suggestions-list">
                        <div class="suggestion-item" data-query="413997627当前在什么位置" style="font-size: 0.95em; display: flex; align-items: center; gap: 6px;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" color="currentColor" fill="currentColor" style="vertical-align: middle;">
                                <path d="M8.175 13.976a.876.876 0 0 1 1.172-.04l.065.061.582.59c.196.194.395.388.596.576l.39.358c1.942 1.753 3.844 2.937 5.357 3.477.81.29 1.444.369 1.884.31.404-.055.61-.216.731-.446.135-.256.209-.678.116-1.31-.08-.546-.275-1.191-.59-1.91l-.141-.313-.034-.083a.875.875 0 0 1 1.575-.741l.042.079.161.353c.36.823.61 1.623.719 2.362.122.836.071 1.675-.3 2.38-.431.818-1.186 1.247-2.044 1.363-.823.111-1.756-.056-2.707-.396-1.912-.681-4.17-2.154-6.357-4.207a30.378 30.378 0 0 1-.63-.61l-.608-.615-.058-.068a.875.875 0 0 1 .079-1.17Zm.624-5.822a.876.876 0 0 1 1.216 1.258c-.396.383-.788.775-1.165 1.178-1.95 2.077-3.26 4.133-3.835 5.747-.29.81-.37 1.444-.31 1.884.055.404.215.61.444.731l.104.048c.261.103.654.149 1.207.068.623-.09 1.378-.333 2.224-.731a.875.875 0 0 1 .745 1.583c-.948.446-1.871.756-2.716.88-.784.114-1.57.078-2.246-.234l-.134-.066c-.817-.431-1.246-1.186-1.362-2.044-.112-.823.056-1.756.395-2.707.64-1.792 1.973-3.889 3.83-5.945l.377-.411c.402-.43.816-.843 1.226-1.239Zm8.5-4.954c.832-.122 1.67-.073 2.372.302h-.001c.814.432 1.243 1.185 1.36 2.042.11.823-.057 1.756-.396 2.707-.682 1.911-2.154 4.17-4.207 6.356h-.001c-.403.429-.818.846-1.236 1.236l-.068.057a.875.875 0 0 1-1.127-1.336l.582-.562c.193-.193.385-.39.573-.592l.359-.39c1.752-1.942 2.937-3.844 3.476-5.357.29-.811.37-1.444.31-1.884-.055-.404-.216-.61-.446-.731l-.003-.002c-.248-.132-.663-.207-1.293-.114-.62.09-1.37.332-2.208.73l-.083.034a.876.876 0 0 1-.667-1.615l.351-.161c.819-.36 1.616-.612 2.353-.72Zm-5.292 7.507a1.3 1.3 0 1 1 0 2.6 1.3 1.3 0 0 1 0-2.6ZM5.544 2.971c.823-.112 1.756.056 2.707.395 1.911.682 4.17 2.154 6.356 4.207.214.201.426.406.632.612l.604.625.057.068a.875.875 0 0 1-1.271 1.19l-.065-.063-.562-.582c-.193-.193-.39-.385-.592-.573-2.077-1.95-4.133-3.26-5.747-3.835-.811-.29-1.444-.37-1.884-.31-.404.055-.61.215-.731.444l-.002.004c-.132.248-.207.664-.114 1.294.08.543.275 1.184.588 1.898l.142.31.034.083a.875.875 0 0 1-1.572.746l-.043-.079-.161-.352c-.36-.819-.612-1.615-.72-2.352-.122-.832-.073-1.67.302-2.372.431-.814 1.185-1.242 2.042-1.358Z"></path>
                            </svg>

                            
                            413997627当前在什么位置
                        </div>
                        <div class="suggestion-item" data-query="汉海5号的船舶档案" style="font-size: 0.95em; display: flex; align-items: center; gap: 6px;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" color="currentColor" fill="currentColor" style="vertical-align: middle;">
                                <path d="M8.175 13.976a.876.876 0 0 1 1.172-.04l.065.061.582.59c.196.194.395.388.596.576l.39.358c1.942 1.753 3.844 2.937 5.357 3.477.81.29 1.444.369 1.884.31.404-.055.61-.216.731-.446.135-.256.209-.678.116-1.31-.08-.546-.275-1.191-.59-1.91l-.141-.313-.034-.083a.875.875 0 0 1 1.575-.741l.042.079.161.353c.36.823.61 1.623.719 2.362.122.836.071 1.675-.3 2.38-.431.818-1.186 1.247-2.044 1.363-.823.111-1.756-.056-2.707-.396-1.912-.681-4.17-2.154-6.357-4.207a30.378 30.378 0 0 1-.63-.61l-.608-.615-.058-.068a.875.875 0 0 1 .079-1.17Zm.624-5.822a.876.876 0 0 1 1.216 1.258c-.396.383-.788.775-1.165 1.178-1.95 2.077-3.26 4.133-3.835 5.747-.29.81-.37 1.444-.31 1.884.055.404.215.61.444.731l.104.048c.261.103.654.149 1.207.068.623-.09 1.378-.333 2.224-.731a.875.875 0 0 1 .745 1.583c-.948.446-1.871.756-2.716.88-.784.114-1.57.078-2.246-.234l-.134-.066c-.817-.431-1.246-1.186-1.362-2.044-.112-.823.056-1.756.395-2.707.64-1.792 1.973-3.889 3.83-5.945l.377-.411c.402-.43.816-.843 1.226-1.239Zm8.5-4.954c.832-.122 1.67-.073 2.372.302h-.001c.814.432 1.243 1.185 1.36 2.042.11.823-.057 1.756-.396 2.707-.682 1.911-2.154 4.17-4.207 6.356h-.001c-.403.429-.818.846-1.236 1.236l-.068.057a.875.875 0 0 1-1.127-1.336l.582-.562c.193-.193.385-.39.573-.592l.359-.39c1.752-1.942 2.937-3.844 3.476-5.357.29-.811.37-1.444.31-1.884-.055-.404-.216-.61-.446-.731l-.003-.002c-.248-.132-.663-.207-1.293-.114-.62.09-1.37.332-2.208.73l-.083.034a.876.876 0 0 1-.667-1.615l.351-.161c.819-.36 1.616-.612 2.353-.72Zm-5.292 7.507a1.3 1.3 0 1 1 0 2.6 1.3 1.3 0 0 1 0-2.6ZM5.544 2.971c.823-.112 1.756.056 2.707.395 1.911.682 4.17 2.154 6.356 4.207.214.201.426.406.632.612l.604.625.057.068a.875.875 0 0 1-1.271 1.19l-.065-.063-.562-.582c-.193-.193-.39-.385-.592-.573-2.077-1.95-4.133-3.26-5.747-3.835-.811-.29-1.444-.37-1.884-.31-.404.055-.61.215-.731.444l-.002.004c-.132.248-.207.664-.114 1.294.08.543.275 1.184.588 1.898l.142.31.034.083a.875.875 0 0 1-1.572.746l-.043-.079-.161-.352c-.36-.819-.612-1.615-.72-2.352-.122-.832-.073-1.67.302-2.372.431-.814 1.185-1.242 2.042-1.358Z"></path>
                            </svg>
                            汉海5号的船舶档案
                        </div>
                        <div class="suggestion-item" data-query="区域119.702208 32.349088,119.705641 32.246613,119.864568 32.311073,119.702208 32.349088的船舶有哪些" style="font-size: 0.95em; display: flex; align-items: center; gap: 6px;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" color="currentColor" fill="currentColor" style="vertical-align: middle;">
                                <path d="M8.175 13.976a.876.876 0 0 1 1.172-.04l.065.061.582.59c.196.194.395.388.596.576l.39.358c1.942 1.753 3.844 2.937 5.357 3.477.81.29 1.444.369 1.884.31.404-.055.61-.216.731-.446.135-.256.209-.678.116-1.31-.08-.546-.275-1.191-.59-1.91l-.141-.313-.034-.083a.875.875 0 0 1 1.575-.741l.042.079.161.353c.36.823.61 1.623.719 2.362.122.836.071 1.675-.3 2.38-.431.818-1.186 1.247-2.044 1.363-.823.111-1.756-.056-2.707-.396-1.912-.681-4.17-2.154-6.357-4.207a30.378 30.378 0 0 1-.63-.61l-.608-.615-.058-.068a.875.875 0 0 1 .079-1.17Zm.624-5.822a.876.876 0 0 1 1.216 1.258c-.396.383-.788.775-1.165 1.178-1.95 2.077-3.26 4.133-3.835 5.747-.29.81-.37 1.444-.31 1.884.055.404.215.61.444.731l.104.048c.261.103.654.149 1.207.068.623-.09 1.378-.333 2.224-.731a.875.875 0 0 1 .745 1.583c-.948.446-1.871.756-2.716.88-.784.114-1.57.078-2.246-.234l-.134-.066c-.817-.431-1.246-1.186-1.362-2.044-.112-.823.056-1.756.395-2.707.64-1.792 1.973-3.889 3.83-5.945l.377-.411c.402-.43.816-.843 1.226-1.239Zm8.5-4.954c.832-.122 1.67-.073 2.372.302h-.001c.814.432 1.243 1.185 1.36 2.042.11.823-.057 1.756-.396 2.707-.682 1.911-2.154 4.17-4.207 6.356h-.001c-.403.429-.818.846-1.236 1.236l-.068.057a.875.875 0 0 1-1.127-1.336l.582-.562c.193-.193.385-.39.573-.592l.359-.39c1.752-1.942 2.937-3.844 3.476-5.357.29-.811.37-1.444.31-1.884-.055-.404-.216-.61-.446-.731l-.003-.002c-.248-.132-.663-.207-1.293-.114-.62.09-1.37.332-2.208.73l-.083.034a.876.876 0 0 1-.667-1.615l.351-.161c.819-.36 1.616-.612 2.353-.72Zm-5.292 7.507a1.3 1.3 0 1 1 0 2.6 1.3 1.3 0 0 1 0-2.6ZM5.544 2.971c.823-.112 1.756.056 2.707.395 1.911.682 4.17 2.154 6.356 4.207.214.201.426.406.632.612l.604.625.057.068a.875.875 0 0 1-1.271 1.19l-.065-.063-.562-.582c-.193-.193-.39-.385-.592-.573-2.077-1.95-4.133-3.26-5.747-3.835-.811-.29-1.444-.37-1.884-.31-.404.055-.61.215-.731.444l-.002.004c-.132.248-.207.664-.114 1.294.08.543.275 1.184.588 1.898l.142.31.034.083a.875.875 0 0 1-1.572.746l-.043-.079-.161-.352c-.36-.819-.612-1.615-.72-2.352-.122-.832-.073-1.67.302-2.372.431-.814 1.185-1.242 2.042-1.358Z"></path>
                            </svg>
                            区域119.702208 32.349088,119.705641 32.246613,119.864568 32.311073,119.702208 32.349088的船舶有哪些
                        </div>
                        <div class="suggestion-item" data-query="南京长江大桥截面10分钟内经过的船舶有哪些" style="font-size: 0.95em; display: flex; align-items: center; gap: 6px;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" color="currentColor" fill="currentColor" style="vertical-align: middle;">
                                <path d="M8.175 13.976a.876.876 0 0 1 1.172-.04l.065.061.582.59c.196.194.395.388.596.576l.39.358c1.942 1.753 3.844 2.937 5.357 3.477.81.29 1.444.369 1.884.31.404-.055.61-.216.731-.446.135-.256.209-.678.116-1.31-.08-.546-.275-1.191-.59-1.91l-.141-.313-.034-.083a.875.875 0 0 1 1.575-.741l.042.079.161.353c.36.823.61 1.623.719 2.362.122.836.071 1.675-.3 2.38-.431.818-1.186 1.247-2.044 1.363-.823.111-1.756-.056-2.707-.396-1.912-.681-4.17-2.154-6.357-4.207a30.378 30.378 0 0 1-.63-.61l-.608-.615-.058-.068a.875.875 0 0 1 .079-1.17Zm.624-5.822a.876.876 0 0 1 1.216 1.258c-.396.383-.788.775-1.165 1.178-1.95 2.077-3.26 4.133-3.835 5.747-.29.81-.37 1.444-.31 1.884.055.404.215.61.444.731l.104.048c.261.103.654.149 1.207.068.623-.09 1.378-.333 2.224-.731a.875.875 0 0 1 .745 1.583c-.948.446-1.871.756-2.716.88-.784.114-1.57.078-2.246-.234l-.134-.066c-.817-.431-1.246-1.186-1.362-2.044-.112-.823.056-1.756.395-2.707.64-1.792 1.973-3.889 3.83-5.945l.377-.411c.402-.43.816-.843 1.226-1.239Zm8.5-4.954c.832-.122 1.67-.073 2.372.302h-.001c.814.432 1.243 1.185 1.36 2.042.11.823-.057 1.756-.396 2.707-.682 1.911-2.154 4.17-4.207 6.356h-.001c-.403.429-.818.846-1.236 1.236l-.068.057a.875.875 0 0 1-1.127-1.336l.582-.562c.193-.193.385-.39.573-.592l.359-.39c1.752-1.942 2.937-3.844 3.476-5.357.29-.811.37-1.444.31-1.884-.055-.404-.216-.61-.446-.731l-.003-.002c-.248-.132-.663-.207-1.293-.114-.62.09-1.37.332-2.208.73l-.083.034a.876.876 0 0 1-.667-1.615l.351-.161c.819-.36 1.616-.612 2.353-.72Zm-5.292 7.507a1.3 1.3 0 1 1 0 2.6 1.3 1.3 0 0 1 0-2.6ZM5.544 2.971c.823-.112 1.756.056 2.707.395 1.911.682 4.17 2.154 6.356 4.207.214.201.426.406.632.612l.604.625.057.068a.875.875 0 0 1-1.271 1.19l-.065-.063-.562-.582c-.193-.193-.39-.385-.592-.573-2.077-1.95-4.133-3.26-5.747-3.835-.811-.29-1.444-.37-1.884-.31-.404.055-.61.215-.731.444l-.002.004c-.132.248-.207.664-.114 1.294.08.543.275 1.184.588 1.898l.142.31.034.083a.875.875 0 0 1-1.572.746l-.043-.079-.161-.352c-.36-.819-.612-1.615-.72-2.352-.122-.832-.073-1.67.302-2.372.431-.814 1.185-1.242 2.042-1.358Z"></path>
                            </svg>
                            南京长江大桥截面10分钟内经过的船舶有哪些
                        </div>
                        <div class="suggestion-item" data-query="中心点31.234,121.712，半径1000米范围内有哪些船舶" style="font-size: 0.95em; display: flex; align-items: center; gap: 6px;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" color="currentColor" fill="currentColor" style="vertical-align: middle;">
                                <path d="M8.175 13.976a.876.876 0 0 1 1.172-.04l.065.061.582.59c.196.194.395.388.596.576l.39.358c1.942 1.753 3.844 2.937 5.357 3.477.81.29 1.444.369 1.884.31.404-.055.61-.216.731-.446.135-.256.209-.678.116-1.31-.08-.546-.275-1.191-.59-1.91l-.141-.313-.034-.083a.875.875 0 0 1 1.575-.741l.042.079.161.353c.36.823.61 1.623.719 2.362.122.836.071 1.675-.3 2.38-.431.818-1.186 1.247-2.044 1.363-.823.111-1.756-.056-2.707-.396-1.912-.681-4.17-2.154-6.357-4.207a30.378 30.378 0 0 1-.63-.61l-.608-.615-.058-.068a.875.875 0 0 1 .079-1.17Zm.624-5.822a.876.876 0 0 1 1.216 1.258c-.396.383-.788.775-1.165 1.178-1.95 2.077-3.26 4.133-3.835 5.747-.29.81-.37 1.444-.31 1.884.055.404.215.61.444.731l.104.048c.261.103.654.149 1.207.068.623-.09 1.378-.333 2.224-.731a.875.875 0 0 1 .745 1.583c-.948.446-1.871.756-2.716.88-.784.114-1.57.078-2.246-.234l-.134-.066c-.817-.431-1.246-1.186-1.362-2.044-.112-.823.056-1.756.395-2.707.64-1.792 1.973-3.889 3.83-5.945l.377-.411c.402-.43.816-.843 1.226-1.239Zm8.5-4.954c.832-.122 1.67-.073 2.372.302h-.001c.814.432 1.243 1.185 1.36 2.042.11.823-.057 1.756-.396 2.707-.682 1.911-2.154 4.17-4.207 6.356h-.001c-.403.429-.818.846-1.236 1.236l-.068.057a.875.875 0 0 1-1.127-1.336l.582-.562c.193-.193.385-.39.573-.592l.359-.39c1.752-1.942 2.937-3.844 3.476-5.357.29-.811.37-1.444.31-1.884-.055-.404-.216-.61-.446-.731l-.003-.002c-.248-.132-.663-.207-1.293-.114-.62.09-1.37.332-2.208.73l-.083.034a.876.876 0 0 1-.667-1.615l.351-.161c.819-.36 1.616-.612 2.353-.72Zm-5.292 7.507a1.3 1.3 0 1 1 0 2.6 1.3 1.3 0 0 1 0-2.6ZM5.544 2.971c.823-.112 1.756.056 2.707.395 1.911.682 4.17 2.154 6.356 4.207.214.201.426.406.632.612l.604.625.057.068a.875.875 0 0 1-1.271 1.19l-.065-.063-.562-.582c-.193-.193-.39-.385-.592-.573-2.077-1.95-4.133-3.26-5.747-3.835-.811-.29-1.444-.37-1.884-.31-.404.055-.61.215-.731.444l-.002.004c-.132.248-.207.664-.114 1.294.08.543.275 1.184.588 1.898l.142.31.034.083a.875.875 0 0 1-1.572.746l-.043-.079-.161-.352c-.36-.819-.612-1.615-.72-2.352-.122-.832-.073-1.67.302-2.372.431-.814 1.185-1.242 2.042-1.358Z"></path>
                            </svg>
                            中心点31.234,121.712，半径1000米范围内有哪些船舶
                        </div>
                        <div class="suggestion-item" data-query="南京10分钟内经过的货船有哪些" style="font-size: 0.95em; display: flex; align-items: center; gap: 6px;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" color="currentColor" fill="currentColor" style="vertical-align: middle;">
                                <path d="M8.175 13.976a.876.876 0 0 1 1.172-.04l.065.061.582.59c.196.194.395.388.596.576l.39.358c1.942 1.753 3.844 2.937 5.357 3.477.81.29 1.444.369 1.884.31.404-.055.61-.216.731-.446.135-.256.209-.678.116-1.31-.08-.546-.275-1.191-.59-1.91l-.141-.313-.034-.083a.875.875 0 0 1 1.575-.741l.042.079.161.353c.36.823.61 1.623.719 2.362.122.836.071 1.675-.3 2.38-.431.818-1.186 1.247-2.044 1.363-.823.111-1.756-.056-2.707-.396-1.912-.681-4.17-2.154-6.357-4.207a30.378 30.378 0 0 1-.63-.61l-.608-.615-.058-.068a.875.875 0 0 1 .079-1.17Zm.624-5.822a.876.876 0 0 1 1.216 1.258c-.396.383-.788.775-1.165 1.178-1.95 2.077-3.26 4.133-3.835 5.747-.29.81-.37 1.444-.31 1.884.055.404.215.61.444.731l.104.048c.261.103.654.149 1.207.068.623-.09 1.378-.333 2.224-.731a.875.875 0 0 1 .745 1.583c-.948.446-1.871.756-2.716.88-.784.114-1.57.078-2.246-.234l-.134-.066c-.817-.431-1.246-1.186-1.362-2.044-.112-.823.056-1.756.395-2.707.64-1.792 1.973-3.889 3.83-5.945l.377-.411c.402-.43.816-.843 1.226-1.239Zm8.5-4.954c.832-.122 1.67-.073 2.372.302h-.001c.814.432 1.243 1.185 1.36 2.042.11.823-.057 1.756-.396 2.707-.682 1.911-2.154 4.17-4.207 6.356h-.001c-.403.429-.818.846-1.236 1.236l-.068.057a.875.875 0 0 1-1.127-1.336l.582-.562c.193-.193.385-.39.573-.592l.359-.39c1.752-1.942 2.937-3.844 3.476-5.357.29-.811.37-1.444.31-1.884-.055-.404-.216-.61-.446-.731l-.003-.002c-.248-.132-.663-.207-1.293-.114-.62.09-1.37.332-2.208.73l-.083.034a.876.876 0 0 1-.667-1.615l.351-.161c.819-.36 1.616-.612 2.353-.72Zm-5.292 7.507a1.3 1.3 0 1 1 0 2.6 1.3 1.3 0 0 1 0-2.6ZM5.544 2.971c.823-.112 1.756.056 2.707.395 1.911.682 4.17 2.154 6.356 4.207.214.201.426.406.632.612l.604.625.057.068a.875.875 0 0 1-1.271 1.19l-.065-.063-.562-.582c-.193-.193-.39-.385-.592-.573-2.077-1.95-4.133-3.26-5.747-3.835-.811-.29-1.444-.37-1.884-.31-.404.055-.61.215-.731.444l-.002.004c-.132.248-.207.664-.114 1.294.08.543.275 1.184.588 1.898l.142.31.034.083a.875.875 0 0 1-1.572.746l-.043-.079-.161-.352c-.36-.819-.612-1.615-.72-2.352-.122-.832-.073-1.67.302-2.372.431-.814 1.185-1.242 2.042-1.358Z"></path>
                            </svg>
                            南京10分钟内经过的货船有哪些
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="results-section" id="resultsSection">
            <div class="loading" id="loadingIndicator">
                <div class="spinner"></div>
                <span>正在搜索...</span>
            </div>
            <div id="resultsContainer"></div>
        </section>
    </main>

    <button class="help-btn" title="帮助">
        ?
    </button>

    <div class="user-avatars">
        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTYiIGZpbGw9IiNGRjYzNDciLz4KPHN2ZyB4PSI4IiB5PSI4IiB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSI+CjxjaXJjbGUgY3g9IjgiIGN5PSI2IiByPSIzIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMiAxNGMwLTMuMzE0IDIuNjg2LTYgNi02czYgMi42ODYgNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4KPC9zdmc+" alt="User 1" class="user-avatar">
        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTYiIGZpbGw9IiM0Rjc5QTQiLz4KPHN2ZyB4PSI4IiB5PSI4IiB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSI+CjxjaXJjbGUgY3g9IjgiIGN5PSI2IiByPSIzIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMiAxNGMwLTMuMzE0IDIuNjg2LTYgNi02czYgMi42ODYgNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4KPC9zdmc+" alt="User 2" class="user-avatar">
        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTYiIGZpbGw9IiMzNEM3NEMiLz4KPHN2ZyB4PSI4IiB5PSI4IiB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSI+CjxjaXJjbGUgY3g9IjgiIGN5PSI2IiByPSIzIiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMiAxNGMwLTMuMzE0IDIuNjg2LTYgNi02czYgMi42ODYgNiA2IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4KPC9zdmc+" alt="User 3" class="user-avatar">
    </div>

    <script>
        class PerplexityClone {
            constructor() {
                this.searchInput = document.getElementById('searchInput');
                this.searchBtn = document.getElementById('searchBtn');
                this.searchModeToggle = document.getElementById('searchModeToggle');
                this.currentModeSpan = document.getElementById('currentMode');
                this.resultsSection = document.getElementById('resultsSection');
                this.loadingIndicator = document.getElementById('loadingIndicator');
                this.resultsContainer = document.getElementById('resultsContainer');
                this.suggestionsBtn = document.getElementById('suggestionsBtn');
                this.suggestionsDropdown = document.getElementById('suggestionsDropdown');
                this.placeholderText = document.getElementById('placeholderText');
                
                this.currentMode = 'sync'; // 默认同步模式
                this.modes = ['同步', '流式'];
                this.modeIndex = 0;
                
                this.initEventListeners();
            }

            initEventListeners() {
                // 搜索按钮点击
                this.searchBtn.addEventListener('click', () => this.performSearch());
                
                // 回车键搜索
                this.searchInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter') {
                        this.performSearch();
                    }
                });

                // 搜索模式切换
                this.searchModeToggle.addEventListener('click', () => {
                    this.modeIndex = (this.modeIndex + 1) % this.modes.length;
                    this.currentMode = this.modeIndex === 0 ? 'sync' : 'stream';
                    this.currentModeSpan.textContent = this.modes[this.modeIndex];
                    
                    // 添加动画效果
                    this.searchModeToggle.classList.add('active');
                    setTimeout(() => {
                        this.searchModeToggle.classList.remove('active');
                    }, 200);
                    
                    console.log(`搜索模式切换为: ${this.currentMode}`);
                });

                // 建议按钮点击
                this.suggestionsBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggleSuggestionsDropdown();
                });

                // 建议问题点击
                this.suggestionsDropdown.addEventListener('click', (e) => {
                    const suggestionItem = e.target.closest('.suggestion-item');
                    if (suggestionItem) {
                        // 添加点击动画
                        suggestionItem.classList.add('active');
                        setTimeout(() => {
                            suggestionItem.classList.remove('active');
                        }, 200);
                        
                        // 填充搜索框、关闭下拉列表并执行搜索
                        this.searchInput.value = suggestionItem.dataset.query;
                        this.handlePlaceholderText();
                        this.hideSuggestionsDropdown();
                        this.performSearch();
                    }
                });

                // 点击页面其他地方关闭下拉列表
                document.addEventListener('click', () => {
                    this.hideSuggestionsDropdown();
                });

                // 阻止下拉列表内的点击事件冒泡
                this.suggestionsDropdown.addEventListener('click', (e) => {
                    e.stopPropagation();
                });

                // ESC键关闭下拉列表
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && this.suggestionsDropdown.classList.contains('active')) {
                        this.hideSuggestionsDropdown();
                    }
                });

                // 帮助按钮
                document.querySelector('.help-btn').addEventListener('click', () => {
                    alert('Ship Agent - 智能船货匹配助手，为您提供专业的海运物流咨询服务。');
                });

                // 处理占位符文本的显示和隐藏
                this.searchInput.addEventListener('input', () => {
                    this.handlePlaceholderText();
                });

                this.searchInput.addEventListener('focus', () => {
                    this.handlePlaceholderText();
                });

                this.searchInput.addEventListener('blur', () => {
                    this.handlePlaceholderText();
                });

                // 初始化占位符状态
                this.handlePlaceholderText();
            }

            handlePlaceholderText() {
                if (this.searchInput.value.trim()) {
                    // 有内容时完全隐藏
                    this.placeholderText.style.opacity = '0';
                    this.placeholderText.style.transform = 'translateY(-6px) scale(0.85)';
                    this.placeholderText.style.pointerEvents = 'none';
                } else if (document.activeElement === this.searchInput) {
                    // 聚焦时淡化显示
                    this.placeholderText.style.opacity = '0.5';
                    this.placeholderText.style.transform = 'translateY(-1px) scale(0.95)';
                    this.placeholderText.style.pointerEvents = 'none';
                } else {
                    // 默认状态
                    this.placeholderText.style.opacity = '1';
                    this.placeholderText.style.transform = 'translateY(0) scale(1)';
                    this.placeholderText.style.pointerEvents = 'none';
                }
            }

            async performSearch() {
                const query = this.searchInput.value.trim();
                if (!query) return;

                // 防止重复提交
                if (this.searchBtn.disabled) {
                    return;
                }

                const mode = this.currentMode;
                
                // 禁用提交按钮并显示处理状态
                this.setSubmitButtonState(true, mode);
                
                // 重置并显示结果区域和加载状态
                this.resetAndShowResults();
                this.showLoading();

                try {
                    // 根据搜索模式调用实际的API
                    if (mode === 'stream') {
                        await this.handleStreamRequest(query);
                    } else {
                        await this.handleSyncRequest(query);
                    }
                } catch (error) {
                    console.error('搜索错误:', error);
                    this.showError('搜索过程中发生错误，请重试。');
                } finally {
                    // 恢复提交按钮状态
                    this.setSubmitButtonState(false, mode);
                }
            }

            async handleSyncRequest(query) {
                const response = await fetch('/agent/invoke', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        input: {
                            question: query,
                            session_id: 'demo-session-' + Date.now()
                        }
                    })
                });

                const data = await response.json();
                
                if (data.output) {
                    this.showResult(query, data.output.answer, data.output.metadata, 'sync');
                } else {
                    throw new Error('响应格式错误: ' + JSON.stringify(data));
                }
            }

            async handleStreamRequest(query) {
                const response = await fetch('/agent/stream', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        input: {
                            question: query,
                            session_id: 'demo-stream-session-' + Date.now()
                        }
                    })
                });

                if (!response.body) {
                    throw new Error('流式响应不支持');
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let result = '';
                let metadata = {};

                // 初始化流式结果显示
                this.showStreamingResult(query);

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                
                                // 处理不同类型的流式数据
                                if (data.event === 'data' && data.data && data.data.chunk) {
                                    result += data.data.chunk;
                                    this.updateStreamingResult(result);
                                } else if (data.event === 'end' && data.data) {
                                    metadata = data.data.metadata || {};
                                    this.finalizeStreamingResult(result, metadata);
                                    return;
                                }
                            } catch (e) {
                                console.log('JSON解析错误:', e);
                            }
                        }
                    }
                }

                // 如果没有接收到end事件，手动完成
                this.finalizeStreamingResult(result, metadata);
            }

            showResult(question, answer, metadata, mode) {
                // 隐藏loading状态
                this.hideLoading();
                
                const processingTime = metadata?.processing_time?.toFixed(2) || 'N/A';
                const cacheStats = metadata?.cache_stats || {};
                
                this.resultsContainer.innerHTML = `
                    <div class="result-item">
                        <div class="result-question">${question}</div>
                        <div class="result-answer">${answer}</div>
                        <div class="result-sources">
                            <span style="padding: 4px 8px; background-color: #f5f5f5; border-radius: 8px; border: 1px solid var(--border-color); font-size: 12px;">
                                ⚡ ${mode === 'sync' ? '同步模式' : '流式模式'} | ⏱️ ${processingTime}s | 💾 缓存命中: ${cacheStats.hits || 0} | 🔍 缓存未命中: ${cacheStats.misses || 0}
                            </span>
                        </div>
                    </div>
                `;
            }

            showStreamingResult(question) {
                // 隐藏loading状态
                this.hideLoading();
                
                this.resultsContainer.innerHTML = `
                    <div class="result-item">
                        <div class="result-question">${question}</div>
                        <div class="result-answer" id="streamingAnswer"></div>
                        <div class="result-sources">
                            <span style="padding: 4px 8px; background-color: #f5f5f5; border-radius: 8px; border: 1px solid var(--border-color); font-size: 12px;">
                                🔄 流式回答中...
                            </span>
                        </div>
                    </div>
                `;
            }

            updateStreamingResult(text) {
                const answerDiv = document.getElementById('streamingAnswer');
                if (answerDiv) {
                    answerDiv.innerHTML = text;
                    // 添加打字机效果
                    answerDiv.scrollTop = answerDiv.scrollHeight;
                }
            }

            finalizeStreamingResult(text, metadata) {
                const answerDiv = document.getElementById('streamingAnswer');
                const metadataDiv = answerDiv?.parentElement?.querySelector('.result-sources span');
                
                if (answerDiv) {
                    answerDiv.innerHTML = text;
                }
                
                if (metadataDiv) {
                    const processingTime = metadata?.processing_time?.toFixed(2) || 'N/A';
                    const cacheStats = metadata?.cache_stats || {};
                    
                    metadataDiv.innerHTML = `
                        ✅ 流式完成 | ⏱️ ${processingTime}s | 💾 缓存命中: ${cacheStats.hits || 0} | 🔍 缓存未命中: ${cacheStats.misses || 0}
                    `;
                }
            }

            showResults() {
                this.resultsSection.classList.add('active');
                this.resultsSection.scrollIntoView({ behavior: 'smooth' });
            }

            resetAndShowResults() {
                // 清空之前的结果内容
                this.resultsContainer.innerHTML = '';
                // 显示结果区域
                this.showResults();
            }

            setSubmitButtonState(isProcessing, mode) {
                if (isProcessing) {
                    // 禁用按钮并显示处理状态
                    this.searchBtn.disabled = true;
                    this.searchBtn.style.opacity = '0.6';
                    this.searchBtn.style.cursor = 'not-allowed';
                    this.searchBtn.title = mode === 'sync' ? '正在处理...' : '正在流式回答...';
                } else {
                    // 恢复按钮状态
                    this.searchBtn.disabled = false;
                    this.searchBtn.style.opacity = '1';
                    this.searchBtn.style.cursor = 'pointer';
                    this.searchBtn.title = '搜索';
                }
            }

            showSuggestionsDropdown() {
                this.suggestionsDropdown.classList.add('active');
                document.querySelector('.search-input-wrapper').classList.add('dropdown-active');
            }

            hideSuggestionsDropdown() {
                this.suggestionsDropdown.classList.remove('active');
                document.querySelector('.search-input-wrapper').classList.remove('dropdown-active');
            }

            toggleSuggestionsDropdown() {
                if (this.suggestionsDropdown.classList.contains('active')) {
                    this.hideSuggestionsDropdown();
                } else {
                    this.showSuggestionsDropdown();
                }
            }

            showLoading() {
                this.loadingIndicator.style.display = 'flex';
                this.resultsContainer.innerHTML = '';
            }

            hideLoading() {
                this.loadingIndicator.style.display = 'none';
            }

            showError(message) {
                // 隐藏loading状态
                this.hideLoading();
                
                // 显示错误信息
                this.resultsContainer.innerHTML = `
                    <div class="result-item" style="border-color: #ff4444;">
                        <div class="result-question">出现错误</div>
                        <div class="result-answer" style="color: #ff4444;">${message}</div>
                    </div>
                `;
            }
        }

        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new PerplexityClone();
        });

        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', () => {
            // 搜索框聚焦效果
            const searchInput = document.getElementById('searchInput');
            const searchWrapper = document.querySelector('.search-input-wrapper');
            
            searchInput.addEventListener('focus', () => {
                searchWrapper.style.transform = 'translateY(-1px)';
            });
            
            searchInput.addEventListener('blur', () => {
                searchWrapper.style.transform = 'translateY(0)';
            });

            // 平滑滚动到搜索框
            searchInput.addEventListener('focus', () => {
                searchInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
            });

            // 状态点动画
            const statusDot = document.querySelector('.status-dot');
            if (statusDot) {
                setInterval(() => {
                    statusDot.style.opacity = statusDot.style.opacity === '0.6' ? '1' : '0.6';
                }, 1000);
            }
        });
    </script>
</body>
</html>'''
        return html_content
    
    # 添加LangServe路由
    add_routes(
        app,
        runnable_agent,
        path="/agent",
        enabled_endpoints=["invoke", "batch", "stream", "stream_log"],
        input_type=AgentInput,
        config_keys=["configurable"]
    )
    
    logger.info("LangServe路由已添加到 /agent")
    
    return app


# 创建应用实例
app = create_app()


if __name__ == "__main__":
    import uvicorn
    
    logger.info("🚀 启动CJHY-KGAgent API服务...")
    
    uvicorn.run(
        "src.api.server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    ) 