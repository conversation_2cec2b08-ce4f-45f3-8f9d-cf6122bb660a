"""
航线节点ETL模块
实现ShippingRoute和RouteMonthStat节点的创建和管理
符合数据库设计文档v4.0的航线维度要求
"""

import pandas as pd
from datetime import datetime
from typing import Dict, Any, List, Optional
import logging

from .database import DatabaseManager

logger = logging.getLogger(__name__)


class RouteETL:
    """航线ETL处理器 - 符合数据库设计文档v4.0"""
    
    def __init__(self, database_manager: DatabaseManager, batch_size: int = 1000):
        """
        初始化航线ETL处理器
        
        Args:
            database_manager: 数据库管理器
            batch_size: 批处理大小
        """
        self.db_manager = database_manager
        self.batch_size = batch_size
        logger.info(f"初始化航线ETL处理器，批处理大小: {batch_size}")
    
    def create_shipping_routes_and_month_stats(self, ship_filter: str = None, time_filter: str = None, limit: int = 10000) -> Dict[str, int]:
        """
        合并步骤1和步骤2：同时创建航线节点和月度统计节点

        Args:
            ship_filter: 船舶过滤条件
            time_filter: 时间过滤条件
            limit: 记录数限制

        Returns:
            Dict: 创建的节点统计
        """
        try:
            # 构建SQL查询 - 同时提取航线基础信息和月度统计
            base_sql = """
             select * from(
                SELECT
                    t.ym,
                    t.merge_city_name_o as origin_port,
                    t.merge_city_name_d as destination_port,
                    COUNT(DISTINCT t.ship_id) as total_ship_count,
                    COUNT(*) as total_voyage_count,
                    SUM(t.capacity) as total_cargo_ton,
                    SUM(t.TURNOVER) as total_turnover_tonkm,
                    AVG(t.MILEAGE) as avg_distance_km,
                    0 as avg_load_ratio,
                    COUNT(*) as frequency,
                    COUNT(*) as route_frequency
                FROM yssjtj.od_dwd_statistics t
                JOIN yssjtj.dim_l_ship_basic_data s ON t.ship_id = s.ship_id
                WHERE 1=1
                  AND t.merge_city_name_o IS NOT NULL
                  AND t.merge_city_name_d IS NOT NULL
                  AND t.merge_city_name_o != t.merge_city_name_d
                  and t.basin_o='干线' and t.basin_d='干线'
            """

            # 动态添加过滤条件
            # if ship_filter:
            #     base_sql += f" AND {ship_filter}"
            if time_filter:
                base_sql += f" AND {time_filter}"
            else:
                base_sql += " AND t.ym >= '202407'"

            base_sql += """
                GROUP BY t.ym, t.merge_city_name_o, t.merge_city_name_d
                HAVING COUNT(*) >= 1000
                ORDER BY t.ym DESC, SUM(t.capacity) DESC
                ) where rownum<=100
            """

            # if limit:
            #     base_sql += f" AND ROWNUM <= {limit}"

            logger.info("从Oracle执行合并的航线和月度统计查询")

            # 执行查询
            df = self.db_manager.oracle.execute_query_to_dataframe(base_sql)
            df.columns = df.columns.str.lower()

            logger.info(f"从Oracle提取了{len(df)}条航线月度统计记录")

            if df.empty:
                logger.warning("从Oracle查询到的航线数据为空")
                return {"routes_created": 0, "month_stats_created": 0, "errors": 0}

            return self._create_routes_and_month_stats(df)
            
        except Exception as e:
            logger.error(f"创建航线和月度统计节点失败: {e}")
            return {"routes_created": 0, "month_stats_created": 0, "errors": 1}
    
    def _create_routes_and_month_stats(self, df: pd.DataFrame) -> Dict[str, int]:
        """
        合并创建航线节点和月度统计节点

        Args:
            df: 航线月度统计数据DataFrame

        Returns:
            Dict: 创建结果统计
        """
        routes_created = 0
        month_stats_created = 0
        error_count = 0

        # 首先提取唯一的航线信息
        route_df = df.groupby(['origin_port', 'destination_port']).agg({
            'avg_distance_km': 'mean',
            'frequency': 'first'  # 使用 frequency 而不是 route_frequency
        }).reset_index()

        # 创建航线节点
        route_result = self._create_shipping_route_nodes(route_df)
        routes_created = route_result.get("created", 0)
        error_count += route_result.get("errors", 0)

        # 创建月度统计节点
        month_result = self._create_route_month_stat_nodes(df)
        month_stats_created = month_result.get("created", 0)
        error_count += month_result.get("errors", 0)

        return {
            "routes_created": routes_created,
            "month_stats_created": month_stats_created,
            "errors": error_count
        }

    def _create_shipping_route_nodes(self, df: pd.DataFrame) -> Dict[str, int]:
        """
        创建航线节点
        
        Args:
            df: 航线数据DataFrame
            
        Returns:
            Dict: 创建结果统计
        """
        created_count = 0
        error_count = 0
        
        # 按batch_size分块处理
        for i in range(0, len(df), self.batch_size):
            chunk = df.iloc[i:i+self.batch_size]
            
            try:
                with self.db_manager.neo4j.get_session() as session:
                    with session.begin_transaction() as tx:
                        # 准备批量数据
                        rows = []
                        for _, row in chunk.iterrows():
                            origin_port = str(row['origin_port']).strip()
                            destination_port = str(row['destination_port']).strip()
                            
                            # 生成航线ID和名称
                            route_code = f"{origin_port[:2].upper()}-{destination_port[:2].upper()}"
                            route_id = f"{route_code}-{hash(f'{origin_port}-{destination_port}') % 1000:03d}"
                            route_name = f"{origin_port}-{destination_port}航线"
                            
                            rows.append({
                                'routeId': route_id,
                                'routeName': route_name,
                                'routeCode': route_code,
                                'originPortName': origin_port,
                                'destinationPortName': destination_port,
                                'distance_km': float(row['avg_distance_km']) if pd.notna(row['avg_distance_km']) else 0.0,
                                'frequency': int(row['frequency']) if pd.notna(row['frequency']) else 0
                            })
                        
                        # 批量创建航线节点
                        tx.run("""
                            UNWIND $rows AS row
                            MERGE (sr:ShippingRoute {routeId: row.routeId})
                            SET sr.routeName = row.routeName,
                                sr.routeCode = row.routeCode,
                                sr.originPortName = row.originPortName,
                                sr.destinationPortName = row.destinationPortName,
                                sr.distance_km = row.distance_km,
                                sr.lastUpdated = datetime()
                            WITH sr, row
                            
                            // 建立与起点港口的关系
                            WITH sr, row
                            OPTIONAL MATCH (pO:Port {name: row.originPortName})
                            FOREACH (port IN CASE WHEN pO IS NOT NULL THEN [pO] ELSE [] END |
                                MERGE (sr)-[:ROUTE_ORIGIN]->(port)
                            )

                            // 建立与终点港口的关系
                            WITH sr, row
                            OPTIONAL MATCH (pD:Port {name: row.destinationPortName})
                            FOREACH (port IN CASE WHEN pD IS NOT NULL THEN [pD] ELSE [] END |
                                MERGE (sr)-[:ROUTE_DESTINATION]->(port)
                            )

                            // 建立与流域的关系
                            WITH sr, row
                            OPTIONAL MATCH (b:Basin {name: "长江"})
                            FOREACH (basin IN CASE WHEN b IS NOT NULL THEN [b] ELSE [] END |
                                MERGE (sr)-[:ROUTE_IN_BASIN]->(basin)
                            )
                        """, rows=rows)
                        
                        tx.commit()
                        created_count += len(chunk)
                        logger.info(f"批量创建航线节点 {len(chunk)} 个")
                        
            except Exception as e:
                error_count += len(chunk)
                logger.error(f"批量创建航线节点失败: {e}")
        
        return {"created": created_count, "errors": error_count}

    
    def _create_route_month_stat_nodes(self, df: pd.DataFrame) -> Dict[str, int]:
        """
        创建航线月度统计节点
        
        Args:
            df: 航线月度统计数据DataFrame
            
        Returns:
            Dict: 创建结果统计
        """
        created_count = 0
        error_count = 0
        
        # 按batch_size分块处理
        for i in range(0, len(df), self.batch_size):
            chunk = df.iloc[i:i+self.batch_size]
            
            try:
                with self.db_manager.neo4j.get_session() as session:
                    with session.begin_transaction() as tx:
                        # 准备批量数据
                        rows = []
                        for _, row in chunk.iterrows():
                            origin_port = str(row['origin_port']).strip()
                            destination_port = str(row['destination_port']).strip()
                            ym = str(row['ym'])
                            
                            # 生成航线标识
                            route_code = f"{origin_port[:2].upper()}-{destination_port[:2].upper()}"
                            route_id = f"{route_code}-{hash(f'{origin_port}-{destination_port}') % 1000:03d}"
                            
                            # 生成统计节点名称
                            stat_name = f"{route_id}_{ym}"
                            
                            rows.append({
                                'name': stat_name,
                                'routeId': route_id,
                                'ym': ym,
                                'totalShipCount': int(row['total_ship_count']) if pd.notna(row['total_ship_count']) else 0,
                                'totalVoyageCount': int(row['total_voyage_count']) if pd.notna(row['total_voyage_count']) else 0,
                                'totalCargo_ton': float(row['total_cargo_ton']) if pd.notna(row['total_cargo_ton']) else 0.0,
                                'totalTurnover_tonkm': float(row['total_turnover_tonkm']) if pd.notna(row['total_turnover_tonkm']) else 0.0,
                                'avgLoadRatio': float(row['avg_load_ratio']) if pd.notna(row['avg_load_ratio']) else 0.0,
                                'utilizationRate': 0.85  # 默认值，后续可以根据实际数据计算
                            })
                        
                        # 批量创建航线月度统计节点
                        tx.run("""
                            UNWIND $rows AS row
                            MERGE (rms:RouteMonthStat {name: row.name})
                            SET rms.totalShipCount = row.totalShipCount,
                                rms.totalVoyageCount = row.totalVoyageCount,
                                rms.totalCargo_ton = row.totalCargo_ton,
                                rms.totalTurnover_tonkm = row.totalTurnover_tonkm,
                                rms.avgLoadRatio = row.avgLoadRatio,
                                rms.utilizationRate = row.utilizationRate,
                                rms.lastUpdated = datetime()
                            WITH rms, row
                            
                            // 建立与航线的关系
                            OPTIONAL MATCH (sr:ShippingRoute {routeId: row.routeId})
                            FOREACH (route IN CASE WHEN sr IS NOT NULL THEN [sr] ELSE [] END |
                                MERGE (rms)-[:ROUTE_STAT_FOR_ROUTE]->(route)
                            )
                            
                            // 建立与年月的关系
                            MERGE (ym:YearMonth {ym: row.ym})
                            MERGE (rms)-[:ROUTE_STAT_FOR_MONTH]->(ym)
                        """, rows=rows)
                        
                        tx.commit()
                        created_count += len(chunk)
                        logger.info(f"批量创建航线月度统计节点 {len(chunk)} 个")
                        
            except Exception as e:
                error_count += len(chunk)
                logger.error(f"批量创建航线月度统计节点失败: {e}")
        
        return {"created": created_count, "errors": error_count}

    def create_route_cargo_stats(self, ship_filter: str = None, time_filter: str = None, limit: int = 10000) -> Dict[str, int]:
        """
        创建航线月度分货类统计节点

        Args:
            ship_filter: 船舶过滤条件
            time_filter: 时间过滤条件
            limit: 记录数限制

        Returns:
            Dict: 创建结果统计
        """
        try:
            # 首先从Neo4j获取已存在的航线列表
            existing_routes = self._get_existing_routes()
            if not existing_routes:
                logger.warning("没有找到已存在的航线，跳过分货类统计创建")
                return {"created": 0, "errors": 0}

            # 构建航线过滤条件
            route_conditions = []
            for route in existing_routes:
                route_conditions.append(f"(t.merge_city_name_o = '{route['origin']}' AND t.merge_city_name_d = '{route['destination']}')")

            route_filter = " OR ".join(route_conditions)

            # 构建SQL查询 - 只查询已存在航线的分货类统计数据
            base_sql = f"""
                SELECT
                    t.ym,
                    t.merge_city_name_o as origin_port,
                    t.merge_city_name_d as destination_port,
                    t.cargo_name,
                    COUNT(DISTINCT t.ship_id) as ship_count,
                    COUNT(*) as voyage_count,
                    SUM(t.capacity) as cargo_ton,
                    AVG(t.capacity) as avg_cargo_per_voyage_ton
                FROM yssjtj.od_dwd_statistics t
                JOIN yssjtj.dim_l_ship_basic_data s ON t.ship_id = s.ship_id
                WHERE 1=1
                  AND t.merge_city_name_o IS NOT NULL
                  AND t.merge_city_name_d IS NOT NULL
                  AND t.merge_city_name_o != t.merge_city_name_d
                  AND t.cargo_name IS NOT NULL
                  AND ({route_filter})
            """

            # 动态添加过滤条件
            # if ship_filter:
            #     base_sql += f" AND {ship_filter}"
            if time_filter:
                base_sql += f" AND {time_filter}"
            else:
                base_sql += " AND t.ym >= '202401'"

            base_sql += """
                GROUP BY t.ym, t.merge_city_name_o, t.merge_city_name_d, t.cargo_name
                HAVING COUNT(*) >= 1000
                ORDER BY t.ym DESC, SUM(t.capacity) DESC
            """

            # if limit:
            #     base_sql += f" AND ROWNUM <= {limit}"

            logger.info("从Oracle执行航线分货类统计查询")

            # 执行查询
            df = self.db_manager.oracle.execute_query_to_dataframe(base_sql)
            df.columns = df.columns.str.lower()

            logger.info(f"从Oracle加载了{len(df)}条航线分货类统计记录")

            if df.empty:
                logger.warning("从Oracle查询到的航线分货类统计数据为空")
                return {"created": 0, "errors": 0}

            return self._create_route_cargo_stat_nodes(df)

        except Exception as e:
            logger.error(f"创建航线分货类统计节点失败: {e}")
            return {"created": 0, "errors": 1}

    def _create_route_cargo_stat_nodes(self, df: pd.DataFrame) -> Dict[str, int]:
        """
        创建航线月度分货类统计节点

        Args:
            df: 航线分货类统计数据DataFrame

        Returns:
            Dict: 创建结果统计
        """
        created_count = 0
        error_count = 0

        # 按batch_size分块处理
        for i in range(0, len(df), self.batch_size):
            chunk = df.iloc[i:i+self.batch_size]

            try:
                with self.db_manager.neo4j.get_session() as session:
                    with session.begin_transaction() as tx:
                        # 准备批量数据
                        rows = []
                        for _, row in chunk.iterrows():
                            origin_port = str(row['origin_port']).strip()
                            destination_port = str(row['destination_port']).strip()
                            ym = str(row['ym'])
                            cargo_type = str(row['cargo_name']).strip()

                            # 生成航线标识
                            route_code = f"{origin_port[:2].upper()}-{destination_port[:2].upper()}"
                            route_id = f"{route_code}-{hash(f'{origin_port}-{destination_port}') % 1000:03d}"

                            # 生成统计节点名称
                            stat_name = f"{route_id}_{ym}_{cargo_type}"

                            rows.append({
                                'name': stat_name,
                                'routeId': route_id,
                                'ym': ym,
                                'cargoType': cargo_type,
                                'cargo_ton': float(row['cargo_ton']) if pd.notna(row['cargo_ton']) else 0.0,
                                'shipCount': int(row['ship_count']) if pd.notna(row['ship_count']) else 0,
                                'voyageCount': int(row['voyage_count']) if pd.notna(row['voyage_count']) else 0,
                                'avgCargoPerVoyage_ton': float(row['avg_cargo_per_voyage_ton']) if pd.notna(row['avg_cargo_per_voyage_ton']) else 0.0
                            })

                        # 批量创建航线分货类统计节点
                        tx.run("""
                            UNWIND $rows AS row
                            MERGE (rmcs:RouteMonthCargoStat {name: row.name})
                            SET rmcs.cargo_ton = row.cargo_ton,
                                rmcs.shipCount = row.shipCount,
                                rmcs.voyageCount = row.voyageCount,
                                rmcs.avgCargoPerVoyage_ton = row.avgCargoPerVoyage_ton,
                                rmcs.lastUpdated = datetime()
                            WITH rmcs, row

                            // 建立与航线的关系
                            WITH rmcs, row
                            OPTIONAL MATCH (sr:ShippingRoute {routeId: row.routeId})
                            FOREACH (route IN CASE WHEN sr IS NOT NULL THEN [sr] ELSE [] END |
                                MERGE (rmcs)-[:ROUTE_CARGO_STAT_FOR_ROUTE]->(route)
                            )

                            // 建立与年月的关系
                            WITH rmcs, row
                            MERGE (ym:YearMonth {ym: row.ym})
                            MERGE (rmcs)-[:ROUTE_CARGO_STAT_FOR_MONTH]->(ym)

                            // 建立与货物类型的关系
                            WITH rmcs, row
                            OPTIONAL MATCH (ct:CargoType {subName: row.cargoType})
                            FOREACH (cargo IN CASE WHEN ct IS NOT NULL THEN [ct] ELSE [] END |
                                MERGE (rmcs)-[:ROUTE_CARGO_STAT_FOR_TYPE]->(cargo)
                            )
                        """, rows=rows)

                        tx.commit()
                        created_count += len(chunk)
                        logger.info(f"批量创建航线分货类统计节点 {len(chunk)} 个")

            except Exception as e:
                error_count += len(chunk)
                logger.error(f"批量创建航线分货类统计节点失败: {e}")

        return {"created": created_count, "errors": error_count}

    def _get_existing_routes(self) -> List[Dict[str, str]]:
        """
        从Neo4j获取已存在的航线列表

        Returns:
            List: 已存在的航线列表，包含origin和destination
        """
        try:
            with self.db_manager.neo4j.get_session() as session:
                # 首先检查是否存在ShippingRoute节点
                count_result = session.run("MATCH (sr:ShippingRoute) RETURN count(sr) as count")
                count_record = count_result.single()

                if not count_record or count_record['count'] == 0:
                    logger.info("数据库中还没有ShippingRoute节点")
                    return []

                # 获取已存在的航线
                result = session.run("""
                    MATCH (sr:ShippingRoute)
                    WHERE sr.originPortName IS NOT NULL AND sr.destinationPortName IS NOT NULL
                    RETURN sr.originPortName as origin, sr.destinationPortName as destination
                """)

                routes = []
                for record in result:
                    if record['origin'] and record['destination']:
                        routes.append({
                            'origin': record['origin'],
                            'destination': record['destination']
                        })

                logger.info(f"获取到{len(routes)}条已存在的航线")
                return routes

        except Exception as e:
            logger.warning(f"获取已存在航线失败，可能是首次运行: {e}")
            return []

    # def fix_ship_month_line_stats_relationships(self, limit: int = 10000) -> Dict[str, int]:
    #     """
    #     修复ShipMonthLineStat节点的关系，连接到ShippingRoute节点

    #     Args:
    #         limit: 记录数限制

    #     Returns:
    #         Dict: 修复结果统计
    #     """
    #     try:
    #         logger.info("开始修复ShipMonthLineStat节点的航线关系")

    #         with self.db_manager.neo4j.get_session() as session:
    #             # 查询现有的ShipMonthLineStat节点
    #             result = session.run("""
    #                 MATCH (smls:ShipMonthLineStat)
    #                 WHERE smls.portO IS NOT NULL AND smls.portD IS NOT NULL
    #                 RETURN smls.name as stat_name, smls.portO as origin_port, smls.portD as destination_port
    #                 LIMIT $limit
    #             """, limit=limit)

    #             stats_to_fix = []
    #             for record in result:
    #                 origin_port = record['origin_port'].strip()
    #                 destination_port = record['destination_port'].strip()

    #                 # 生成对应的航线ID
    #                 route_code = f"{origin_port[:2].upper()}-{destination_port[:2].upper()}"
    #                 route_id = f"{route_code}-{hash(f'{origin_port}-{destination_port}') % 1000:03d}"

    #                 stats_to_fix.append({
    #                     'stat_name': record['stat_name'],
    #                     'route_id': route_id
    #                 })

    #             logger.info(f"找到{len(stats_to_fix)}个需要修复关系的ShipMonthLineStat节点")

    #             # 批量修复关系
    #             fixed_count = 0
    #             error_count = 0

    #             for i in range(0, len(stats_to_fix), self.batch_size):
    #                 chunk = stats_to_fix[i:i+self.batch_size]

    #                 try:
    #                     with session.begin_transaction() as tx:
    #                         tx.run("""
    #                             UNWIND $rows AS row
    #                             MATCH (smls:ShipMonthLineStat {name: row.stat_name})
    #                             OPTIONAL MATCH (sr:ShippingRoute {routeId: row.route_id})
    #                             FOREACH (route IN CASE WHEN sr IS NOT NULL THEN [sr] ELSE [] END |
    #                                 MERGE (smls)-[:STAT_FOR_ROUTE]->(route)
    #                             )
    #                         """, rows=chunk)

    #                         tx.commit()
    #                         fixed_count += len(chunk)
    #                         logger.info(f"修复了{len(chunk)}个ShipMonthLineStat节点的航线关系")

    #                 except Exception as e:
    #                     error_count += len(chunk)
    #                     logger.error(f"修复ShipMonthLineStat关系失败: {e}")

    #             return {"fixed": fixed_count, "errors": error_count}

    #     except Exception as e:
    #         logger.error(f"修复ShipMonthLineStat关系失败: {e}")
    #         return {"fixed": 0, "errors": 1}

    def execute_full_route_etl(self, ship_filter: str = None, time_filter: str = None, limit: int = 10000) -> Dict[str, Any]:
        """
        执行完整的航线ETL流程

        Args:
            ship_filter: 船舶过滤条件
            time_filter: 时间过滤条件
            limit: 记录数限制

        Returns:
            Dict: 完整的ETL执行结果
        """
        start_time = datetime.now()
        logger.info("开始执行航线ETL流程")

        results = {
            "start_time": start_time,
            "routes_and_month_stats": {"routes_created": 0, "month_stats_created": 0, "errors": 0},
            "route_cargo_stats": {"created": 0, "errors": 0},
            "total_created": 0,
            "total_errors": 0,
            "duration_seconds": 0
        }

        try:
            # 步骤1+2: 合并创建航线节点和月度统计节点
            logger.info("步骤1+2: 合并创建航线节点和月度统计节点")
            logger.info(f"调用参数: ship_filter={ship_filter}, time_filter={time_filter}, limit={limit}")

            combined_result = self.create_shipping_routes_and_month_stats(ship_filter, time_filter, limit)
            logger.info(f"步骤1+2完成，结果: {combined_result}")
            results["routes_and_month_stats"] = combined_result

            # 步骤3: 创建航线分货类统计节点（依赖前面创建的航线）
            logger.info("步骤3: 创建航线分货类统计节点（过滤已存在航线）")
            route_cargo_result = self.create_route_cargo_stats(ship_filter, time_filter, limit)
            logger.info(f"步骤3完成，结果: {route_cargo_result}")
            results["route_cargo_stats"] = route_cargo_result

            # 计算总计
            results["total_created"] = (
                combined_result.get("routes_created", 0) +
                combined_result.get("month_stats_created", 0) +
                route_cargo_result.get("created", 0)
            )
            results["total_errors"] = (
                combined_result.get("errors", 0) +
                route_cargo_result.get("errors", 0)
            )

            end_time = datetime.now()
            results["end_time"] = end_time
            results["duration_seconds"] = (end_time - start_time).total_seconds()

            logger.info(f"航线ETL流程完成，耗时: {results['duration_seconds']:.2f}秒")
            logger.info(f"总计创建: {results['total_created']} 个节点")
            logger.info(f"总计错误: {results['total_errors']} 个")

            return results

        except Exception as e:
            logger.error(f"航线ETL流程执行失败: {e}")
            end_time = datetime.now()
            results["end_time"] = end_time
            results["duration_seconds"] = (end_time - start_time).total_seconds()
            results["error"] = str(e)
            return results

    def get_route_statistics(self) -> Dict[str, Any]:
        """
        获取航线相关的统计信息

        Returns:
            Dict: 航线统计信息
        """
        try:
            with self.db_manager.neo4j.get_session() as session:
                result = session.run("""
                    MATCH (sr:ShippingRoute)
                    OPTIONAL MATCH (sr)<-[:ROUTE_STAT_FOR_ROUTE]-(rms:RouteMonthStat)
                    OPTIONAL MATCH (sr)<-[:ROUTE_CARGO_STAT_FOR_ROUTE]-(rmcs:RouteMonthCargoStat)
                    OPTIONAL MATCH (sr)<-[:STAT_FOR_ROUTE]-(smls:ShipMonthLineStat)
                    RETURN
                        count(DISTINCT sr) as total_routes,
                        count(DISTINCT rms) as total_route_month_stats,
                        count(DISTINCT rmcs) as total_route_cargo_stats,
                        count(DISTINCT smls) as total_ship_line_stats
                """)

                record = result.single()
                if record:
                    stats = {
                        "total_routes": record["total_routes"],
                        "total_route_month_stats": record["total_route_month_stats"],
                        "total_route_cargo_stats": record["total_route_cargo_stats"],
                        "total_ship_line_stats": record["total_ship_line_stats"],
                        "query_time": datetime.now().isoformat()
                    }

                    logger.info(f"航线统计信息: {stats}")
                    return stats
                else:
                    return {"error": "无法获取航线统计信息"}

        except Exception as e:
            logger.error(f"获取航线统计信息失败: {e}")
            return {"error": str(e)}
