"""
长江航运智能指标问答Agent (CJHY-KGAgent)
Neo4j数据库客户端模块

封装Neo4j数据库的连接管理、查询执行、错误处理等功能
"""

from typing import Dict, List, Any, Optional
from contextlib import contextmanager
from loguru import logger
from neo4j import GraphDatabase
import neo4j
import pandas as pd

try:
    from ..config import settings
except ImportError:
    # 兼容直接运行的情况
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(__file__)))
    from src.config import settings

try:
    import cx_Oracle  # 优先尝试传统的cx_Oracle
    ORACLE_AVAILABLE = True
except ImportError:
    # 尝试使用python-oracledb (thin 模式)
    try:
        import oracledb as cx_Oracle  # type: ignore
        cx_Oracle.init_oracle_client = getattr(cx_Oracle, "init_oracle_client", lambda *args, **kwargs: None)
        # python-oracledb 默认 thin 模式，无需客户端库
        ORACLE_AVAILABLE = True
        logger.info("已使用 python-oracledb 作为 Oracle 驱动 (thin 模式)")
    except ImportError:
        ORACLE_AVAILABLE = False
        logger.warning("cx_Oracle 和 python-oracledb 均不可用，Oracle 功能将被禁用。请执行 'pip install cx_Oracle' 或 'pip install oracledb' 来启用。")


class Neo4jClient:
    """Neo4j数据库客户端"""
    
    def __init__(self):
        """初始化Neo4j客户端"""
        self.driver = None
        self._connect()
    
    def _connect(self):
        """连接到Neo4j数据库"""
        try:
            logger.info(f"连接Neo4j数据库: {settings.neo4j.uri}")
            logger.info(f"使用用户名: {settings.neo4j.username}")
            
            self.driver = GraphDatabase.driver(
                settings.neo4j.uri,
                auth=(settings.neo4j.username, settings.neo4j.password)
            )
            # 测试连接
            with self.driver.session() as session:
                session.run("RETURN 1")
            logger.info("✅ Neo4j数据库连接成功")
        except Exception as e:
            logger.error(f"❌ Neo4j数据库连接失败: {e}")
            logger.error("🔧 请检查Neo4j配置和服务状态")
            self.driver = None
    
    @contextmanager
    def get_session(self):
        """获取Neo4j会话上下文管理器"""
        if not self.driver:
            raise Exception("Neo4j驱动未初始化")
        
        session = self.driver.session(database=settings.neo4j.database)
        try:
            yield session
        finally:
            session.close()
    
    def execute_query(self, query: str, parameters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """执行Cypher查询"""
        if not self.driver:
            logger.warning("Neo4j驱动未连接，无法执行查询")
            return []
            
        try:
            with self.get_session() as session:
                result = session.run(query, parameters or {})
                return [record.data() for record in result]
        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            logger.error(f"查询语句: {query}")
            logger.error(f"参数: {parameters}")
            return []
    
    def execute_write_query(self, query: str, parameters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """执行写入查询（事务）"""
        if not self.driver:
            logger.warning("Neo4j驱动未连接，无法执行写入查询")
            return []
            
        try:
            with self.get_session() as session:
                with session.begin_transaction() as tx:
                    result = tx.run(query, parameters or {})
                    records = [record.data() for record in result]
                    tx.commit()
                    return records
        except Exception as e:
            logger.error(f"写入查询执行失败: {e}")
            logger.error(f"查询语句: {query}")
            logger.error(f"参数: {parameters}")
            return []
    
    def check_connection(self) -> bool:
        """检查数据库连接状态"""
        if not self.driver:
            return False
            
        try:
            with self.get_session() as session:
                session.run("RETURN 1")
            return True
        except Exception as e:
            logger.error(f"数据库连接检查失败: {e}")
            return False
    
    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库基本信息"""
        if not self.driver:
            return {
                "error": "Neo4j驱动未连接",
                "node_count": 0,
                "relationship_count": 0,
                "labels": [],
                "relationship_types": [],
                "database": settings.neo4j.database
            }
        
        try:
            # 获取节点数量
            node_count_result = self.execute_query("MATCH (n) RETURN count(n) as count")
            node_count = node_count_result[0]['count'] if node_count_result else 0
            
            # 获取关系数量
            rel_count_result = self.execute_query("MATCH ()-[r]->() RETURN count(r) as count")
            relationship_count = rel_count_result[0]['count'] if rel_count_result else 0
            
            # 获取标签
            labels_result = self.execute_query("CALL db.labels()")
            labels = [record['label'] for record in labels_result]
            
            # 获取关系类型
            rel_types_result = self.execute_query("CALL db.relationshipTypes()")
            relationship_types = [record['relationshipType'] for record in rel_types_result]
            
            return {
                "node_count": node_count,
                "relationship_count": relationship_count,
                "labels": labels,
                "relationship_types": relationship_types,
                "database": settings.neo4j.database
            }
        except Exception as e:
            logger.error(f"获取数据库信息失败: {e}")
            return {
                "error": str(e),
                "node_count": 0,
                "relationship_count": 0,
                "labels": [],
                "relationship_types": [],
                "database": settings.neo4j.database
            }
    
    def get_node_counts_by_label(self) -> Dict[str, int]:
        """获取各标签的节点数量"""
        try:
            labels_result = self.execute_query("CALL db.labels()")
            labels = [record['label'] for record in labels_result]
            
            counts = {}
            for label in labels:
                count_result = self.execute_query(f"MATCH (n:{label}) RETURN count(n) as count")
                counts[label] = count_result[0]['count'] if count_result else 0
            
            return counts
        except Exception as e:
            logger.error(f"获取节点数量统计失败: {e}")
            return {}
    
    def get_sample_nodes(self, label: str, limit: int = 5) -> List[Dict[str, Any]]:
        """获取指定标签的示例节点"""
        try:
            query = f"MATCH (n:{label}) RETURN n LIMIT $limit"
            result = self.execute_query(query, {"limit": limit})
            return [record['n'] for record in result]
        except Exception as e:
            logger.error(f"获取示例节点失败: {e}")
            return []
    
    def close(self):
        """关闭数据库连接"""
        if self.driver:
            logger.info("关闭Neo4j连接")
            self.driver.close()
            self.driver = None


class OracleClient:
    """Oracle数据库客户端"""
    
    def __init__(self, dsn: str = None, username: str = None, password: str = None):
        """
        初始化Oracle客户端

        Args:
            dsn: Oracle数据库DSN (如未提供则从配置读取)
            username: Oracle用户名 (如未提供则从配置读取)
            password: Oracle密码 (如未提供则从配置读取)
        """
        # 优先使用传入参数，否则使用默认配置
        self.dsn = dsn or "172.17.2.183:1521/chtgldb"
        self.username = username or "yssjtj"
        self.password = password or "Longshine#1"
        self.connection = None

        if all([self.dsn, self.username, self.password]):
            self._connect()
    
    def _connect(self):
        """连接到Oracle数据库"""
        try:
            logger.info(f"连接Oracle数据库: {self.dsn}")
            logger.info(f"使用用户名: {self.username}")
            
            self.connection = cx_Oracle.connect(
                user=self.username,
                password=self.password,
                dsn=self.dsn
            )
            
            # 测试连接
            cursor = self.connection.cursor()
            cursor.execute("SELECT 1 FROM DUAL")
            cursor.close()
            
            logger.info("✅ Oracle数据库连接成功")
        except Exception as e:
            logger.error(f"❌ Oracle数据库连接失败: {e}")
            logger.error("🔧 解决方案:")
            self.connection = None
    
    def check_connection(self) -> bool:
        """检查Oracle数据库连接状态"""
        if not self.connection:
            return False
            
        try:
            self.connection.ping()
            return True
        except Exception as e:
            logger.error(f"Oracle连接检查失败: {e}")
            return False
    
    def get_connection(self):
        """获取Oracle数据库连接"""
        if not ORACLE_AVAILABLE:
            raise Exception("cx_Oracle not available. Please install: pip install cx_Oracle")
        
        if not all([self.dsn, self.username, self.password]):
            raise Exception("Oracle连接参数不完整，请检查配置")
        
        if not self.connection or not self.check_connection():
            self._connect()
        
        if not self.connection:
            raise Exception("Oracle连接建立失败")
        
        return self.connection
    
    def execute_query(self, query: str, parameters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """执行Oracle查询并返回结果"""
        try:
            conn = self.get_connection()
            df = pd.read_sql(query, conn, params=parameters or {})
            return df.to_dict('records')
        except Exception as e:
            logger.error(f"Oracle查询执行失败: {e}")
            logger.error(f"查询语句: {query}")
            logger.error(f"参数: {parameters}")
            return []
    
    def execute_query_to_dataframe(self, query: str, parameters: Dict[str, Any] = None) -> pd.DataFrame:
        """执行Oracle查询并返回DataFrame"""
        try:
            conn = self.get_connection()
            df = pd.read_sql(query, conn, params=parameters or {})
            return df
        except Exception as e:
            logger.error(f"Oracle查询执行失败: {e}")
            logger.error(f"查询语句: {query}")
            logger.error(f"参数: {parameters}")
            return pd.DataFrame()
    
    def close(self):
        """关闭Oracle数据库连接"""
        if self.connection:
            try:
                self.connection.close()
                logger.info("Oracle数据库连接已关闭")
            except Exception as e:
                logger.error(f"关闭Oracle连接失败: {e}")
            finally:
                self.connection = None


class DatabaseManager:
    """数据库管理器 - 统一管理Neo4j和Oracle连接"""
    
    def __init__(self, neo4j_client: Neo4jClient = None, oracle_client: OracleClient = None):
        """
        初始化数据库管理器
        
        Args:
            neo4j_client: Neo4j客户端实例
            oracle_client: Oracle客户端实例
        """
        self.neo4j = neo4j_client or Neo4jClient()
        self.oracle = oracle_client or OracleClient()

    
    def set_oracle_connection(self, dsn: str, username: str, password: str):
        """设置Oracle连接参数"""
        self.oracle = OracleClient(dsn=dsn, username=username, password=password)
    
    def check_connections(self) -> Dict[str, bool]:
        """检查所有数据库连接状态"""
        status = {
            "neo4j": self.neo4j.check_connection() if self.neo4j else False,
            "oracle": self.oracle.check_connection() if self.oracle else False
        }
        
        logger.info(f"数据库连接状态: {status}")
        return status
    
    def get_database_info(self) -> Dict[str, Any]:
        """获取所有数据库信息"""
        info = {
            "neo4j": self.neo4j.get_database_info() if self.neo4j else {"error": "Neo4j客户端不可用"},
            "oracle": {
                "available": self.oracle is not None, 
                "connected": self.oracle.check_connection() if self.oracle else False
            }
        }
        
        if self.oracle and self.oracle.check_connection():
            try:
                # 获取Oracle版本信息
                version_result = self.oracle.execute_query("SELECT * FROM V$VERSION WHERE ROWNUM = 1")
                if version_result:
                    info["oracle"]["version"] = version_result[0].get("BANNER", "Unknown")
            except Exception as e:
                logger.warning(f"获取Oracle版本信息失败: {e}")
                info["oracle"]["version"] = "Unknown"
        
        return info
    
    def close_all(self):
        """关闭所有数据库连接"""
        if self.neo4j:
            self.neo4j.close()
        if self.oracle:
            self.oracle.close()
        logger.info("所有数据库连接已关闭")


# 全局数据库客户端实例
neo4j_client = Neo4jClient()
database_manager = DatabaseManager(neo4j_client=neo4j_client)


