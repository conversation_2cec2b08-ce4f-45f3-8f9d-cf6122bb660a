
**产品需求文档 (PRD): 长江航运智能指标问答Agent (CJHY-KGAgent)**

**版本**: 1.0
**日期**: [当前日期]
**负责人**: [你的名字/团队]

**目录**
1.  引言与目标
    1.1 项目背景
    1.2 产品目标
    1.3 目标用户
    1.4 衡量成功的关键指标 (KPIs)
2.  产品概述
3.  功能需求
    3.1 自然语言理解 (NLU)
    3.2 知识图谱交互 (Neo4j)
    3.3 API工具调用
    3.4 智能记忆 (Mem0 Integration)
    3.5 数据处理与计算
    3.6 对话管理
    3.7 答案生成与呈现
    3.8 后台数据同步
4.  非功能需求
    4.1 性能
    4.2 可靠性与可用性
    4.3 可维护性与可扩展性
    4.4 安全性
    4.5 可观察性
5.  技术架构与框架选型
    5.1 核心Agent框架
    5.2 大型语言模型 (LLM)
    5.3 知识图谱数据库
    5.4 智能记忆库
    5.5 数据访问
    5.6 工具集 (LangChain Tools)
    5.7 后台数据同步进程
    5.8 用户界面 (UI)
6.  用户故事 (长江航运场景)
7.  未来展望/潜在扩展
8.  附录 (待补充)
    A.1 长江航运知识图谱本体设计概要 (Neo4j节点标签与关系类型)
    A.2 航运数据API接口定义示例
    A.3 LangGraph流程图示例

---

**1. 引言与目标 (Introduction & Goals)**

*   **1.1 项目背景**:
    *   长江航运行业拥有复杂且专业的指标体系，涉及港口吞吐量、船舶流量、航道状况、运输效率、安全事故、环境监测等多个维度。数据大部分为月度/季度/年度静态数据，部分关键运行数据（如实时船舶位置、重点航段气象）需通过API获取。
    *   现有数据分散在不同业务系统或报表中，查询和综合分析耗时费力，难以快速、准确地获取指标信息并进行解读。
    *   为提升数据利用效率和决策支持能力，计划将长江航运核心指标体系、航道信息、港口数据、船舶元数据等构建入**Neo4j知识图谱 (KG)**。
*   **1.2 产品目标**:
    *   构建一个长江航运智能指标问答Agent (CJHY-KGAgent)，能够理解用户（如航运管理者、调度员、分析师）通过自然语言提出的关于长江航运各类指标和运行情况的问题。
    *   准确、高效地从Neo4j知识图谱、内部航运数据API、外部实时航运/气象API获取数据，并进行必要的整合与计算。
    *   提供清晰、专业的答案，支持多轮对话和上下文理解，辅助航运管理、调度决策和趋势分析。
*   **1.3 目标用户**:
    *   航运集团/港务局管理者、航道管理局人员、船舶调度员、物流分析师、安全监控员、政策研究员、以及其他需要便捷访问航运数据的内部员工。
*   **1.4 衡量成功的关键指标 (KPIs)**:
    *   问题理解准确率 (NLU Accuracy - 针对航运术语): > [具体数值]%
    *   答案准确率 (Answer Accuracy - 涉及具体航运数据): > [具体数值]%
    *   平均响应时间 (Average Response Time): < [具体数值] 秒
    *   用户满意度 (User Satisfaction Score - 通过调研或反馈获取): > [具体数值]/5
    *   知识图谱 (Neo4j) 查询成功率: > [具体数值]%
    *   API调用成功率: > [具体数值]%
    *   Mem0缓存命中率: > [具体数值]% (针对可缓存查询)
    *   Agent服务正常运行时间: > [具体数值]%

**2. 产品概述 (Product Overview)**

CJHY-KGAgent是一个基于大型语言模型 (LLM) 和LangGraph框架构建的智能对话系统，专为长江航运领域定制。它以Neo4j知识图谱为核心数据和元数据来源，该知识图谱包含了航运指标、港口、航段、船舶等实体及其丰富的关系。Agent通过API工具访问实时运行数据和封装后的历史统计数据。Agent具备自然语言理解、对话管理、工具编排、航运领域知识推理和答案生成能力。引入Mem0智能记忆库以优化响应速度和用户体验。历史统计数据主要通过后台进程同步至Neo4j知识图谱中，Agent优先查询KG中的数据。

**3. 功能需求 (Functional Requirements)**

*   **3.1 自然语言理解 (NLU)**
    *   FR1.1: Agent应能理解用户关于长江航运指标的各类自然语言提问，涵盖港口指标、航道指标、船舶与运输指标、安全与环境指标、定义与关系查询等。（示例见PRD上一版）
    *   FR1.2: Agent应能准确提取问题中的关键实体：指标名称（如“集装箱吞吐量”、“通航水深”）、时间范围（支持相对时间如“上个月”，和绝对时间如“2023年Q2”）、地理范围（港口名、航段名、里程标、行政区域）、船舶类型、货物种类、公司名称等。
    *   FR1.3: Agent应能处理航运领域的专业术语（如TEU, DWT, LOA, 吃水）、缩写（如AIS, VTS）和口语化表达。
    *   FR1.4: Agent应能识别并尝试通过反问或提供选项的方式澄清模糊或不明确的提问。
*   **3.2 知识图谱交互 (Neo4j)**
    *   FR2.1: Agent应能通过Cypher查询Neo4j知识图谱获取航运指标的元数据：定义（存储为节点属性）、计算方法（描述性文本）、数据来源系统、统计周期、计量单位、相关政策文件链接等。
    *   FR2.2: Agent应能查询Neo4j获取港口、航段、船舶类型、航标、闸口等实体的属性（如港口代码、泊位数、航段长度、设计水深、船舶MMSI、IMO编号）和它们之间的关系（如`位于(Port)-[:LOCATED_IN]->(Region)`，`包含(Waterway)-[:CONTAINS_SEGMENT]->(WaterwaySegment)`，`属于(Ship)-[:BELONGS_TO_FLEET_OF]->(ShippingCompany)`）。
    *   FR2.3: Agent应能查询Neo4j获取指标数据访问指令：内部数据API的端点、HTTP方法、必要的Cypher查询片段（用于从API参数映射到KG实体ID）、参数映射关系。
    *   FR2.4: Agent应能查询Neo4j中由后台进程同步的航运统计数据点（例如，存储为带有时间戳和维度属性的`MetricDataPoint`节点，关联到相应的`Metric`和`DimensionValue`节点）。
    *   FR2.5: Agent应能利用Neo4j的图结构进行多跳查询和路径发现，辅助LLM进行复杂推理（例如，查找某航段上游所有可能对其产生影响的排污口，或某船舶从A港到B港可能经过的关键闸口）。
*   **3.3 API工具调用 (API Tool Invocation)**
    *   FR3.1: Agent应能根据Neo4j KG提供的指令，调用内部航运数据服务API获取封装后的历史统计数据（主要作为后台同步的补充或特定精细查询）。
    *   FR3.2: Agent应能调用外部API获取实时数据，例如：
        *   船舶AIS实时位置、航速、航向。
        *   重点航段的实时气象（风速、能见度）、水文信息（水位、流速）。
        *   电子航道图服务的特定信息。
    *   FR3.3: API工具应能处理API请求构建（包括动态参数填充）、必要的认证头（如API Key, Token）、响应解析（JSON, XML等）和网络/API错误处理。
*   **3.4 智能记忆 (Mem0 Integration)**
    *   FR4.1: Agent应集成Mem0智能记忆库。
    *   FR4.2: 每次用户与Agent的完整交互（用户问题、Agent思考步骤摘要、最终答案、相关实体ID）应存入Mem0。
    *   FR4.3: 在处理新问题时，Agent应首先查询Mem0，基于语义相似度和提取的实体检索相关的历史交互。
    *   FR4.4: 如果Mem0中存在高度相关且答案（尤其是定义类、关系类信息，或在后台同步周期内仍有效的数值类信息）仍然有效，Agent可直接使用或基于该记忆调整后回答。
    *   FR4.5: 对于Mem0中检索到的数值型数据，需结合其存储时间戳和当前指标的更新频率（可从KG获取）判断其时效性。
*   **3.5 数据处理与计算 (Data Processing & Calculation)**
    *   FR5.1: Agent应能对从不同来源（Neo4j KG, API, Mem0）获取的数据进行清洗、转换和整合。
    *   FR5.2: Agent应能通过代码解释器工具（如Python REPL）执行必要的计算，例如：总和、平均值、百分比、环比/同比增长、加权平均、单位换算（如吨转换为TEU的估算）。
    *   FR5.3: Agent应能将结构化数据（如多行查询结果）格式化为易读的文本表格或列表进行展示。
*   **3.6 对话管理 (Dialogue Management)**
    *   FR6.1: Agent应能支持多轮对话，保持对话状态，理解用户在后续轮次中对前文内容的指代（例如，“它上个月是多少？”）。
    *   FR6.2: Agent应能记忆对话过程中的关键实体和约束条件，用于后续交互的上下文限定。
*   **3.7 答案生成与呈现 (Response Generation)**
    *   FR7.1: Agent应能将查询和计算结果以清晰、准确、专业的航运术语和流畅的自然语言回复给用户。
    *   FR7.2: 答案应包含必要的信息来源（如“根据XX港务局月报数据”、“来自实时AIS系统”）、数据时间范围、地理区域等元信息。
    *   FR7.3: 对于无法回答或超出当前知识范围/能力的问题，应礼貌地告知用户，并可提供相关概念的解释或引导用户尝试其他问法。
*   **3.8 后台数据同步 (Backend Data Synchronization - *外部依赖*)**
    *   FR8.1: (系统级需求) 应有一个后台进程负责定期（如每日/每周/每月）将航运相关的静态和历史统计数据从原始业务数据库/数据仓库同步到Neo4j知识图谱中。
    *   FR8.2: 同步过程应包括数据清洗、转换，并按照预定义的Neo4j本体模型创建/更新节点和关系（如`Port`节点的`monthly_throughput`属性，或创建关联的`MetricDataPoint`节点）。
    *   FR8.3: 后台进程负责维护Neo4j KG中这部分数据的“最新状态”（相对于其更新周期）。Agent主要消费这些预同步的数据。

**4. 非功能需求 (Non-Functional Requirements)**

*   **4.1 性能 (Performance)**
    *   NFR1.1: 对于90%的常见查询（特别是Mem0命中或直接KG查询），端到端响应时间应在 [3-5] 秒内。
    *   NFR1.2: 对于需要调用外部实时API或复杂计算的查询，响应时间应在 [5-10] 秒内。
    *   NFR1.3: Neo4j数据库应针对典型查询进行索引优化，确保Cypher查询高效执行。
    *   NFR1.4: LangGraph流程中的并行化设计（如同时查询KG和API）应有效减少总等待时间。
*   **4.2 可靠性与可用性 (Reliability & Availability)**
    *   NFR2.1: Agent服务应达到 99.9% 的可用性。
    *   NFR2.2: Agent应能通过LangGraph的流程控制妥善处理工具调用失败（Neo4j查询异常、API超时/错误）、LLM响应格式错误等情况，并给出用户友好的错误提示或尝试自动重试（例如，对瞬时网络问题）。
*   **4.3 可维护性与可扩展性 (Maintainability & Scalability)**
    *   NFR3.1: Agent的架构（基于LangGraph）应模块化，各组件（NLU、KG交互、API调用、Mem0、响应生成）职责清晰，易于独立更新和维护。
    *   NFR3.2: 新的航运指标、数据源、API或业务逻辑应能方便地集成到Agent中，主要通过更新KG本体、API工具配置和LangGraph流程。
    *   NFR3.3: Neo4j知识图谱的本体设计应遵循最佳实践，易于扩展新的节点标签、属性和关系类型。
    *   NFR3.4: 系统应能通过增加Agent实例或扩展Neo4j/向量数据库集群来水平扩展，以应对用户量和数据量的增长。
*   **4.4 安全性 (Security)**
    *   NFR4.1: 对内部和外部API的调用应使用安全的认证机制（如OAuth 2.0, API Keys managed via a secure vault）。
    *   NFR4.2: 用户身份认证与授权机制应确保只有授权用户才能访问敏感数据或执行特定操作。
    *   NFR4.3: Neo4j数据库的访问应受权限控制。
    *   NFR4.4: 代码解释器工具（如果使用）必须在隔离的沙箱环境中运行，严格限制其文件系统访问和网络权限。
*   **4.5 可观察性 (Observability)**
    *   NFR5.1: 应建立全面的日志和监控系统，记录Agent的请求处理流程、LLM交互、工具调用（Neo4j查询语句、API请求/响应）、Mem0操作、错误和延迟。
    *   NFR5.2: 推荐使用LangSmith或类似可观察性平台，以图形化方式追踪和调试Agent的完整思考链、状态转换和决策过程。
    *   NFR5.3: 关键业务指标（如KPIs中列出的）应能通过仪表盘实时监控。

**5. 技术架构与框架选型 (Technical Architecture & Frameworks)**

*   **5.1 核心Agent框架**: **LangGraph** (基于LangChain生态)
    *   利用其图结构定义Agent的控制流（节点和边）、状态管理、条件逻辑和错误处理。
*   **5.2 大型语言模型 (LLM)**: [具体选型，如GPT-4o, Claude 3.5 Sonnet, 或国内优秀模型]
    *   用于NLU、意图识别、Cypher查询理解与辅助生成（可选）、工具选择与编排、答案生成。
*   **5.3 知识图谱数据库**: **Neo4j** (版本 [具体版本])
    *   存储长江航运指标元数据、实体（港口、航段、船舶等）、关系、API访问指令、后台同步的静态数据。
*   **5.4 智能记忆库**: **Mem0**
    *   用于存储和检索对话历史。需配置向量嵌入模型（如 `text-embedding-3-small` 或 Sentence Transformers）和向量数据库后端（如Qdrant, Milvus, Pinecone, 或Neo4j Vector Index - 若适用且性能满足）。
*   **5.5 数据访问**:
    *   内部数据服务API (InternalDataAPI): 基于RESTful或GraphQL，封装对原始航运业务数据库的访问。
    *   外部实时数据API (ExternalRealtimeAPI): 例如，商业AIS数据服务API、国家气象/水文API。
*   **5.6 工具集 (LangChain Tools)**:
    *   自定义`Neo4jKGTool`: 封装与Neo4j知识图谱的交互逻辑，执行Cypher查询，解析结果。可利用LangChain的`GraphCypherQAChain`作为基础或参考。
    *   自定义`ShippingAPITool`: 封装对内部和外部航运相关API的调用逻辑。
    *   (可选) `PythonCodeInterpreterTool`: 执行Python代码进行复杂数据处理和计算，运行在安全沙箱内。
    *   (可能) `Mem0Tool`或直接集成: 封装与Mem0的交互逻辑。
*   **5.7 后台数据同步进程**: [技术选型，如Python (Pandas, Neo4j Driver) + Airflow / Apache NiFi / 自定义调度脚本]
    *   负责定期从源系统抽取数据，转换为图结构，并加载/更新到Neo4j中。
*   **5.8 用户界面 (UI)**: [技术选型，如Streamlit / Gradio (快速原型), 或基于React/Vue的自定义Web应用 (生产环境)]

**6. 用户故事 (User Stories - 长江航运场景)** (同PRD上一版中的US1-US8)

*   US1 (港口管理): 作为一名港口运营经理，我想要问“上个月九江港的煤炭和铁矿石进港量分别是多少？”，以便了解主要货种的到港情况，辅助泊位调度。
*   US2 (航道维护): 作为一名航道维护工程师，我想要问“长江中游A航段至B航段近7天的最低通航水深记录？”，以便评估大型船舶的通航风险。
*   US3 (船舶调度): 作为一名船舶调度员，我想要问“目前在C区域（如某锚地）附近有哪些正在等待过闸的万吨级以上船舶？”，以便进行过闸计划的动态调整。
*   US4 (物流分析): 作为一名物流分析师，我想要问“查询近三个月从重庆到上海的集装箱平均运输时间和成本？”，以便进行物流方案的经济性评估。
*   US5 (安全监控): 作为一名安全监控中心值班员，我想要问“过去24小时内，长江D管制区是否有船舶偏航或超速报警事件？”，以便及时发现潜在安全隐患。
*   US6 (政策研究): 作为一名行业分析师，我想要问“近五年来长江干线港口总吞吐量的年度增长趋势如何？”，以便分析行业发展态势。
*   US7 (应急响应): 作为一名应急指挥人员，在发生某航段污染事件后，我想要问“该航段下游有哪些敏感取水口或生态保护区？”，以便快速制定应急预案。
*   US8 (信息查询): 作为一名新员工，我想要问“解释一下什么是‘船舶实际载重系数’？”，以便学习航运专业知识。

**7. 未来展望/潜在扩展 (Future Considerations)** (同PRD上一版)

*   支持基于地理位置的查询（例如，“显示我当前位置附近航道的水深信息”）。
*   集成船舶轨迹可视化。
*   结合预测模型，提供港口拥堵预测、航行风险预警。
*   支持更复杂的航运网络优化分析。
*   对接船舶能效管理系统，提供能耗分析与优化建议。
*   支持通过语音输入进行提问。
*   提供个性化的指标仪表盘推送。

**8. 附录 (Appendix)** (这些部分需要在项目具体设计阶段进行填充)

*   **A.1 长江航运知识图谱本体设计概要 (Neo4j节点标签与关系类型)**
    *   示例节点标签: `Port`, `WaterwaySegment`, `Ship`, `Cargo`, `Metric`, `MetricDataPoint`, `Regulation`, `Accident`, `Company`, `WeatherStation`, `WaterLevelStation`, `NavigationMark`, `Berth`, `Lock`等。
    *   示例关系类型: `HAS_METRIC`, `LOCATED_IN`, `CONNECTS_TO`, `CARRIES_CARGO`, `OPERATED_BY`, `SUBJECT_TO_REGULATION`, `OCCURRED_AT`, `MEASURES_PROPERTY_OF`等。
*   **A.2 航运数据API接口定义示例**
    *   内部数据服务API (InternalDataAPI) 示例
    *   外部实时API (如AIS, 气象) 接入说明和示例
*   **A.3 LangGraph流程图示例**
    *   针对一个典型的用户查询（例如US1），绘制其在LangGraph中的处理流程图，标明主要节点（NLU, Mem0 Query, KG Query, API Call, Aggregation, Response Gen）和条件边。

