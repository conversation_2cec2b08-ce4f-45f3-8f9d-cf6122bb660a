
import sqlite3
import json
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
from loguru import logger

try:
    import faiss
except ImportError:
    logger.error("faiss-cpu not installed. Run: pip install faiss-cpu")
    raise

from .embedder import get_embedder


class SqliteFaissMemoryStore:
    """SQLite + FAISS 混合记忆存储"""
    
    def __init__(self, db_path: str = "memory.db", index_path: str = "memory.faiss"):
        """
        初始化存储
        
        Args:
            db_path: SQLite数据库路径
            index_path: FAISS索引文件路径
        """
        self.db_path = Path(db_path)
        self.index_path = Path(index_path)
        self.embedder = get_embedder()
        self.dimension = self.embedder.get_embedding_dim()
        
        # 初始化数据库和索引
        self._init_database()
        self._init_faiss_index()
        
        logger.info(f"Memory store initialized: DB={self.db_path}, Index={self.index_path}")
    
    def _init_database(self):
        """初始化SQLite数据库"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建对话表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS conversations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    question TEXT NOT NULL,
                    answer TEXT NOT NULL,
                    intent TEXT,
                    entities TEXT,  -- JSON格式存储
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    vector_id INTEGER UNIQUE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_intent ON conversations(intent)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON conversations(timestamp)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_vector_id ON conversations(vector_id)")
            
            conn.commit()
            logger.info("Database initialized")
    
    def _init_faiss_index(self):
        """初始化FAISS索引"""
        try:
            if self.index_path.exists():
                # 加载现有索引
                self.index = faiss.read_index(str(self.index_path))
                logger.info(f"Loaded existing FAISS index with {self.index.ntotal} vectors")
            else:
                # 创建新索引 - 使用内积索引，适合归一化向量
                self.index = faiss.IndexFlatIP(self.dimension)
                logger.info(f"Created new FAISS index with dimension {self.dimension}")
                
        except Exception as e:
            logger.error(f"Failed to initialize FAISS index: {e}")
            # 创建新索引作为备用
            self.index = faiss.IndexFlatIP(self.dimension)
    
    def _save_index(self):
        """保存FAISS索引到磁盘"""
        try:
            faiss.write_index(self.index, str(self.index_path))
            logger.debug("FAISS index saved")
        except Exception as e:
            logger.error(f"Failed to save FAISS index: {e}")
    
    def store_conversation(self, question: str, answer: str, intent: str = None, 
                          entities: Dict[str, Any] = None, metadata: Dict[str, Any] = None) -> int:
        """
        存储对话记录
        
        Args:
            question: 用户问题
            answer: 系统回答
            intent: 意图
            entities: 实体信息
            metadata: 额外元数据
            
        Returns:
            记录ID
        """
        try:
            # 生成问题的嵌入向量
            question_embedding = self.embedder.embed_single(question, normalize=True)
            
            # 添加向量到FAISS索引
            vector_id = self.index.ntotal
            self.index.add(question_embedding.reshape(1, -1))
            
            # 存储到数据库
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT INTO conversations (question, answer, intent, entities, vector_id)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    question,
                    answer,
                    intent,
                    json.dumps(entities or {}, ensure_ascii=False),
                    vector_id
                ))
                record_id = cursor.lastrowid
                conn.commit()
            
            # 保存索引
            self._save_index()
            
            logger.info(f"Stored conversation: ID={record_id}, VectorID={vector_id}")
            return record_id
            
        except Exception as e:
            logger.error(f"Failed to store conversation: {e}")
            raise
    
    def query_similar_conversations(self, question: str, entities: Dict[str, Any] = None,
                                   intent: str = None, limit: int = 5, 
                                   similarity_threshold: float = 0.7) -> List[Dict[str, Any]]:
        """
        查询相似对话
        
        Args:
            question: 查询问题
            entities: 实体过滤条件
            intent: 意图过滤条件
            limit: 返回数量限制
            similarity_threshold: 相似度阈值
            
        Returns:
            相似对话列表
        """
        try:
            results = []
            
            # 如果索引为空，直接返回
            if self.index.ntotal == 0:
                return results
            
            # 生成查询向量
            query_embedding = self.embedder.embed_single(question, normalize=True)
            
            # FAISS向量搜索
            search_k = min(limit * 3, self.index.ntotal)  # 搜索更多候选，后续过滤
            similarities, vector_ids = self.index.search(
                query_embedding.reshape(1, -1), search_k
            )
            
            # 构建SQL查询条件
            conditions = ["vector_id IN ({})".format(",".join(map(str, vector_ids[0])))]
            params = []
            
            if intent:
                conditions.append("intent = ?")
                params.append(intent)
            
            # 实体过滤（简单的JSON字符串匹配）
            if entities:
                for key, value in entities.items():
                    if isinstance(value, list) and value:
                        # 对于列表，检查是否包含任何一个值
                        entity_conditions = []
                        for v in value:
                            entity_conditions.append("entities LIKE ?")
                            params.append(f'%"{key}"%"{v}"%')
                        conditions.append(f"({' OR '.join(entity_conditions)})")
                    elif value:
                        conditions.append("entities LIKE ?")
                        params.append(f'%"{key}"%"{value}"%')
            
            # 查询数据库
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                sql = f"""
                    SELECT id, question, answer, intent, entities, timestamp, vector_id
                    FROM conversations
                    WHERE {' AND '.join(conditions)}
                    ORDER BY timestamp DESC
                """
                cursor.execute(sql, params)
                rows = cursor.fetchall()
            
            # 处理结果
            for i, row in enumerate(rows):
                if i >= limit:
                    break
                
                # 找到对应的相似度
                vector_id = row[6]
                similarity = 0.0
                for j, vid in enumerate(vector_ids[0]):
                    if vid == vector_id:
                        similarity = float(similarities[0][j])
                        break
                
                # 过滤低相似度结果
                if similarity < similarity_threshold:
                    continue
                
                result = {
                    "id": row[0],
                    "question": row[1],
                    "answer": row[2],
                    "intent": row[3],
                    "entities": json.loads(row[4]) if row[4] else {},
                    "timestamp": row[5],
                    "similarity": similarity
                }
                results.append(result)
            
            # 按相似度排序
            results.sort(key=lambda x: x["similarity"], reverse=True)
            
            logger.info(f"Found {len(results)} similar conversations")
            return results
            
        except Exception as e:
            logger.error(f"Failed to query similar conversations: {e}")
            return []
    
    def search_by_entities(self, entities: Dict[str, Any], limit: int = 10) -> List[Dict[str, Any]]:
        """
        根据实体搜索记忆
        
        Args:
            entities: 实体过滤条件
            limit: 返回数量限制
            
        Returns:
            匹配的对话列表
        """
        try:
            conditions = []
            params = []
            
            for key, value in entities.items():
                if isinstance(value, list) and value:
                    # 对于列表，检查是否包含任何一个值
                    entity_conditions = []
                    for v in value:
                        entity_conditions.append("entities LIKE ?")
                        params.append(f'%"{key}"%"{v}"%')
                    conditions.append(f"({' OR '.join(entity_conditions)})")
                elif value:
                    conditions.append("entities LIKE ?")
                    params.append(f'%"{key}"%"{value}"%')
            
            if not conditions:
                return []
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                sql = f"""
                    SELECT id, question, answer, intent, entities, timestamp
                    FROM conversations
                    WHERE {' AND '.join(conditions)}
                    ORDER BY timestamp DESC
                    LIMIT ?
                """
                params.append(limit)
                cursor.execute(sql, params)
                rows = cursor.fetchall()
            
            results = []
            for row in rows:
                result = {
                    "id": row[0],
                    "question": row[1],
                    "answer": row[2],
                    "intent": row[3],
                    "entities": json.loads(row[4]) if row[4] else {},
                    "timestamp": row[5]
                }
                results.append(result)
            
            logger.info(f"Found {len(results)} conversations by entities")
            return results
            
        except Exception as e:
            logger.error(f"Failed to search by entities: {e}")
            return []
    
    def get_conversation_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取最近的对话历史
        
        Args:
            limit: 返回数量限制
            
        Returns:
            对话历史列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT id, question, answer, intent, entities, timestamp
                    FROM conversations
                    ORDER BY timestamp DESC
                    LIMIT ?
                """, (limit,))
                rows = cursor.fetchall()
            
            results = []
            for row in rows:
                result = {
                    "id": row[0],
                    "question": row[1],
                    "answer": row[2],
                    "intent": row[3],
                    "entities": json.loads(row[4]) if row[4] else {},
                    "timestamp": row[5]
                }
                results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Failed to get conversation history: {e}")
            return []
    
    def delete_conversation(self, conversation_id: int) -> bool:
        """
        删除对话记录
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            是否删除成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取vector_id
                cursor.execute("SELECT vector_id FROM conversations WHERE id = ?", (conversation_id,))
                row = cursor.fetchone()
                if not row:
                    return False
                
                vector_id = row[0]
                
                # 删除数据库记录
                cursor.execute("DELETE FROM conversations WHERE id = ?", (conversation_id,))
                conn.commit()
                
                # 注意：FAISS不支持直接删除单个向量，需要重建索引
                # 这里简化处理，只删除数据库记录
                logger.warning(f"Deleted conversation {conversation_id}, but FAISS vector {vector_id} remains")
                
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete conversation: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取存储统计信息
        
        Returns:
            统计信息字典
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 总对话数
                cursor.execute("SELECT COUNT(*) FROM conversations")
                total_conversations = cursor.fetchone()[0]
                
                # 按意图统计
                cursor.execute("""
                    SELECT intent, COUNT(*) 
                    FROM conversations 
                    WHERE intent IS NOT NULL 
                    GROUP BY intent
                """)
                intent_stats = dict(cursor.fetchall())
                
                # 最新对话时间
                cursor.execute("SELECT MAX(timestamp) FROM conversations")
                latest_timestamp = cursor.fetchone()[0]
            
            return {
                "total_conversations": total_conversations,
                "total_vectors": self.index.ntotal,
                "intent_distribution": intent_stats,
                "latest_conversation": latest_timestamp,
                "database_size": self.db_path.stat().st_size if self.db_path.exists() else 0,
                "index_size": self.index_path.stat().st_size if self.index_path.exists() else 0,
                "embedding_dimension": self.dimension
            }
            
        except Exception as e:
            logger.error(f"Failed to get stats: {e}")
            return {}
    
    def rebuild_index(self):
        """重建FAISS索引"""
        try:
            logger.info("Rebuilding FAISS index...")
            
            # 创建新索引
            new_index = faiss.IndexFlatIP(self.dimension)
            
            # 从数据库获取所有对话
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT id, question FROM conversations ORDER BY id")
                rows = cursor.fetchall()
            
            # 重新生成嵌入并添加到索引
            for i, (record_id, question) in enumerate(rows):
                embedding = self.embedder.embed_single(question, normalize=True)
                new_index.add(embedding.reshape(1, -1))
                
                # 更新vector_id
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute("UPDATE conversations SET vector_id = ? WHERE id = ?", (i, record_id))
                    conn.commit()
            
            # 替换索引
            self.index = new_index
            self._save_index()
            
            logger.info(f"Index rebuilt with {self.index.ntotal} vectors")
            
        except Exception as e:
            logger.error(f"Failed to rebuild index: {e}")
            raise


if __name__ == "__main__":
    # 测试存储组件
    store = SqliteFaissMemoryStore("test_memory.db", "test_memory.faiss")
    
    # 测试存储
    record_id = store.store_conversation(
        question="九江港的吞吐量是多少？",
        answer="九江港2023年的货物吞吐量为1.2亿吨。",
        intent="1.PointQuery",
        entities={"ports": ["九江港"], "metrics": ["吞吐量"], "times": ["2023年"]}
    )
    print(f"Stored conversation with ID: {record_id}")
    
    # 测试查询
    results = store.query_similar_conversations("九江港口的货物吞吐量情况")
    print(f"Found {len(results)} similar conversations")
    for result in results:
        print(f"- {result['question']} (similarity: {result['similarity']:.3f})")
    
    # 测试统计
    stats = store.get_stats()
    print(f"Stats: {stats}") 