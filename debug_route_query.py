#!/usr/bin/env python3
"""
诊断航线查询问题
检查数据库中的航线数据和船舶航线统计数据
"""

import sys
import os

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from kgnode.database import DatabaseManager

def debug_route_data():
    """诊断航线数据问题"""
    print("=== 航线查询问题诊断 ===")
    
    db_manager = DatabaseManager()
    
    try:
        with db_manager.neo4j.get_session() as session:
            # 1. 检查航线节点数量
            print("1. 检查航线节点...")
            result = session.run("MATCH (sr:ShippingRoute) RETURN count(sr) as count")
            route_count = result.single()['count']
            print(f"   航线节点总数: {route_count}")
            
            # 2. 检查包含"九江"和"武汉"的航线
            print("\n2. 检查九江-武汉相关航线...")
            result = session.run("""
                MATCH (sr:ShippingRoute)
                WHERE sr.routeName CONTAINS '九江' OR sr.routeName CONTAINS '武汉'
                   OR sr.originPortName IN ['九江', '武汉'] 
                   OR sr.destinationPortName IN ['九江', '武汉']
                RETURN sr.routeName, sr.originPortName, sr.destinationPortName, sr.routeId
                ORDER BY sr.routeName
                LIMIT 20
            """)
            
            routes = []
            for record in result:
                routes.append({
                    'routeName': record['sr.routeName'],
                    'origin': record['sr.originPortName'],
                    'destination': record['sr.destinationPortName'],
                    'routeId': record['sr.routeId']
                })
            
            if routes:
                print(f"   找到 {len(routes)} 条相关航线:")
                for route in routes:
                    print(f"     - {route['routeName']}: {route['origin']} -> {route['destination']} ({route['routeId']})")
            else:
                print("   ❌ 未找到包含九江或武汉的航线")
            
            # 3. 检查船舶航线统计数据
            print("\n3. 检查船舶航线统计数据...")
            result = session.run("""
                MATCH (smls:ShipMonthLineStat)-[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
                WHERE sr.routeName CONTAINS '九江' OR sr.routeName CONTAINS '武汉'
                RETURN count(smls) as stat_count, sr.routeName
                ORDER BY stat_count DESC
                LIMIT 10
            """)
            
            stats = []
            for record in result:
                stats.append({
                    'routeName': record['sr.routeName'],
                    'stat_count': record['stat_count']
                })
            
            if stats:
                print(f"   找到 {len(stats)} 条航线有统计数据:")
                for stat in stats:
                    print(f"     - {stat['routeName']}: {stat['stat_count']} 条统计记录")
            else:
                print("   ❌ 未找到九江-武汉航线的统计数据")
            
            # 4. 检查特定时间期间的数据
            print("\n4. 检查202506期间的数据...")
            result = session.run("""
                MATCH (smls:ShipMonthLineStat)-[:LINE_STAT_FOR_MONTH]->(ym:YearMonth {ym: '202506'})
                MATCH (smls)-[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
                WHERE sr.routeName CONTAINS '九江' OR sr.routeName CONTAINS '武汉'
                RETURN sr.routeName, count(smls) as ship_count
                ORDER BY ship_count DESC
                LIMIT 10
            """)
            
            period_stats = []
            for record in result:
                period_stats.append({
                    'routeName': record['sr.routeName'],
                    'ship_count': record['ship_count']
                })
            
            if period_stats:
                print(f"   202506期间找到 {len(period_stats)} 条航线有船舶数据:")
                for stat in period_stats:
                    print(f"     - {stat['routeName']}: {stat['ship_count']} 艘船")
            else:
                print("   ❌ 202506期间未找到九江-武汉航线的船舶数据")
            
            # 5. 检查查询中使用的具体航线名称
            print("\n5. 检查查询使用的航线名称...")
            search_names = ['九江-武汉', '九江-武汉航线', '武汉-九江', '武汉-九江航线']
            
            for name in search_names:
                result = session.run("""
                    MATCH (sr:ShippingRoute)
                    WHERE sr.routeName = $name
                    RETURN sr.routeName, sr.originPortName, sr.destinationPortName
                """, name=name)
                
                found = result.single()
                if found:
                    print(f"   ✅ 找到航线: {name}")
                    print(f"      起点: {found['sr.originPortName']}, 终点: {found['sr.destinationPortName']}")
                else:
                    print(f"   ❌ 未找到航线: {name}")
            
            # 6. 检查船舶月度统计和航线统计的关联
            print("\n6. 检查船舶统计数据关联...")
            result = session.run("""
                MATCH (s:Ship)<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: '202506'})
                MATCH (s)<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)-[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
                MATCH (smls)-[:LINE_STAT_FOR_MONTH]->(ym2:YearMonth {ym: '202506'})
                WHERE sr.routeName CONTAINS '九江' OR sr.routeName CONTAINS '武汉'
                RETURN count(DISTINCT s) as ship_count, count(DISTINCT sr) as route_count
            """)
            
            link_result = result.single()
            if link_result:
                print(f"   关联的船舶数: {link_result['ship_count']}")
                print(f"   关联的航线数: {link_result['route_count']}")
            
            # 7. 检查原始查询的问题
            print("\n7. 测试原始查询逻辑...")
            result = session.run("""
                MATCH (s:Ship)<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
                WHERE EXISTS {
                    MATCH (s)<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)
                    -[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
                    -[:LINE_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
                    WHERE sr.routeName IN $active_routes
                }
                RETURN count(s) as ship_count
            """, period='202506', active_routes=['九江-武汉'])
            
            query_result = result.single()
            print(f"   原始查询结果: {query_result['ship_count']} 艘船")
            
            # 8. 尝试修正的查询
            print("\n8. 尝试修正的查询...")
            result = session.run("""
                MATCH (s:Ship)<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
                WHERE EXISTS {
                    MATCH (s)<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)
                    -[:LINE_STAT_FOR_MONTH]->(ym_line:YearMonth {ym: $period})
                    MATCH (smls)-[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
                    WHERE sr.routeName IN $active_routes
                }
                RETURN count(s) as ship_count
            """, period='202506', active_routes=['九江-武汉航线', '武汉-九江航线'])
            
            corrected_result = result.single()
            print(f"   修正查询结果: {corrected_result['ship_count']} 艘船")
            
    except Exception as e:
        print(f"❌ 诊断过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        db_manager.close_all()
    
    return True

def suggest_fixes():
    """建议修复方案"""
    print("\n=== 修复建议 ===")
    print("基于诊断结果，可能的问题和解决方案:")
    print()
    print("1. **航线名称不匹配**")
    print("   - 问题: 查询使用'九江-武汉'，但数据库中可能是'九江-武汉航线'")
    print("   - 解决: 调整查询条件，使用正确的航线名称")
    print()
    print("2. **关系链接问题**")
    print("   - 问题: ShipMonthLineStat与YearMonth的关系可能有问题")
    print("   - 解决: 检查并修正关系链接逻辑")
    print()
    print("3. **数据缺失**")
    print("   - 问题: 可能缺少202506期间的航线统计数据")
    print("   - 解决: 重新运行航线ETL，确保数据完整")
    print()
    print("4. **查询逻辑优化**")
    print("   - 建议: 简化查询逻辑，分步验证每个环节")

def main():
    """主函数"""
    print("开始诊断航线查询问题\n")
    
    success = debug_route_data()
    
    if success:
        suggest_fixes()
        print("\n🔍 诊断完成！请根据上述结果调整查询逻辑。")
    else:
        print("\n❌ 诊断失败，请检查数据库连接。")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
