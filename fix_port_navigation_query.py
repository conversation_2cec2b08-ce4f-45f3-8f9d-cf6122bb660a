#!/usr/bin/env python3
"""
修复港口航行查询 - 根据数据库设计文档使用正确的关系
"""

import sys
import os

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_correct_port_navigation_query():
    """测试正确的港口航行查询"""
    from kgnode.database import DatabaseManager
    
    print("=== 测试正确的港口航行查询 ===")
    
    db = DatabaseManager()
    
    try:
        with db.neo4j.get_session() as session:
            # 1. 检查九江港相关的航线
            print("1. 检查九江港相关的航线...")
            result = session.run("""
                MATCH (p:Port {name: '九江'})
                OPTIONAL MATCH (p)<-[:ROUTE_ORIGIN]-(sr1:ShippingRoute)-[:ROUTE_DESTINATION]->(dest1:Port)
                OPTIONAL MATCH (p)<-[:ROUTE_DESTINATION]-(sr2:ShippingRoute)-[:ROUTE_ORIGIN]->(orig2:Port)
                WITH p, 
                     collect(DISTINCT {route: sr1.routeName, destination: dest1.name, direction: 'outbound'}) as outbound_routes,
                     collect(DISTINCT {route: sr2.routeName, origin: orig2.name, direction: 'inbound'}) as inbound_routes
                RETURN outbound_routes, inbound_routes
            """)
            
            record = result.single()
            if record:
                outbound = record['outbound_routes']
                inbound = record['inbound_routes']
                
                print(f"   九江港出发的航线: {len([r for r in outbound if r['route']])}")
                for route in outbound[:3]:  # 显示前3个
                    if route['route']:
                        print(f"     {route['route']} -> {route['destination']}")
                
                print(f"   到达九江港的航线: {len([r for r in inbound if r['route']])}")
                for route in inbound[:3]:  # 显示前3个
                    if route['route']:
                        print(f"     {route['origin']} -> {route['route']}")
            
            # 2. 使用正确的查询方式
            print("\n2. 使用正确的港口航行查询...")
            result = session.run("""
                MATCH (p:Port {name: $portName})
                MATCH (p)<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
                
                // 查找九江港作为起点的航线
                MATCH (p)<-[:ROUTE_ORIGIN]-(sr:ShippingRoute)-[:ROUTE_DESTINATION]->(otherPort:Port)
                OPTIONAL MATCH (otherPort)<-[:STAT_FOR_PORT]-(otherPms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym)
                
                WITH p, otherPort, sr, pms, otherPms,
                     COALESCE(pms.outShipCount, 0) as out_ships,
                     COALESCE(pms.outCargo_ton, 0) as out_cargo,
                     COALESCE(otherPms.inShipCount, 0) as in_ships,
                     COALESCE(otherPms.inCargo_ton, 0) as in_cargo
                
                WITH otherPort, sr,
                     out_ships + in_ships as ship_count,
                     out_cargo + in_cargo as cargo_volume
                WHERE ship_count > 0 OR cargo_volume > 0
                
                RETURN otherPort.name as destination,
                       sr.routeName as route_name,
                       sr.routeId as route_id,
                       ship_count,
                       cargo_volume
                ORDER BY cargo_volume DESC
                LIMIT 5
                
                UNION
                
                // 查找九江港作为终点的航线
                MATCH (p:Port {name: $portName})
                MATCH (p)<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
                
                MATCH (p)<-[:ROUTE_DESTINATION]-(sr:ShippingRoute)-[:ROUTE_ORIGIN]->(otherPort:Port)
                OPTIONAL MATCH (otherPort)<-[:STAT_FOR_PORT]-(otherPms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym)
                
                WITH p, otherPort, sr, pms, otherPms,
                     COALESCE(pms.inShipCount, 0) as in_ships,
                     COALESCE(pms.inCargo_ton, 0) as in_cargo,
                     COALESCE(otherPms.outShipCount, 0) as out_ships,
                     COALESCE(otherPms.outCargo_ton, 0) as out_cargo
                
                WITH otherPort, sr,
                     in_ships + out_ships as ship_count,
                     in_cargo + out_cargo as cargo_volume
                WHERE ship_count > 0 OR cargo_volume > 0
                
                RETURN otherPort.name as destination,
                       sr.routeName as route_name,
                       sr.routeId as route_id,
                       ship_count,
                       cargo_volume
                ORDER BY cargo_volume DESC
                LIMIT 5
            """, portName='九江', period='202507')
            
            print("   正确的港口航行查询结果:")
            count = 0
            for record in result:
                count += 1
                dest = record['destination']
                route = record['route_name']
                route_id = record['route_id']
                ships = record['ship_count']
                cargo = record['cargo_volume']
                print(f"     {count}. 目的地: {dest}")
                print(f"        航线: {route} ({route_id})")
                print(f"        船舶数: {ships}, 货物量: {cargo:.1f}吨")
            
            if count == 0:
                print("     ❌ 正确的查询仍然没有返回结果")
                
                # 进一步调试：检查是否有航线数据
                result = session.run("""
                    MATCH (sr:ShippingRoute)
                    OPTIONAL MATCH (sr)-[:ROUTE_ORIGIN]->(pO:Port)
                    OPTIONAL MATCH (sr)-[:ROUTE_DESTINATION]->(pD:Port)
                    WHERE pO.name = '九江' OR pD.name = '九江'
                    RETURN count(sr) as route_count
                """)
                route_count = result.single()['route_count']
                print(f"     调试: 九江港相关的航线数量: {route_count}")
                
                if route_count == 0:
                    print("     ❌ 数据库中没有九江港相关的航线数据")
                    print("     💡 建议: 需要先运行航线ETL来创建航线数据")
                    return False
                else:
                    print("     ❌ 有航线数据但查询仍无结果，可能是统计数据问题")
                    return False
            else:
                print(f"     ✅ 正确的查询返回 {count} 条结果")
                return True
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        db.close_all()

def create_simplified_correct_query():
    """创建简化的正确查询示例"""
    print("\n=== 简化的正确查询示例 ===")
    
    correct_query = """
    // 根据数据库设计文档的正确港口航行查询
    MATCH (p:Port {name: $portName})
    MATCH (p)<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
    
    // 方法1: 通过航线查找连接的港口
    MATCH (p)<-[:ROUTE_ORIGIN]-(sr:ShippingRoute)-[:ROUTE_DESTINATION]->(otherPort:Port)
    OPTIONAL MATCH (otherPort)<-[:STAT_FOR_PORT]-(otherPms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym)
    
    WITH p, otherPort, sr,
         COALESCE(pms.outShipCount, 0) + COALESCE(otherPms.inShipCount, 0) as ship_count,
         COALESCE(pms.outCargo_ton, 0) + COALESCE(otherPms.inCargo_ton, 0) as cargo_volume
    WHERE ship_count > 0 OR cargo_volume > 0
    
    RETURN otherPort.name as destination,
           sr.routeName as route,
           ship_count,
           cargo_volume
    ORDER BY cargo_volume DESC
    LIMIT 10
    """
    
    print("正确的查询语句:")
    print(correct_query)
    
    print("\n关键差异:")
    print("❌ 错误的关系: (p)-[:PORT_TO_PORT]-(otherPort:Port)")
    print("✅ 正确的关系: (p)<-[:ROUTE_ORIGIN]-(sr:ShippingRoute)-[:ROUTE_DESTINATION]->(otherPort:Port)")
    print("✅ 或者反向: (p)<-[:ROUTE_DESTINATION]-(sr:ShippingRoute)-[:ROUTE_ORIGIN]->(otherPort:Port)")

def main():
    """运行所有测试"""
    print("开始修复港口航行查询\n")
    
    # 创建正确查询示例
    create_simplified_correct_query()
    
    # 测试正确的查询
    success = test_correct_port_navigation_query()
    
    print(f"\n=== 修复结果 ===")
    if success:
        print("🎉 港口航行查询修复成功！")
        print("\n✅ 修复总结:")
        print("1. 发现了查询中使用的PORT_TO_PORT关系不存在于设计文档中")
        print("2. 根据设计文档使用正确的航线关系进行查询")
        print("3. 港口间连接应该通过ShippingRoute节点和ROUTE_ORIGIN/ROUTE_DESTINATION关系")
        print("4. 查询现在能正确返回港口航行数据")
        return True
    else:
        print("❌ 港口航行查询仍有问题")
        print("\n可能的原因:")
        print("1. 数据库中缺少航线数据 - 需要运行航线ETL")
        print("2. 航线与港口的关系没有正确建立")
        print("3. PortMonthStat数据仍有问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
