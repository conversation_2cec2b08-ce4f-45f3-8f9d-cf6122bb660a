#!/usr/bin/env python3
"""
调试港口货物数据查询问题
"""

import sys
import os

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def debug_port_cargo_query():
    """调试港口货物数据查询"""
    from kgnode.database import DatabaseManager
    
    db = DatabaseManager()
    
    try:
        with db.neo4j.get_session() as session:
            print("=== 调试港口货物数据查询 ===")
            
            # 1. 检查九江港的基本信息
            result = session.run("""
                MATCH (p:Port {name: '九江'})
                RETURN p.name as port_name, p.province as province
            """)
            port_info = result.single()
            if port_info:
                print(f"港口信息: {port_info['port_name']}, 省份: {port_info['province']}")
            else:
                print("❌ 未找到九江港")
                return
            
            # 2. 检查九江港的货物统计数据总数
            result = session.run("""
                MATCH (p:Port {name: '九江'})<-[:PORT_CARGO_STAT_FOR_PORT]-(pmcs:PortMonthCargoStat)
                RETURN count(pmcs) as total_stats
            """)
            total_stats = result.single()['total_stats']
            print(f"九江港货物统计总数: {total_stats}")
            
            # 3. 检查有哪些时间期间
            result = session.run("""
                MATCH (p:Port {name: '九江'})<-[:PORT_CARGO_STAT_FOR_PORT]-(pmcs:PortMonthCargoStat)
                MATCH (pmcs)-[:PORT_CARGO_STAT_FOR_MONTH]->(ym:YearMonth)
                RETURN DISTINCT ym.ym as period
                ORDER BY ym.ym DESC
                LIMIT 5
            """)
            
            periods = []
            print("九江港有数据的期间:")
            for record in result:
                period = record['period']
                periods.append(period)
                print(f"  {period}")
            
            if not periods:
                print("❌ 九江港没有时间期间数据")
                return
            
            # 4. 测试具体期间的查询
            test_period = periods[0]
            print(f"\n测试期间: {test_period}")
            
            # 4.1 检查该期间的统计数量
            result = session.run("""
                MATCH (p:Port {name: '九江'})<-[:PORT_CARGO_STAT_FOR_PORT]-(pmcs:PortMonthCargoStat)
                MATCH (pmcs)-[:PORT_CARGO_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
                RETURN count(pmcs) as period_stats
            """, period=test_period)
            period_stats = result.single()['period_stats']
            print(f"期间 {test_period} 的统计数量: {period_stats}")
            
            # 4.2 检查该期间有货物类型的统计数量
            result = session.run("""
                MATCH (p:Port {name: '九江'})<-[:PORT_CARGO_STAT_FOR_PORT]-(pmcs:PortMonthCargoStat)
                MATCH (pmcs)-[:PORT_CARGO_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
                MATCH (pmcs)-[:PORT_CARGO_STAT_FOR_TYPE]->(ct:CargoType)
                RETURN count(pmcs) as with_cargo_type
            """, period=test_period)
            with_type = result.single()['with_cargo_type']
            print(f"期间 {test_period} 有货物类型的统计数量: {with_type}")
            
            # 4.3 执行完整的查询
            result = session.run("""
                MATCH (p:Port {name: $portName})<-[:PORT_CARGO_STAT_FOR_PORT]-(pmcs:PortMonthCargoStat)-[:PORT_CARGO_STAT_FOR_TYPE]->(ct:CargoType)
                MATCH (pmcs)-[:PORT_CARGO_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
                RETURN ct.subName as cargoType,
                       pmcs.inCargo_ton as inCargoTon,
                       pmcs.outCargo_ton as outCargoTon
                ORDER BY (pmcs.inCargo_ton + pmcs.outCargo_ton) DESC
                LIMIT 5
            """, portName='九江', period=test_period)
            
            print(f"\n期间 {test_period} 的货物数据:")
            count = 0
            for record in result:
                count += 1
                cargo_type = record['cargoType']
                in_cargo = record['inCargoTon']
                out_cargo = record['outCargoTon']
                print(f"  {count}. 货物: {cargo_type}, 进货: {in_cargo}, 出货: {out_cargo}")
            
            if count == 0:
                print("  ❌ 没有找到货物数据")
                
                # 进一步调试：检查数据结构
                print("\n进一步调试:")
                result = session.run("""
                    MATCH (p:Port {name: '九江'})<-[:PORT_CARGO_STAT_FOR_PORT]-(pmcs:PortMonthCargoStat)
                    MATCH (pmcs)-[:PORT_CARGO_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
                    OPTIONAL MATCH (pmcs)-[:PORT_CARGO_STAT_FOR_TYPE]->(ct:CargoType)
                    RETURN pmcs.name as stat_name,
                           pmcs.inCargo_ton as in_cargo,
                           pmcs.outCargo_ton as out_cargo,
                           ct.subName as cargo_type,
                           ym.ym as period_check
                    LIMIT 3
                """, period=test_period)
                
                for record in result:
                    print(f"  统计名称: {record['stat_name']}")
                    print(f"  进货量: {record['in_cargo']}")
                    print(f"  出货量: {record['out_cargo']}")
                    print(f"  货物类型: {record['cargo_type']}")
                    print(f"  期间: {record['period_check']}")
                    print("  ---")
            else:
                print(f"✅ 找到 {count} 条货物数据")
                
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        db.close_all()

if __name__ == "__main__":
    debug_port_cargo_query()
