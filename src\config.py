"""
长江航运智能指标问答Agent (CJHY-KGAgent)
配置管理模块

统一管理所有配置项，包括Neo4j、Oracle、LLM、Mem0、API等配置
"""

from typing import Optional
from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field
from pathlib import Path

# 获取项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
CONFIG_FILE = PROJECT_ROOT / "config.env"


class Neo4jSettings:
    """Neo4j数据库配置 - 直接硬编码"""
    def __init__(self):
        self.uri = "bolt://localhost:7687"
        self.username = "neo4j"
        self.password = "neo4j123"
        self.database = "neo4j"

class OracleSettings:
    """Oracle数据库配置 - 直接硬编码"""
    def __init__(self):
        import os
        from dotenv import load_dotenv
        load_dotenv(str(CONFIG_FILE))
        self.dsn = os.getenv("ORACLE_DSN", "************:1521/chtgldb")
        self.username = os.getenv("ORACLE_USERNAME", "yssjtj")
        self.password = os.getenv("ORACLE_PASSWORD", "Longshine#1")
        # 连接池配置
        self.pool_min = 1
        self.pool_max = 5
        self.pool_increment = 1


class ETLSettings(BaseSettings):
    """ETL配置"""
    batch_size: int = Field(default=100, env="ETL_BATCH_SIZE")
    parallel_jobs: int = Field(default=2, env="ETL_PARALLEL_JOBS")
    max_retries: int = Field(default=3, env="ETL_MAX_RETRIES")
    retry_delay: int = Field(default=5, env="ETL_RETRY_DELAY")

    
    # 数据文件路径
    port_data_file: str = Field(default="doc/图谱-港口.csv", env="PORT_DATA_FILE")
    ship_data_file: str = Field(default="doc/图谱-船舶.csv", env="SHIP_DATA_FILE")
    cargo_types_file: str = Field(default="doc/图谱-货类.csv", env="CARGO_TYPES_FILE")
    ship_classes_file: str = Field(default="doc/图谱-船类.csv", env="SHIP_CLASSES_FILE")
    port_cargo_data_file: str = Field(default="doc/图谱-港口货类.csv", env="PORT_CARGO_DATA_FILE")
    
    # 调度配置
    schedule_enabled: bool = Field(default=False, env="ETL_SCHEDULE_ENABLED")
    schedule_cron: str = Field(default="0 2 * * *", env="ETL_SCHEDULE_CRON")  # 每天凌晨2点
    
    model_config = SettingsConfigDict(env_file=str(CONFIG_FILE), env_file_encoding="utf-8", extra="ignore")


class LLMSettings:
    def __init__(self):
        import os
        from dotenv import load_dotenv
        load_dotenv(str(CONFIG_FILE))
        
        # 服务提供商选择
        self.provider = os.getenv("LLM_PROVIDER", "qwen_cloud")  # qwen_cloud 或 local_openai
        
        # 千问云端配置
        self.qwen_api_key = os.getenv("QWEN_API_KEY","sk-34c2ff0ddfb547809f715b6e7e922657")
        self.qwen_api_base_url = os.getenv(
            "QWEN_API_BASE_URL",
            "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        )
        self.qwen_model_name = os.getenv("QWEN_MODEL_NAME", "qwen-turbo")
        
        # 本地OpenAI兼容配置
        self.local_base_url = os.getenv("LOCAL_LLM_BASE_URL", "http://localhost:8000")
        self.local_model_name = os.getenv("LOCAL_LLM_MODEL_NAME", "Qwen/Qwen2.5-0.5B-Instruct")
        self.local_api_key = os.getenv("LOCAL_LLM_API_KEY", None)  # 可选


class Mem0Settings(BaseSettings):
    """Mem0智能记忆库配置"""
    api_key: Optional[str] = Field(default=None, env="MEM0_API_KEY")
    
    class Config:
        env_file = str(CONFIG_FILE)
        extra = "ignore"


class ApiSettings(BaseSettings):
    """船舶位置API配置"""
    base_url: str = Field(default="http://localhost:9000/api", env="API_BASE_URL")
    api_key: Optional[str] = Field(default=None, env="API_KEY")
    timeout: int = Field(default=10, env="API_TIMEOUT")
    
    class Config:
        env_file = str(CONFIG_FILE)
        extra = "ignore"


class AppSettings(BaseSettings):
    """应用配置"""
    app_env: str = Field(default="development", env="APP_ENV")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    api_host: str = Field(default="0.0.0.0", env="FASTAPI_HOST")
    api_port: int = Field(default=8000, env="FASTAPI_PORT")
    
    class Config:
        env_file = str(CONFIG_FILE)
        extra = "ignore"


class Settings:
    """全局配置管理器"""
    
    def __init__(self):
        self.neo4j = Neo4jSettings()
        self.oracle = OracleSettings()
        self.etl = ETLSettings()
        self.llm = LLMSettings()
        self.mem0 = Mem0Settings()
        self.api = ApiSettings()
        self.app = AppSettings()


# 全局配置实例
settings = Settings()


if __name__ == "__main__":
    # 配置验证和测试
    print("配置加载测试...")
    print(f"配置文件路径: {CONFIG_FILE}")
    print(f"配置文件存在: {CONFIG_FILE.exists()}")
    print(f"Neo4j URI: {settings.neo4j.uri}")
    print(f"Neo4j 用户名: '{settings.neo4j.username}' (长度: {len(settings.neo4j.username)})")
    print(f"Neo4j 密码: '{settings.neo4j.password}' (长度: {len(settings.neo4j.password)})")
    print(f"Oracle DSN: {settings.oracle.dsn}")
    print(f"Oracle 用户名: {settings.oracle.username}")
    print(f"Oracle 密码: {settings.oracle.password}")
    print(f"ETL批次大小: {settings.etl.batch_size}")
    print(f"ETL并行任务数: {settings.etl.parallel_jobs}")
    print(f"Qwen API Key: {settings.llm.qwen_api_key}")
    print(f"Mem0 API Key: {settings.mem0.api_key}")

    print("配置加载完成") 