# 航线ETL使用指南

**版本**: 1.0  
**日期**: 2025年7月31日  
**符合**: 数据库设计文档v4.0航线维度要求

---

## 📋 概述

本指南介绍如何使用新实现的航线ETL模块，该模块完全符合数据库设计文档v4.0的航线维度要求，实现了：

- ✅ **ShippingRoute节点**：标准化航线实体
- ✅ **RouteMonthStat节点**：航线月度统计
- ✅ **RouteMonthCargoStat节点**：航线分货类统计
- ✅ **正确的关系网络**：符合设计文档的关系定义
- ✅ **ShipMonthLineStat修复**：连接到航线节点

---

## 🚀 快速开始

### 1. 基本使用

```python
from src.kgnode.database import DatabaseManager
from src.kgnode.node_route import RouteETL

# 初始化
db_manager = DatabaseManager()
route_etl = RouteETL(database_manager=db_manager, batch_size=1000)

# 执行完整的航线ETL流程
result = route_etl.execute_full_route_etl(
    ship_filter=None,  # 不限制船舶
    time_filter="t.ym >= '202401'",  # 2024年1月以后的数据
    limit=5000  # 限制记录数
)

print(f"创建节点: {result['total_created']}")
print(f"修复关系: {result['relationship_fixes']['fixed']}")
```

### 2. 分步执行

```python
# 1. 创建航线节点
route_result = route_etl.create_shipping_routes_from_data(
    time_filter="t.ym >= '202401'",
    limit=1000
)

# 2. 创建航线月度统计
stat_result = route_etl.create_route_month_stats(
    time_filter="t.ym >= '202401'",
    limit=1000
)

# 3. 创建航线分货类统计
cargo_result = route_etl.create_route_cargo_stats(
    time_filter="t.ym >= '202401'",
    limit=1000
)

# 4. 修复现有ShipMonthLineStat关系
fix_result = route_etl.fix_ship_month_line_stats_relationships(limit=1000)
```

---

## 📊 数据结构

### ShippingRoute节点

```cypher
(:ShippingRoute {
    routeId: "WH-NJ-001",           // 航线唯一标识符
    routeName: "武汉-南京航线",      // 航线名称
    routeCode: "WH-NJ",             // 航线代码
    originPortName: "武汉",         // 起点港口名称
    destinationPortName: "南京",    // 终点港口名称
    distance_km: 500.0,             // 航线距离
    lastUpdated: datetime()         // 最后更新时间
})
```

### RouteMonthStat节点

```cypher
(:RouteMonthStat {
    name: "WH-NJ-001_202406",       // 统计节点名称
    totalShipCount: 25,             // 该月运营船舶总数
    totalVoyageCount: 78,           // 总航次数
    totalCargo_ton: 150000.0,       // 总货运量
    totalTurnover_tonkm: 75000000.0,// 总周转量
    avgLoadRatio: 0.87,             // 平均装载率
    utilizationRate: 0.85,          // 航线利用率
    lastUpdated: datetime()         // 最后更新时间
})
```

### 关系网络

```cypher
// 航线基础关系
(sr:ShippingRoute)-[:ROUTE_ORIGIN]->(pO:Port)
(sr:ShippingRoute)-[:ROUTE_DESTINATION]->(pD:Port)
(sr:ShippingRoute)-[:ROUTE_IN_BASIN]->(b:Basin)

// 统计关系
(rms:RouteMonthStat)-[:ROUTE_STAT_FOR_ROUTE]->(sr:ShippingRoute)
(rms:RouteMonthStat)-[:ROUTE_STAT_FOR_MONTH]->(ym:YearMonth)

// 船舶航线关系（修复后）
(smls:ShipMonthLineStat)-[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
```

---

## 🔧 配置选项

### 过滤参数

```python
# 船舶过滤
ship_filter = "s.ship_name_cn = '汉海5号'"  # 特定船舶
ship_filter = "s.ship_sub_type_name = '散货船'"  # 特定船型

# 时间过滤
time_filter = "t.ym >= '202401'"  # 2024年1月以后
time_filter = "t.ym BETWEEN '202401' AND '202406'"  # 特定时间范围

# 记录限制
limit = 1000  # 限制处理记录数，用于测试
limit = None  # 不限制，处理全部数据
```

### 批处理大小

```python
# 小批次（适合测试）
route_etl = RouteETL(database_manager=db_manager, batch_size=500)

# 大批次（适合生产）
route_etl = RouteETL(database_manager=db_manager, batch_size=2000)
```

---

## 📈 监控和统计

### 获取统计信息

```python
# 获取航线相关统计
stats = route_etl.get_route_statistics()
print(f"总航线数: {stats['total_routes']}")
print(f"航线月度统计: {stats['total_route_month_stats']}")
print(f"航线货物统计: {stats['total_route_cargo_stats']}")
print(f"船舶航线统计: {stats['total_ship_line_stats']}")
```

### 查询示例

```cypher
-- 查询最繁忙的航线TOP5
MATCH (sr:ShippingRoute)<-[:ROUTE_STAT_FOR_ROUTE]-(rms:RouteMonthStat)
-[:ROUTE_STAT_FOR_MONTH]->(ym:YearMonth {ym: "202406"})
RETURN sr.routeName, rms.totalShipCount, rms.totalCargo_ton
ORDER BY rms.totalCargo_ton DESC LIMIT 5

-- 查询特定航线的月度趋势
MATCH (sr:ShippingRoute {routeName: "武汉-南京航线"})
<-[:ROUTE_STAT_FOR_ROUTE]-(rms:RouteMonthStat)
-[:ROUTE_STAT_FOR_MONTH]->(ym:YearMonth)
WHERE ym.ym >= "202401"
RETURN ym.ym, rms.totalShipCount, rms.totalCargo_ton, rms.avgLoadRatio
ORDER BY ym.ym

-- 查询船舶在特定航线的表现
MATCH (s:Ship {name: "汉海5号"})
<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)
-[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
RETURN sr.routeName, smls.cargo_ton, smls.voyageCount
```

---

## 🧪 测试

### 运行测试脚本

```bash
cd src/kgnode
python test_route_etl.py
```

### 测试内容

- ✅ 航线节点创建
- ✅ 航线月度统计创建
- ✅ 航线分货类统计创建
- ✅ ShipMonthLineStat关系修复
- ✅ 完整ETL流程
- ✅ 查询验证

---

## 📝 SQL数据源

### 预置SQL查询

使用 `resource/sql/route_data_queries.sql` 中的预置查询：

1. **航线基础数据提取**：用于创建ShippingRoute节点
2. **航线月度统计**：用于创建RouteMonthStat节点
3. **航线分货类统计**：用于创建RouteMonthCargoStat节点
4. **关系数据提取**：用于建立航线关系
5. **数据质量检查**：验证数据完整性

### 自定义查询

```sql
-- 示例：提取特定时间范围的航线数据
SELECT DISTINCT
    t.merge_city_name_o as origin_port,
    t.merge_city_name_d as destination_port,
    COUNT(*) as frequency,
    AVG(t.MILEAGE) as avg_distance_km
FROM yssjtj.od_dwd_statistics t
WHERE t.ym BETWEEN '202401' AND '202406'
  AND t.merge_city_name_o IS NOT NULL 
  AND t.merge_city_name_d IS NOT NULL
GROUP BY t.merge_city_name_o, t.merge_city_name_d
HAVING COUNT(*) >= 5
ORDER BY COUNT(*) DESC;
```

---

## ⚠️ 注意事项

### 数据质量

1. **航线阈值**：默认要求至少5次航行才创建航线节点
2. **统计阈值**：月度统计要求至少3次航行
3. **货物阈值**：分货类统计要求至少2次运输

### 性能优化

1. **批处理大小**：根据内存情况调整batch_size
2. **记录限制**：测试时使用limit参数
3. **索引建议**：在routeId、ym等字段上创建索引

### 数据一致性

1. **先创建航线节点**：再创建统计节点
2. **关系修复**：在所有节点创建完成后执行
3. **事务处理**：每个批次使用独立事务

---

## 🔗 相关文档

- [数据库设计文档v4.0](数据库设计.md)
- [API设计文档v4.0](新API设计文档v3.0.md)
- [kgnode问题分析与改进建议](kgnode问题分析与改进建议.md)

---

## 📞 支持

如有问题，请参考：
1. 测试脚本输出的详细日志
2. Neo4j数据库中的实际节点和关系
3. Oracle数据源的数据质量检查结果
