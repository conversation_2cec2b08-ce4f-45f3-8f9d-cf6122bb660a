"""
时间维度数据聚合器
用于生成季度和年度统计数据
"""

import pandas as pd
from loguru import logger
from typing import Dict, List, Optional, Any
from datetime import datetime
from .database import DatabaseManager


class TimeAggregator:
    """时间维度数据聚合器"""
    
    def __init__(self, database_manager: DatabaseManager):
        """
        初始化时间聚合器
        
        Args:
            database_manager: 数据库管理器实例
        """
        self.db_manager = database_manager
    
    def create_time_hierarchy(self) -> Dict[str, int]:
        """
        创建完整的时间层次结构
        
        Returns:
            Dict: 创建的时间节点统计
        """
        logger.info("开始创建时间层次结构")
        
        results = {
            "years": 0,
            "quarters": 0,
            "months": 0,
            "relationships": 0,
            "success": True,
            "error": None
        }
        
        try:
            with self.db_manager.neo4j.get_session() as session:
                # 1. 创建年份节点 (2020-2030)
                years_query = """
                UNWIND range(2020, 2030) AS year
                MERGE (y:Year {year: year, name: toString(year)})
                RETURN count(y) as created
                """
                result = session.run(years_query)
                results["years"] = result.single()["created"]
                
                # 2. 创建季度节点
                quarters_query = """
                UNWIND range(2020, 2030) AS year
                UNWIND range(1, 4) AS quarter
                WITH year, quarter, toString(year) + "Q" + toString(quarter) AS quarter_name
                MERGE (q:Quarter {
                    year: year,
                    quarter: quarter,
                    name: quarter_name,
                    startMonth: (quarter - 1) * 3 + 1,
                    endMonth: quarter * 3
                })
                RETURN count(q) as created
                """
                result = session.run(quarters_query)
                results["quarters"] = result.single()["created"]
                
                # 3. 创建月份节点 (基于现有YearMonth)
                months_query = """
                UNWIND range(2020, 2030) AS year
                UNWIND range(1, 12) AS month
                WITH year, month,
                     toString(year) + CASE WHEN month < 10 THEN "0" + toString(month) ELSE toString(month) END AS ym_value,
                     toString(year) + "-" + CASE WHEN month < 10 THEN "0" + toString(month) ELSE toString(month) END AS month_name
                MERGE (ym:YearMonth {
                    ym: ym_value,
                    year: year,
                    month: month,
                    name: month_name
                })
                RETURN count(ym) as created
                """
                result = session.run(months_query)
                results["months"] = result.single()["created"]
                
                # 4. 建立时间层次关系
                total_rels = 0

                # 月份 -> 季度关系
                month_quarter_query = """
                MATCH (ym:YearMonth), (q:Quarter)
                WHERE ym.year = q.year
                  AND ym.month >= q.startMonth
                  AND ym.month <= q.endMonth
                MERGE (ym)-[:BELONGS_TO_QUARTER]->(q)
                RETURN count(*) as rels_created
                """
                result = session.run(month_quarter_query)
                month_quarter_rels = result.single()["rels_created"]
                total_rels += month_quarter_rels

                # 季度 -> 年份关系
                quarter_year_query = """
                MATCH (q:Quarter), (y:Year)
                WHERE q.year = y.year
                MERGE (q)-[:BELONGS_TO_YEAR]->(y)
                RETURN count(*) as rels_created
                """
                result = session.run(quarter_year_query)
                quarter_year_rels = result.single()["rels_created"]
                total_rels += quarter_year_rels

                # 月份 -> 年份关系
                month_year_query = """
                MATCH (ym:YearMonth), (y:Year)
                WHERE ym.year = y.year
                MERGE (ym)-[:BELONGS_TO_YEAR]->(y)
                RETURN count(*) as rels_created
                """
                result = session.run(month_year_query)
                month_year_rels = result.single()["rels_created"]
                total_rels += month_year_rels

                results["relationships"] = total_rels
                
        except Exception as e:
            logger.error(f"创建时间层次结构失败: {e}")
            results["success"] = False
            results["error"] = str(e)

        logger.info(f"时间层次结构创建完成: {results}")
        return results
    
    def aggregate_ship_quarter_stats(self, year: int, quarter: int) -> Dict[str, int]:
        """
        聚合船舶季度统计数据
        
        Args:
            year: 年份
            quarter: 季度 (1-4)
            
        Returns:
            Dict: 聚合结果统计
        """
        logger.info(f"开始聚合船舶 {year}Q{quarter} 季度统计")
        
        # 计算季度包含的月份
        start_month = (quarter - 1) * 3 + 1
        end_month = quarter * 3
        month_list = [f"{year}{i:02d}" for i in range(start_month, end_month + 1)]
        
        try:
            with self.db_manager.neo4j.get_session() as session:
                query = """
                // 查找该季度的所有月度统计
                MATCH (sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth)
                WHERE ym.ym IN $month_list
                
                // 按船舶分组聚合
                MATCH (sms)-[:STAT_FOR_SHIP]->(s:Ship)
                WITH s, 
                     collect(sms) as month_stats,
                     $year as year,
                     $quarter as quarter
                
                // 创建季度统计节点
                MERGE (sqs:ShipQuarterStat {
                    name: s.mmsi + "_" + toString(year) + "Q" + toString(quarter)
                })
                SET sqs.year = year,
                    sqs.quarter = quarter,
                    // 聚合指标 - 求和类
                    sqs.totalVoyages = reduce(sum = 0, stat IN month_stats | 
                        sum + coalesce(stat.voyages, 0)),
                    sqs.totalLoadVoyages = reduce(sum = 0, stat IN month_stats | 
                        sum + coalesce(stat.loadVoyages, 0)),
                    sqs.totalTurnover_tonkm = reduce(sum = 0, stat IN month_stats | 
                        sum + coalesce(stat.turnover_tonkm, 0)),
                    sqs.totalMileage_km = reduce(sum = 0, stat IN month_stats | 
                        sum + coalesce(stat.mileage_km, 0)),
                    sqs.totalAnchorTime_day = reduce(sum = 0, stat IN month_stats | 
                        sum + coalesce(stat.anchorTime_day, 0)),
                    sqs.totalSailTime_day = reduce(sum = 0, stat IN month_stats | 
                        sum + coalesce(stat.sailTime_day, 0)),
                    
                    // 聚合指标 - 平均类
                    sqs.avgOpRatio = reduce(sum = 0, stat IN month_stats | 
                        sum + coalesce(stat.opRatio, 0)) / size(month_stats),
                    sqs.avgLoadRatio = reduce(sum = 0, stat IN month_stats | 
                        sum + coalesce(stat.loadRatio, 0)) / size(month_stats),
                    sqs.avgSailRatio = reduce(sum = 0, stat IN month_stats | 
                        sum + coalesce(stat.sailRatio, 0)) / size(month_stats),
                    
                    sqs.monthCount = size(month_stats),
                    sqs.lastUpdated = datetime()
                
                // 建立关系
                MERGE (q:Quarter {year: year, quarter: quarter})
                MERGE (sqs)-[:STAT_FOR_SHIP]->(s)
                MERGE (sqs)-[:STAT_FOR_QUARTER]->(q)
                
                RETURN count(sqs) as created
                """
                
                result = session.run(query, 
                    month_list=month_list, 
                    year=year, 
                    quarter=quarter
                )
                created = result.single()["created"]
                
                logger.info(f"船舶 {year}Q{quarter} 季度统计聚合完成: {created} 条记录")
                return {"created": created, "errors": 0}
                
        except Exception as e:
            logger.error(f"船舶季度统计聚合失败: {e}")
            return {"created": 0, "errors": 1}
    
    def aggregate_ship_year_stats(self, year: int) -> Dict[str, int]:
        """
        聚合船舶年度统计数据
        
        Args:
            year: 年份
            
        Returns:
            Dict: 聚合结果统计
        """
        logger.info(f"开始聚合船舶 {year} 年度统计")
        
        try:
            with self.db_manager.neo4j.get_session() as session:
                query = """
                // 查找该年度的所有季度统计
                MATCH (sqs:ShipQuarterStat)-[:STAT_FOR_QUARTER]->(q:Quarter)
                WHERE q.year = $year
                
                // 按船舶分组聚合
                MATCH (sqs)-[:STAT_FOR_SHIP]->(s:Ship)
                WITH s, 
                     collect(sqs) as quarter_stats,
                     $year as year
                
                // 创建年度统计节点
                MERGE (sys:ShipYearStat {
                    name: s.mmsi + "_" + toString(year)
                })
                SET sys.year = year,
                    // 聚合指标 - 求和类
                    sys.annualTotalVoyages = reduce(sum = 0, stat IN quarter_stats | 
                        sum + coalesce(stat.totalVoyages, 0)),
                    sys.annualTotalLoadVoyages = reduce(sum = 0, stat IN quarter_stats | 
                        sum + coalesce(stat.totalLoadVoyages, 0)),
                    sys.annualTotalTurnover_tonkm = reduce(sum = 0, stat IN quarter_stats | 
                        sum + coalesce(stat.totalTurnover_tonkm, 0)),
                    sys.annualTotalMileage_km = reduce(sum = 0, stat IN quarter_stats | 
                        sum + coalesce(stat.totalMileage_km, 0)),
                    sys.annualTotalAnchorTime_day = reduce(sum = 0, stat IN quarter_stats | 
                        sum + coalesce(stat.totalAnchorTime_day, 0)),
                    sys.annualTotalSailTime_day = reduce(sum = 0, stat IN quarter_stats | 
                        sum + coalesce(stat.totalSailTime_day, 0)),
                    
                    // 聚合指标 - 平均类
                    sys.annualAvgOpRatio = reduce(sum = 0, stat IN quarter_stats | 
                        sum + coalesce(stat.avgOpRatio, 0)) / size(quarter_stats),
                    sys.annualAvgLoadRatio = reduce(sum = 0, stat IN quarter_stats | 
                        sum + coalesce(stat.avgLoadRatio, 0)) / size(quarter_stats),
                    sys.annualAvgSailRatio = reduce(sum = 0, stat IN quarter_stats | 
                        sum + coalesce(stat.avgSailRatio, 0)) / size(quarter_stats),
                    
                    sys.quarterCount = size(quarter_stats),
                    sys.lastUpdated = datetime()
                
                // 建立关系
                MERGE (y:Year {year: year})
                MERGE (sys)-[:STAT_FOR_SHIP]->(s)
                MERGE (sys)-[:STAT_FOR_YEAR]->(y)
                
                RETURN count(sys) as created
                """
                
                result = session.run(query, year=year)
                created = result.single()["created"]
                
                logger.info(f"船舶 {year} 年度统计聚合完成: {created} 条记录")
                return {"created": created, "errors": 0}
                
        except Exception as e:
            logger.error(f"船舶年度统计聚合失败: {e}")
            return {"created": 0, "errors": 1}
    
    def aggregate_port_quarter_stats(self, year: int, quarter: int) -> Dict[str, int]:
        """
        聚合港口季度统计数据
        
        Args:
            year: 年份
            quarter: 季度 (1-4)
            
        Returns:
            Dict: 聚合结果统计
        """
        logger.info(f"开始聚合港口 {year}Q{quarter} 季度统计")
        
        # 计算季度包含的月份
        start_month = (quarter - 1) * 3 + 1
        end_month = quarter * 3
        month_list = [f"{year}{i:02d}" for i in range(start_month, end_month + 1)]
        
        try:
            with self.db_manager.neo4j.get_session() as session:
                query = """
                // 查找该季度的所有港口月度统计
                MATCH (pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth)
                WHERE ym.ym IN $month_list
                
                // 按港口分组聚合
                MATCH (pms)-[:STAT_FOR_PORT]->(p:Port)
                WITH p, 
                     collect(pms) as month_stats,
                     $year as year,
                     $quarter as quarter
                
                // 创建港口季度统计节点
                MERGE (pqs:PortQuarterStat {
                    name: p.name + "_" + toString(year) + "Q" + toString(quarter)
                })
                SET pqs.year = year,
                    pqs.quarter = quarter,
                    // 聚合指标 - 求和类
                    pqs.totalInShipCount = reduce(sum = 0, stat IN month_stats | 
                        sum + coalesce(stat.inShipCount, 0)),
                    pqs.totalOutShipCount = reduce(sum = 0, stat IN month_stats | 
                        sum + coalesce(stat.outShipCount, 0)),
                    pqs.totalInCargo_ton = reduce(sum = 0, stat IN month_stats | 
                        sum + coalesce(stat.inCargo_ton, 0)),
                    pqs.totalOutCargo_ton = reduce(sum = 0, stat IN month_stats | 
                        sum + coalesce(stat.outCargo_ton, 0)),
                    
                    // 聚合指标 - 平均类
                    pqs.avgAnchorTime_days = reduce(sum = 0, stat IN month_stats | 
                        sum + coalesce(stat.anchorTime_days, 0)) / size(month_stats),
                    pqs.avgInLoadRatio = reduce(sum = 0, stat IN month_stats | 
                        sum + coalesce(stat.inLoadRatio, 0)) / size(month_stats),
                    pqs.avgOutLoadRatio = reduce(sum = 0, stat IN month_stats | 
                        sum + coalesce(stat.outLoadRatio, 0)) / size(month_stats),
                    
                    pqs.monthCount = size(month_stats),
                    pqs.lastUpdated = datetime()
                
                // 建立关系
                MERGE (q:Quarter {year: year, quarter: quarter})
                MERGE (pqs)-[:STAT_FOR_PORT]->(p)
                MERGE (pqs)-[:STAT_FOR_QUARTER]->(q)
                
                RETURN count(pqs) as created
                """
                
                result = session.run(query, 
                    month_list=month_list, 
                    year=year, 
                    quarter=quarter
                )
                created = result.single()["created"]
                
                logger.info(f"港口 {year}Q{quarter} 季度统计聚合完成: {created} 条记录")
                return {"created": created, "errors": 0}
                
        except Exception as e:
            logger.error(f"港口季度统计聚合失败: {e}")
            return {"created": 0, "errors": 1}
    
    def execute_full_aggregation(self, year: int) -> Dict[str, Any]:
        """
        执行完整的年度聚合流程
        
        Args:
            year: 目标年份
            
        Returns:
            Dict: 完整的聚合结果
        """
        logger.info(f"开始执行 {year} 年度完整聚合流程")
        
        results = {
            "year": year,
            "time_hierarchy": {"years": 0, "quarters": 0, "months": 0, "relationships": 0},
            "ship_quarters": [],
            "ship_year": {"created": 0, "errors": 0},
            "port_quarters": [],
            "total_created": 0,
            "total_errors": 0,
            "success": True,
            "error": None
        }
        
        try:
            # 1. 创建时间层次结构
            results["time_hierarchy"] = self.create_time_hierarchy()
            
            # 2. 聚合各季度的船舶统计
            for quarter in range(1, 5):
                ship_quarter_result = self.aggregate_ship_quarter_stats(year, quarter)
                results["ship_quarters"].append({
                    "quarter": quarter,
                    "result": ship_quarter_result
                })
                results["total_created"] += ship_quarter_result["created"]
                results["total_errors"] += ship_quarter_result["errors"]
            
            # 3. 聚合年度船舶统计
            results["ship_year"] = self.aggregate_ship_year_stats(year)
            results["total_created"] += results["ship_year"]["created"]
            results["total_errors"] += results["ship_year"]["errors"]
            
            # 4. 聚合各季度的港口统计
            for quarter in range(1, 5):
                port_quarter_result = self.aggregate_port_quarter_stats(year, quarter)
                results["port_quarters"].append({
                    "quarter": quarter,
                    "result": port_quarter_result
                })
                results["total_created"] += port_quarter_result["created"]
                results["total_errors"] += port_quarter_result["errors"]
            
            logger.info(f"{year} 年度聚合完成: 创建 {results['total_created']} 个节点, {results['total_errors']} 个错误")
            
        except Exception as e:
            logger.error(f"年度聚合流程失败: {e}")
            results["total_errors"] += 1
            results["success"] = False
            results["error"] = str(e)

        return results
