#!/usr/bin/env python3
"""
测试港口货物数据修复
"""

import sys
import os

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_port_cargo_fix():
    """测试港口货物数据修复"""
    from kgnode.node_port import PortDynamicETL
    from kgnode.database import DatabaseManager
    
    print("=== 测试港口货物数据修复 ===")
    
    db = DatabaseManager()
    port_etl = PortDynamicETL(db)
    
    try:
        # 1. 清理九江港的旧数据
        print("1. 清理九江港的旧货物统计数据...")
        with db.neo4j.get_session() as session:
            result = session.run("""
                MATCH (p:Port {name: '九江'})<-[:PORT_CARGO_STAT_FOR_PORT]-(pmcs:PortMonthCargoStat)
                MATCH (pmcs)-[:PORT_CARGO_STAT_FOR_MONTH]->(ym:YearMonth {ym: '202507'})
                DETACH DELETE pmcs
                RETURN count(*) as deleted
            """)
            deleted = result.single()['deleted']
            print(f"   删除了 {deleted} 个旧的货物统计节点")
        
        # 2. 重新加载九江港202507期间的数据
        print("2. 重新加载九江港202507期间的货物统计数据...")
        result = port_etl.load_port_cargo_stats('九江', "ym like '202507%'")
        print(f"   加载结果: {result}")
        
        # 3. 验证修复后的数据
        print("3. 验证修复后的数据...")
        with db.neo4j.get_session() as session:
            result = session.run("""
                MATCH (p:Port {name: '九江'})<-[:PORT_CARGO_STAT_FOR_PORT]-(pmcs:PortMonthCargoStat)-[:PORT_CARGO_STAT_FOR_TYPE]->(ct:CargoType)
                MATCH (pmcs)-[:PORT_CARGO_STAT_FOR_MONTH]->(ym:YearMonth {ym: '202507'})
                RETURN ct.subName as cargoType,
                       pmcs.inCargo_ton as inCargoTon,
                       pmcs.outCargo_ton as outCargoTon
                ORDER BY (pmcs.inCargo_ton + pmcs.outCargo_ton) DESC
                LIMIT 5
            """)
            
            print("   修复后的货物数据:")
            count = 0
            for record in result:
                count += 1
                cargo_type = record['cargoType']
                in_cargo = record['inCargoTon']
                out_cargo = record['outCargoTon']
                total = in_cargo + out_cargo
                print(f"     {count}. {cargo_type}: 进货 {in_cargo:.1f}吨, 出货 {out_cargo:.1f}吨, 总计 {total:.1f}吨")
            
            if count == 0:
                print("     ❌ 仍然没有找到数据")
                return False
            else:
                print(f"     ✅ 找到 {count} 条有效的货物数据")
                return True
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        port_etl.close()

def test_original_query():
    """测试原始查询是否现在能正常工作"""
    from kgnode.database import DatabaseManager
    
    print("\n=== 测试原始查询 ===")
    
    db = DatabaseManager()
    
    try:
        with db.neo4j.get_session() as session:
            # 这是您日志中的原始查询
            result = session.run("""
                MATCH (p:Port {name: $portName})<-[:PORT_CARGO_STAT_FOR_PORT]-(pmcs:PortMonthCargoStat)-[:PORT_CARGO_STAT_FOR_TYPE]->(ct:CargoType)
                MATCH (pmcs)-[:PORT_CARGO_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
                RETURN ct.subName as cargoType,
                       pmcs.inCargo_ton as inCargoTon,
                       pmcs.outCargo_ton as outCargoTon
                ORDER BY (pmcs.inCargo_ton + pmcs.outCargo_ton) DESC
            """, portName='九江', period='202507')
            
            print("原始查询结果:")
            count = 0
            for record in result:
                count += 1
                cargo_type = record['cargoType']
                in_cargo = record['inCargoTon']
                out_cargo = record['outCargoTon']
                total = in_cargo + out_cargo
                print(f"  {count}. {cargo_type}: 进货 {in_cargo:.1f}吨, 出货 {out_cargo:.1f}吨, 总计 {total:.1f}吨")
                
                if count >= 5:  # 只显示前5条
                    break
            
            if count == 0:
                print("  ❌ 原始查询仍然返回空结果")
                return False
            else:
                print(f"  ✅ 原始查询返回 {count} 条结果")
                return True
                
    except Exception as e:
        print(f"❌ 原始查询测试失败: {e}")
        return False
    
    finally:
        db.close_all()

def main():
    """运行所有测试"""
    print("开始测试港口货物数据修复\n")
    
    # 测试1: 修复数据
    success1 = test_port_cargo_fix()
    
    # 测试2: 验证原始查询
    success2 = test_original_query()
    
    print(f"\n=== 测试结果 ===")
    if success1 and success2:
        print("🎉 所有测试通过！港口货物数据修复成功")
        print("\n✅ 修复总结:")
        print("1. 修复了字段名映射问题：INCARGO_TUN/OUTCARGO_TUN")
        print("2. 港口货物统计数据现在包含正确的数值")
        print("3. 原始查询现在能正常返回数据")
        return True
    else:
        print("❌ 部分测试失败")
        if not success1:
            print("- 数据修复失败")
        if not success2:
            print("- 原始查询仍有问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
