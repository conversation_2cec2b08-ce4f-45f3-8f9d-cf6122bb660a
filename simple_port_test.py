#!/usr/bin/env python3
"""
简化的港口航线查询测试
"""

import sys
import os

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_import():
    """测试导入"""
    try:
        from kgnode.database import DatabaseManager
        from kgnode.node_port import PortDynamicETL
        print("✅ 导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_connection():
    """测试基本连接"""
    try:
        from kgnode.database import DatabaseManager
        
        db_manager = DatabaseManager()
        print("✅ 数据库管理器创建成功")
        
        with db_manager.neo4j.get_session() as session:
            result = session.run("MATCH (p:Port) RETURN count(p) as count LIMIT 1")
            count = result.single()['count']
            print(f"✅ Neo4j连接成功，港口数量: {count}")
        
        db_manager.close_all()
        return True
        
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_port_etl_creation():
    """测试港口ETL创建"""
    try:
        from kgnode.database import DatabaseManager
        from kgnode.node_port import PortDynamicETL
        
        db_manager = DatabaseManager()
        port_etl = PortDynamicETL(db_manager)
        print("✅ PortDynamicETL创建成功")
        
        # 测试获取港口列表
        ports = port_etl.get_all_ports()
        print(f"✅ 获取港口列表成功，数量: {len(ports)}")
        if ports:
            print(f"   示例港口: {ports[:5]}")
        
        port_etl.close()
        return True
        
    except Exception as e:
        print(f"❌ PortDynamicETL测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_new_methods():
    """测试新增的方法"""
    try:
        from kgnode.database import DatabaseManager
        from kgnode.node_port import PortDynamicETL
        
        db_manager = DatabaseManager()
        port_etl = PortDynamicETL(db_manager)
        
        # 测试新方法是否存在
        if hasattr(port_etl, 'get_port_related_routes'):
            print("✅ get_port_related_routes 方法存在")
        else:
            print("❌ get_port_related_routes 方法不存在")
            
        if hasattr(port_etl, 'get_port_navigation_summary'):
            print("✅ get_port_navigation_summary 方法存在")
        else:
            print("❌ get_port_navigation_summary 方法不存在")
        
        # 简单测试一个方法
        result = port_etl.get_port_related_routes("九江", "202407")
        print(f"✅ get_port_related_routes 调用成功")
        print(f"   结果类型: {type(result)}")
        print(f"   包含键: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        
        port_etl.close()
        return True
        
    except Exception as e:
        print(f"❌ 新方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=== 简化港口测试 ===\n")
    
    tests = [
        ("导入测试", test_import),
        ("连接测试", test_basic_connection),
        ("ETL创建测试", test_port_etl_creation),
        ("新方法测试", test_new_methods)
    ]
    
    for test_name, test_func in tests:
        print(f"--- {test_name} ---")
        success = test_func()
        print(f"结果: {'✅ 通过' if success else '❌ 失败'}\n")
        
        if not success:
            print(f"测试在 {test_name} 阶段失败，停止后续测试")
            return False
    
    print("🎉 所有测试通过！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
