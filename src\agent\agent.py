from __future__ import annotations

import json
import os
from typing import Dict, Any, List, Optional, TypedDict, Callable, Annotated

from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, AIMessage, ToolMessage
from loguru import logger

from src.agent.tool import (
    TOOL_REGISTRY,
    FUNCTION_SCHEMAS,
)
from src.agent.llm import get_qwen_client
from src.agent.nlu import NLU  

USE_NATIVE_FC: bool = os.getenv("AGENT_NATIVE_FC", "false").lower() == "true"
class AgentXState(TypedDict):
    messages: Annotated[List, add_messages]
    current_question: str
    tool_result: Optional[Dict[str, Any]]
    final_answer: str
    error_info: Optional[Dict[str, Any]]


class AgentX:

    def __init__(self):
        self.logger = logger.bind(agent="AgentX")
        self.llm_client = get_qwen_client()
        if not USE_NATIVE_FC:
            self.nlu = NLU()
        else:
            self.nlu = None

        # 构建 LangGraph 工作流
        self.graph = self._build_graph()

    def _build_graph(self) -> StateGraph:
        sg = StateGraph(AgentXState)

        sg.add_node("decide_and_call", self._decide_and_call)
        sg.add_node("generate_answer", self._generate_answer)
        sg.add_node("handle_error", self._handle_error)

        sg.set_entry_point("decide_and_call")
        sg.add_edge("decide_and_call", "generate_answer")
        sg.add_edge("generate_answer", END)
        sg.add_edge("handle_error", END)

        return sg.compile()

    def _decide_and_call(self, state: AgentXState) -> AgentXState:
        question = state.get("current_question", "")
        try:
            if USE_NATIVE_FC:
                tool_result = self._native_fc_flow(question)
            else:
                tool_result = self._nlu_flow(question)

            state["tool_result"] = tool_result
        except Exception as e:  # pragma: no cover
            self.logger.error(f"工具调用失败: {e}")
            state["error_info"] = {
                "stage": "decide_and_call",
                "error": str(e),
                "message": "工具调用出现错误",
            }
        return state

    def _call_tool_by_name(self, fn_name: str, args: Dict[str, Any]) -> Dict[str, Any]:
        """
        根据函数名称和参数调用 TOOL_REGISTRY 中的工具，统一参数解析逻辑，便于代码复用。
        """
        if fn_name not in TOOL_REGISTRY:
            raise ValueError(f"未注册的函数: {fn_name}")

        # 统一参数解析
        if fn_name in ["get_ais_data", "get_ship_archive"]:
            # 这两个函数期望 keyword 参数
            keyword = (
                args.get("keyword")
                or args.get("ship_name")
                or args.get("mmsi")
            )
            if not keyword:
                raise ValueError(f"函数 {fn_name} 缺少必需参数 keyword/ship_name/mmsi")
            return TOOL_REGISTRY[fn_name](keyword)
        elif fn_name == "get_area_ships":
            coordinates = args.get("coordinates")
            if not coordinates:
                raise ValueError(f"函数 {fn_name} 缺少必需参数 coordinates")
            return TOOL_REGISTRY[fn_name](coordinates)
        elif fn_name == "get_section_data":
            section_name = args.get("section_name")
            if not section_name:
                raise ValueError(f"函数 {fn_name} 缺少必需参数 section_name")
            return TOOL_REGISTRY[fn_name](section_name)
        elif fn_name == "get_bridge_section_ships":
            section_name = args.get("section_name")
            if not section_name:
                raise ValueError(f"函数 {fn_name} 缺少必需参数 section_name")
            minutes = args.get("minutes", 10)
            return TOOL_REGISTRY[fn_name](section_name, minutes)
        elif fn_name == "get_circle_ships":
            lat = args.get("lat")
            lon = args.get("lon")
            radius = args.get("radius")
            if lat is None or lon is None or radius is None:
                raise ValueError(f"函数 {fn_name} 缺少必需参数 lat/lon/radius")
            return TOOL_REGISTRY[fn_name](lat, lon, radius)
        else:
            # 其他函数直接传递字典参数
            return TOOL_REGISTRY[fn_name](args)

    # ──────────────────────────────────────
    # Flow A: 使用原生 function-call
    # ──────────────────────────────────────
    def _native_fc_flow(self, question: str) -> Dict[str, Any]:
        self.logger.info("使用原生 function-call 模式")
        messages = [
            {"role": "user", "content": question},
        ]
        response = self.llm_client.chat(
            messages,
            tools=FUNCTION_SCHEMAS,
            temperature=0,
        )
        function_call = None
        if isinstance(response, dict):
            # 检查是否是工具调用降级的情况
            if response.get("downgraded") is True:
                self.logger.info("检测到工具调用降级，直接返回文本内容")
                # 返回一个特殊格式的结果，表示这是降级后的文本回复
                return {
                    "status": "success",
                    "source": "llm_text",
                    "data": {
                        "answer": response.get("content", ""),
                        "downgraded": True
                    }
                }
            
            # qwen 返回格式化的 function_call
            function_call = response.get("function_call")
        else:
            # 兼容 openai.ChatCompletion
            function_call = response.choices[0].message.function_call  # type: ignore

        if not function_call:
            self.logger.error(f"响应格式: {response}")
            raise ValueError("模型未触发任何函数调用")

        fn_name = function_call["name"] if isinstance(function_call, dict) else function_call.name
        args_json = function_call["arguments"] if isinstance(function_call, dict) else function_call.arguments
        self.logger.info(f"模型决定调用 {fn_name}，参数: {args_json}")
        args: Dict[str, Any] = json.loads(args_json)

        return self._call_tool_by_name(fn_name, args)

    # ──────────────────────────────────────
    # Flow B: 使用 NLU 解析 tool_call
    # ──────────────────────────────────────
    def _nlu_flow(self, question: str) -> Dict[str, Any]:
        self.logger.info("使用 NLU tool_call 模式")
        nlu_result = self.nlu.process(question)
        tool_call = nlu_result.get("tool_call", {})
        if not tool_call.get("needed"):
            raise ValueError("NLU 判断无需实时工具调用")

        fn_name = tool_call.get("tool_name")
        params = tool_call.get("parameters", {})
        self.logger.info(f"NLU 解析调用 {fn_name}，参数: {params}")
        return self._call_tool_by_name(fn_name, params)

    def _generate_answer(self, state: AgentXState) -> AgentXState:
        tool_result = state.get("tool_result", {})
        question = state.get("current_question", "")

        if not tool_result:
            state["final_answer"] = "抱歉，未查询到任何数据。"
            return state

        if (tool_result.get("source") == "downgraded"):
            answer = tool_result.get("data", {})
            state["final_answer"] = answer if answer else "抱歉，未能获取到有效回复。"
            self.logger.info("使用降级后的文本回复作为最终答案",tool_result)
            return state

        # 若需要再次调用 LLM 生成自然语言答案，可在此添加
        try:
            # system_prompt = "你是一名长江航运知识问答助手，请阅读 JSON 数据后，用中文回答用户的问题。要求回答专业简洁,但包含该包含的信息，如果没找到数据，请回答：抱歉，未查询到任何数据。领域知识：返回位置包含经纬度和航道里程、所在港口和时效性。不要展示思维链的过程。"
            with open("src/prompt/gen_0.6.txt", "r", encoding="utf-8") as f:
                system_prompt = f.read()
            user_prompt = (
                f"用户问题: {question}\n\nJSON 数据如下:\n{json.dumps(tool_result, ensure_ascii=False, indent=2)}"
            )
            answer = self.llm_client.chat(
                [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt},
                ],
                temperature=0,
            )
            state["final_answer"] = answer.strip() if answer else json.dumps(tool_result, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"调用 LLM 生成答案失败: {e}")
            # 回退到直接返回 JSON
            state["final_answer"] = json.dumps(tool_result, ensure_ascii=False)
        return state

 
    def _handle_error(self, state: AgentXState) -> AgentXState:  # noqa: D401
        error_info = state.get("error_info", {})
        state["final_answer"] = (
            f"抱歉，{error_info.get('message', '系统出现未知错误')}。"
        )
        return state


    def answer(self, question: str) -> str:
        """处理单个用户问题，返回答案字符串"""
        initial_state: AgentXState = {
            "messages": [HumanMessage(content=question)],
            "current_question": question,
            "tool_result": None,
            "final_answer": "",
            "error_info": None,
        }
        final_state = self.graph.invoke(initial_state)
        return final_state["final_answer"]


# ──────────────────────────────────────────────────────────────
# 创建全局实例，方便直接 import 使用
# ──────────────────────────────────────────────────────────────
agent_x = AgentX()

if __name__ == "__main__":
    q = "123456789现在在哪里？"
    print("Q:", q)
    print("A:", agent_x.answer(q)) 
    # q = "123456789档案？"
    # print("Q:", q)
    # print("A:", agent_x.answer(q)) 
    # q = "121.39附近有哪些船舶？"
    # print("Q:", q)
    # print("A:", agent_x.answer(q)) 