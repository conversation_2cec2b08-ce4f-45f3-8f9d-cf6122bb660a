"""
航线ETL测试脚本
测试航线节点的创建和关系建立功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
from datetime import datetime
from src.kgnode.database import DatabaseManager
from src.kgnode.node_route import RouteETL

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_route_etl():
    """测试航线ETL功能"""
    
    logger.info("=" * 60)
    logger.info("开始测试航线ETL功能")
    logger.info("=" * 60)
    
    try:
        # 初始化数据库管理器
        db_manager = DatabaseManager()
        logger.info("✅ 数据库连接初始化成功")
        
        # 初始化航线ETL处理器
        route_etl = RouteETL(database_manager=db_manager, batch_size=500)
        logger.info("✅ 航线ETL处理器初始化成功")
        
        # 测试参数
        test_params = {
            "ship_filter": None,  # 不限制船舶
            "time_filter": "t.ym >= '202401'",  # 2024年1月以后的数据
            "limit": 1000  # 限制1000条记录用于测试
        }
        
        logger.info(f"测试参数: {test_params}")
        
        # 1. 测试创建航线节点
        logger.info("\n" + "=" * 40)
        logger.info("测试1: 创建航线节点")
        logger.info("=" * 40)
        
        route_result = route_etl.create_shipping_routes_from_data(
            ship_filter=test_params["ship_filter"],
            time_filter=test_params["time_filter"],
            limit=test_params["limit"]
        )
        
        logger.info(f"航线节点创建结果: {route_result}")
        
        if route_result["created"] > 0:
            logger.info("✅ 航线节点创建成功")
        else:
            logger.warning("⚠️ 未创建任何航线节点")
        
        # 2. 测试创建航线月度统计节点
        logger.info("\n" + "=" * 40)
        logger.info("测试2: 创建航线月度统计节点")
        logger.info("=" * 40)
        
        route_stat_result = route_etl.create_route_month_stats(
            ship_filter=test_params["ship_filter"],
            time_filter=test_params["time_filter"],
            limit=test_params["limit"]
        )
        
        logger.info(f"航线月度统计节点创建结果: {route_stat_result}")
        
        if route_stat_result["created"] > 0:
            logger.info("✅ 航线月度统计节点创建成功")
        else:
            logger.warning("⚠️ 未创建任何航线月度统计节点")
        
        # 3. 测试创建航线分货类统计节点
        logger.info("\n" + "=" * 40)
        logger.info("测试3: 创建航线分货类统计节点")
        logger.info("=" * 40)
        
        route_cargo_result = route_etl.create_route_cargo_stats(
            ship_filter=test_params["ship_filter"],
            time_filter=test_params["time_filter"],
            limit=test_params["limit"]
        )
        
        logger.info(f"航线分货类统计节点创建结果: {route_cargo_result}")
        
        if route_cargo_result["created"] > 0:
            logger.info("✅ 航线分货类统计节点创建成功")
        else:
            logger.warning("⚠️ 未创建任何航线分货类统计节点")
        
        # 4. 测试修复ShipMonthLineStat关系
        logger.info("\n" + "=" * 40)
        logger.info("测试4: 修复ShipMonthLineStat关系")
        logger.info("=" * 40)
        
        fix_result = route_etl.fix_ship_month_line_stats_relationships(limit=500)
        
        logger.info(f"关系修复结果: {fix_result}")
        
        if fix_result["fixed"] > 0:
            logger.info("✅ ShipMonthLineStat关系修复成功")
        else:
            logger.warning("⚠️ 未修复任何ShipMonthLineStat关系")
        
        # 5. 获取航线统计信息
        logger.info("\n" + "=" * 40)
        logger.info("测试5: 获取航线统计信息")
        logger.info("=" * 40)
        
        stats = route_etl.get_route_statistics()
        logger.info(f"航线统计信息: {stats}")
        
        # 6. 测试完整ETL流程
        logger.info("\n" + "=" * 40)
        logger.info("测试6: 完整ETL流程")
        logger.info("=" * 40)
        
        full_etl_result = route_etl.execute_full_route_etl(
            ship_filter=test_params["ship_filter"],
            time_filter=test_params["time_filter"],
            limit=500  # 减少数量以加快测试
        )
        
        logger.info(f"完整ETL流程结果: {full_etl_result}")
        
        # 总结测试结果
        logger.info("\n" + "=" * 60)
        logger.info("测试结果总结")
        logger.info("=" * 60)
        
        total_created = (
            route_result.get("created", 0) + 
            route_stat_result.get("created", 0) + 
            route_cargo_result.get("created", 0)
        )
        
        total_errors = (
            route_result.get("errors", 0) + 
            route_stat_result.get("errors", 0) + 
            route_cargo_result.get("errors", 0) + 
            fix_result.get("errors", 0)
        )
        
        logger.info(f"✅ 总计创建节点: {total_created}")
        logger.info(f"✅ 总计修复关系: {fix_result.get('fixed', 0)}")
        logger.info(f"❌ 总计错误: {total_errors}")
        
        if total_errors == 0:
            logger.info("🎉 所有测试通过！")
        else:
            logger.warning(f"⚠️ 测试中出现 {total_errors} 个错误")
        
        return {
            "success": total_errors == 0,
            "total_created": total_created,
            "total_fixed": fix_result.get("fixed", 0),
            "total_errors": total_errors,
            "statistics": stats
        }
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生异常: {e}")
        return {
            "success": False,
            "error": str(e)
        }


def test_route_queries():
    """测试航线相关的查询"""
    
    logger.info("\n" + "=" * 60)
    logger.info("测试航线相关查询")
    logger.info("=" * 60)
    
    try:
        db_manager = DatabaseManager()
        
        with db_manager.neo4j.get_session() as session:
            # 查询航线节点
            logger.info("查询航线节点...")
            result = session.run("""
                MATCH (sr:ShippingRoute)
                OPTIONAL MATCH (sr)-[:ROUTE_ORIGIN]->(pO:Port)
                OPTIONAL MATCH (sr)-[:ROUTE_DESTINATION]->(pD:Port)
                RETURN sr.routeName, pO.name as origin, pD.name as destination, sr.distance_km
                ORDER BY sr.distance_km DESC
                LIMIT 10
            """)
            
            routes = []
            for record in result:
                routes.append({
                    "route_name": record["sr.routeName"],
                    "origin": record["origin"],
                    "destination": record["destination"],
                    "distance_km": record["sr.distance_km"]
                })
            
            logger.info(f"找到 {len(routes)} 条航线:")
            for route in routes:
                logger.info(f"  - {route['route_name']}: {route['origin']} -> {route['destination']} ({route['distance_km']}km)")
            
            # 查询航线月度统计
            logger.info("\n查询航线月度统计...")
            result = session.run("""
                MATCH (rms:RouteMonthStat)-[:ROUTE_STAT_FOR_ROUTE]->(sr:ShippingRoute)
                RETURN sr.routeName, rms.totalShipCount, rms.totalCargo_ton
                ORDER BY rms.totalCargo_ton DESC
                LIMIT 5
            """)
            
            stats = []
            for record in result:
                stats.append({
                    "route_name": record["sr.routeName"],
                    "ship_count": record["rms.totalShipCount"],
                    "cargo_ton": record["rms.totalCargo_ton"]
                })
            
            logger.info(f"找到 {len(stats)} 条航线统计:")
            for stat in stats:
                logger.info(f"  - {stat['route_name']}: {stat['ship_count']}艘船, {stat['cargo_ton']}吨货物")
            
            return {
                "routes": routes,
                "statistics": stats
            }
            
    except Exception as e:
        logger.error(f"查询测试失败: {e}")
        return {"error": str(e)}


if __name__ == "__main__":
    # 运行测试
    test_result = test_route_etl()
    
    # 运行查询测试
    query_result = test_route_queries()
    
    logger.info("\n" + "=" * 60)
    logger.info("测试完成")
    logger.info("=" * 60)
    
    if test_result.get("success", False):
        logger.info("🎉 航线ETL测试成功完成！")
    else:
        logger.error("❌ 航线ETL测试失败")
        if "error" in test_result:
            logger.error(f"错误信息: {test_result['error']}")
