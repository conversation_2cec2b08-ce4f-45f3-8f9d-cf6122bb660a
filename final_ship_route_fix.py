#!/usr/bin/env python3
"""
最终的船舶航线查询修复方案
基于诊断结果提供完整的解决方案
"""

import sys
import os

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from kgnode.database import DatabaseManager

def test_final_corrected_query():
    """测试最终修正后的查询"""
    print("=== 最终修正后的船舶航线查询测试 ===")
    
    db_manager = DatabaseManager()
    
    try:
        with db_manager.neo4j.get_session() as session:
            # 最终修正的查询逻辑
            print("1. 测试最终修正的查询...")
            result = session.run("""
                MATCH (s:Ship)<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
                WHERE EXISTS {
                    MATCH (s)<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)
                    -[:LINE_STAT_FOR_MONTH]->(ym_line:YearMonth {ym: $period})
                    MATCH (smls)-[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
                    WHERE ANY(route IN $active_routes 
                        WHERE sr.routeName CONTAINS route 
                           OR sr.routeName = route + '航线')
                }
                RETURN s.name as name, s.mmsi as mmsi, 
                       COALESCE(s.dwt, 0) as dwt, 
                       COALESCE(s.regPortProvince, '未知') as province,
                       COALESCE(sms.cargo_ton, 0) as cargo_volume, 
                       COALESCE(sms.voyages, 0) as voyage_count, 
                       COALESCE(sms.loadRatio, 0) as load_ratio
                ORDER BY sms.cargo_ton DESC, sms.voyages DESC, sms.loadRatio DESC
                LIMIT $limit
            """, period='202506', active_routes=['九江-武汉'], limit=10)
            
            ships = []
            for record in result:
                ships.append({
                    'name': record['name'],
                    'mmsi': record['mmsi'],
                    'dwt': record['dwt'],
                    'province': record['province'],
                    'cargo_volume': record['cargo_volume'],
                    'voyage_count': record['voyage_count'],
                    'load_ratio': record['load_ratio']
                })
            
            print(f"   ✅ 找到 {len(ships)} 艘船:")
            for i, ship in enumerate(ships, 1):
                print(f"     {i}. {ship['name']} (MMSI: {ship['mmsi']})")
                print(f"        载重吨: {ship['dwt']}, 货运量: {ship['cargo_volume']:.1f}吨")
                print(f"        航次数: {ship['voyage_count']}, 装载率: {ship['load_ratio']:.2f}")
                print(f"        注册省份: {ship['province']}")
                print()
            
            return len(ships) > 0
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        db_manager.close_all()

def provide_go_fix():
    """提供Go代码的具体修复方案"""
    print("=== Go代码修复方案 ===")
    
    print("🔧 **问题分析**:")
    print("1. 航线名称不匹配: 查询用'九江-武汉'，数据库中是'九江-武汉航线'")
    print("2. 查询逻辑错误: 关系链接有问题")
    print()
    
    print("🛠️ **修复方案**:")
    print()
    print("**原始查询 (有问题):**")
    print("""
WHERE EXISTS {
    MATCH (s)<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)
    -[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
    -[:LINE_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
    WHERE sr.routeName IN $active_routes
}
""")
    
    print("**修正后的查询:**")
    print("""
WHERE EXISTS {
    MATCH (s)<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)
    -[:LINE_STAT_FOR_MONTH]->(ym_line:YearMonth {ym: $period})
    MATCH (smls)-[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
    WHERE ANY(route IN $active_routes 
        WHERE sr.routeName CONTAINS route 
           OR sr.routeName = route + '航线')
}
""")
    
    print("**关键修改点:**")
    print("1. ✅ 分离了关系链接: 先连接到YearMonth，再连接到ShippingRoute")
    print("2. ✅ 使用ANY()函数支持模糊匹配")
    print("3. ✅ 自动处理'航线'后缀问题")
    print()
    
    print("**完整的Go查询代码:**")
    go_code = '''
query := `
    MATCH (s:Ship)<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
    WHERE EXISTS {
        MATCH (s)<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)
        -[:LINE_STAT_FOR_MONTH]->(ym_line:YearMonth {ym: $period})
        MATCH (smls)-[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
        WHERE ANY(route IN $active_routes 
            WHERE sr.routeName CONTAINS route 
               OR sr.routeName = route + '航线')
    }
    RETURN s.name as name, s.mmsi as mmsi, 
           COALESCE(s.dwt, 0) as dwt, 
           COALESCE(s.regPortProvince, '') as province,
           COALESCE(sms.cargo_ton, 0) as cargo_volume, 
           COALESCE(sms.voyages, 0) as voyage_count, 
           COALESCE(sms.loadRatio, 0) as load_ratio
    ORDER BY sms.cargo_ton DESC, sms.voyages DESC, sms.loadRatio DESC
    LIMIT $limit
`

// 参数处理也需要调整
params := map[string]interface{}{
    "period":        period,
    "active_routes": active_routes,  // 保持原样，如 ["九江-武汉"]
    "limit":         limit,
}
'''
    print(go_code)

def provide_api_improvements():
    """提供API改进建议"""
    print("\n=== API改进建议 ===")
    
    print("🚀 **建议的API增强功能:**")
    print()
    print("1. **智能航线名称匹配**")
    print("   - 输入: '九江-武汉' 自动匹配 '九江-武汉航线'")
    print("   - 支持双向匹配: '九江-武汉' 和 '武汉-九江'")
    print("   - 支持别名和简称")
    print()
    
    print("2. **增强的查询参数**")
    enhanced_params = '''
{
    "entity_type": "Ship",
    "time_period": "202506",
    "filters": {
        "active_routes": {
            "operator": "contains_any",
            "values": ["九江-武汉"],
            "fuzzy_match": true,          // 新增: 支持模糊匹配
            "bidirectional": true         // 新增: 支持双向匹配
        },
        "performance_metrics": {
            "min_cargo_volume": 1000,
            "min_voyage_count": 1
        }
    },
    "options": {
        "include_route_details": true,    // 新增: 包含航线详情
        "sort_by": "cargo_volume"         // 新增: 排序选项
    }
}
'''
    print("   增强的请求参数:")
    print(enhanced_params)
    
    print("3. **改进的响应格式**")
    enhanced_response = '''
{
    "entity_type": "Ship",
    "time_period": "202506",
    "results": [
        {
            "name": "汉海5号",
            "mmsi": "413256960",
            "dwt": 5000,
            "province": "湖北省",
            "cargo_volume": 4500.5,
            "voyage_count": 3,
            "load_ratio": 0.85,
            "route_details": [              // 新增: 航线详情
                {
                    "route_name": "九江-武汉航线",
                    "cargo_volume": 4500.5,
                    "voyage_count": 3,
                    "avg_load_ratio": 0.85
                }
            ]
        }
    ],
    "total_found": 1,
    "returned": 1,
    "execution_time_ms": 15,
    "query_metadata": {                   // 新增: 查询元数据
        "matched_routes": ["九江-武汉航线"],
        "fuzzy_matches": true,
        "bidirectional_search": true
    }
}
'''
    print("   增强的响应格式:")
    print(enhanced_response)

def main():
    """主函数"""
    print("🔧 船舶航线查询问题 - 最终修复方案\n")
    
    # 测试修正后的查询
    success = test_final_corrected_query()
    
    # 提供Go代码修复方案
    provide_go_fix()
    
    # 提供API改进建议
    provide_api_improvements()
    
    print(f"\n=== 修复总结 ===")
    if success:
        print("🎉 **修复成功！**")
        print()
        print("✅ **已解决的问题:**")
        print("1. 航线名称不匹配问题 ('九江-武汉' vs '九江-武汉航线')")
        print("2. 查询逻辑中的关系链接错误")
        print("3. 空值处理问题 (使用COALESCE)")
        print()
        print("🚀 **提供的解决方案:**")
        print("1. 修正的Cypher查询逻辑")
        print("2. 完整的Go代码修复方案")
        print("3. API增强功能建议")
        print("4. 智能航线名称匹配机制")
        print()
        print("📋 **下一步行动:**")
        print("1. 将修正的查询逻辑应用到Go代码中")
        print("2. 测试各种航线名称组合")
        print("3. 考虑实现API增强功能")
        print("4. 添加查询性能监控")
        
        return True
    else:
        print("❌ **修复失败**")
        print("需要进一步检查数据完整性")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
