
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent))

import uvicorn
from loguru import logger
from src.agent.server import app
from src.config import settings


def main():
    """启动API服务"""
    logger.info("🚀 正在启动CJHY-KGAgent API服务...")
    
    # 打印启动信息
    print("=" * 60)
    print("🚢 长江航运智能指标问答Agent API服务")
    print("   基于LangServe提供HTTP RESTful接口")
    print("=" * 60)
    print(f"📍 服务地址: http://localhost:{settings.app.api_port}")
    print(f"📖 API文档: http://localhost:{settings.app.api_port}/docs")
    print(f"🎮 演示页面: http://localhost:{settings.app.api_port}/")
    print(f"🏥 健康检查: http://localhost:{settings.app.api_port}/health")
    print("=" * 60)
    print("📌 主要接口:")
    print(f"   POST /agent/invoke    - 同步问答")
    print(f"   POST /agent/stream    - 流式问答")
    print(f"   POST /agent/batch     - 批量问答")
    print("=" * 60)
    print("💡 使用示例:")
    print("   curl -X POST http://localhost:8000/agent/invoke \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -d '{\"input\": {\"question\": \"南京港的基本信息\"}}'")
    print("=" * 60)
    
    try:
        # 启动服务
        uvicorn.run(
            app,
            host=settings.app.api_host,
            port=settings.app.api_port,
            reload=True if os.getenv('ENVIRONMENT') == 'development' else False,
            log_level="info",
            access_log=True
        )
    except KeyboardInterrupt:
        logger.info("👋 API服务已停止")
    except Exception as e:
        logger.error(f"❌ API服务启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 