"""
港口动态节点ETL模块
用于处理港口月度统计和港口分货类月度统计数据的ETL流程
"""

import pandas as pd
from loguru import logger
from typing import Dict, List, Optional, Set, Any
from datetime import datetime
import os
from concurrent.futures import ThreadPoolExecutor

from .database import DatabaseManager, Neo4jClient, OracleClient


class PortDynamicETL:
    """港口动态节点ETL管理器"""
    
    def __init__(self, database_manager: DatabaseManager = None, batch_size: int = 100):
        """
        初始化港口动态ETL管理器
        
        Args:
            database_manager: 数据库管理器实例
            batch_size: 批处理大小
        """
        self.batch_size = batch_size
        self.processed_count = 0
        self.error_count = 0
        
        if not database_manager:
            raise ValueError("必须提供database_manager实例")
        self.db_manager = database_manager

    def close(self):
        """关闭数据库连接"""
        if self.db_manager:
            self.db_manager.close_all()
            logger.info("PortDynamicETL database connections closed")

    def get_all_ports(self) -> List[str]:
        """
        从Neo4j静态节点获取所有港口名称

        Returns:
            List[str]: 港口名称列表
        """
        try:
            with self.db_manager.neo4j.get_session() as session:
                # 从Neo4j Port节点获取所有港口名称
                result = session.run("""
                    MATCH (port:Port)
                    WHERE port.name IN ["武汉", "南京"]
                    RETURN port.name as port_name
                    
                    ORDER BY port.name
                """)

                ports = [record["port_name"] for record in result if record["port_name"]]
                logger.info(f"从Neo4j静态节点获取到 {len(ports)} 个港口: {ports[:10]}{'...' if len(ports) > 10 else ''}")

                return ports

        except Exception as e:
            logger.error(f"从Neo4j获取港口列表失败: {e}")
            return []
    
    def validate_dependencies(self) -> Dict[str, bool]:
        """
        验证静态节点依赖是否满足
        
        Returns:
            Dict: 各类静态节点的存在状态
        """
        dependencies = {
            'ports': False,
            'cargo_types': False,
            'years': False
        }
        
        with self.db_manager.neo4j.get_session() as session:
            # 检查Port节点
            result = session.run("MATCH (p:Port) RETURN count(p) as count")
            port_count = result.single()['count']
            dependencies['ports'] = port_count > 0
            logger.info(f"发现 {port_count} 个Port节点")
            
            # 检查CargoType节点
            result = session.run("MATCH (ct:CargoType) RETURN count(ct) as count")
            cargo_type_count = result.single()['count']
            dependencies['cargo_types'] = cargo_type_count > 0
            logger.info(f"发现 {cargo_type_count} 个CargoType节点")
            
            # 检查Year节点
            result = session.run("MATCH (y:Year) RETURN count(y) as count")
            year_count = result.single()['count']
            dependencies['years'] = year_count > 0
            logger.info(f"发现 {year_count} 个Year节点")
        
        return dependencies
    
    
    def get_missing_ports(self, port_names: Set[str]) -> Set[str]:
        """
        检查缺失的港口节点
        
        Args:
            port_names: 需要检查的港口名称集合
            
        Returns:
            Set[str]: 缺失的港口名称
        """
        existing_ports = set()
        
        with self.db_manager.neo4j.get_session() as session:
            result = session.run("""
                MATCH (p:Port)
                WHERE p.name IN $port_names
                RETURN p.name as name
            """, port_names=list(port_names))
            
            existing_ports = {record['name'] for record in result}
        
        missing_ports = port_names - existing_ports
        if missing_ports:
            logger.warning(f"缺失的港口节点: {missing_ports}")
        
        return missing_ports
    
    def get_missing_cargo_types(self, cargo_types: Set[str]) -> Set[str]:
        """
        检查缺失的货物类型节点
        
        Args:
            cargo_types: 需要检查的货物类型名称集合
            
        Returns:
            Set[str]: 缺失的货物类型名称
        """
        existing_cargo_types = set()
        
        with self.db_manager.neo4j.get_session() as session:
            result = session.run("""
                MATCH (ct:CargoType)
                WHERE ct.subName IN $cargo_types
                RETURN ct.subName as subName
            """, cargo_types=list(cargo_types))
            
            existing_cargo_types = {record['subName'] for record in result}
        
        missing_types = cargo_types - existing_cargo_types
        if missing_types:
            logger.warning(f"缺失的货物类型节点: {missing_types}")
        
        return missing_types
    
    def load_port_stats(self, port_name: str = None, time_filter: str = "ym like '2024%'") -> Dict[str, int]:
        """
        从Oracle数据库加载港口月度统计数据

        Args:
            port_name: 港口名称，如果为None则加载所有港口
            time_filter: 时间过滤条件，默认为2024年数据

        Returns:
            Dict: 加载结果统计
        """
        try:
            # 如果没有指定港口，获取所有港口
            if port_name is None:
                ports = self.get_all_ports()
                if not ports:
                    logger.warning("未找到任何港口，跳过港口统计加载")
                    return {"created": 0, "errors": 0}

                # 递归调用，为每个港口加载数据
                total_created = 0
                total_errors = 0

                for port in ports:
                    logger.info(f"加载港口统计数据: {port}")
                    result = self.load_port_stats(port, time_filter)
                    total_created += result.get("created", 0)
                    total_errors += result.get("errors", 0)

                logger.info(f"所有港口统计加载完成: 创建 {total_created}, 错误 {total_errors}")
                return {"created": total_created, "errors": total_errors}

            # 为指定港口构建SQL查询
            sql = f"""
            select a.ym,a.merge_city_name portName,a.ShipCount inShipCount,b.ShipCount outShipCount,a.LoadRatio inLoadRatio,b.LoadRatio outLoadRatio,c.cargo inCargo_tun,d.cargo outCargo_tun,e.anchorTime_days from(
            select ym,merge_city_name,count(1) ShipCount,round(avg(t.actual_carrying_capacity/t.ship_dwt),2) LoadRatio from yssjtj.dwd_ship_eep_report_i_d t where t.merge_city_name='{port_name}' and t.ship_dwt>0 and t.arrival_or_leave=0 and {time_filter} group by ym,merge_city_name
            ) a join (
            select ym,merge_city_name,count(1) ShipCount,round(avg(t.actual_carrying_capacity/t.ship_dwt),2) LoadRatio from yssjtj.dwd_ship_eep_report_i_d t where t.merge_city_name='{port_name}' and t.ship_dwt>0 and t.arrival_or_leave=1 and {time_filter} group by ym,merge_city_name
            ) b on a.ym=b.ym and a.merge_city_name=b.merge_city_name
            join (
            select ym,merge_city_name_d merge_city_name,sum(capacity) cargo from yssjtj.od_dwd_statistics t where t.merge_city_name_d='{port_name}' and {time_filter} group by ym,merge_city_name_d
            ) c on a.ym=c.ym and a.merge_city_name=c.merge_city_name
            join (
            select ym,merge_city_name_o merge_city_name,sum(capacity) cargo from yssjtj.od_dwd_statistics t where t.merge_city_name_o='{port_name}' and {time_filter} group by ym,merge_city_name_o
            ) d on a.ym=d.ym and a.merge_city_name=d.merge_city_name
            join (
            select ym,merge_city_name_o merge_city_name,round(avg(t.anchor_time)/1440,1) anchorTime_days from yssjtj.dwd_ship_eep_report_iod_i_d t where t.merge_city_name_o='{port_name}' and {time_filter} group by ym,merge_city_name_o
            ) e on a.ym=e.ym and a.merge_city_name=e.merge_city_name
            """
            
            logger.info(f"从Oracle执行查询: {sql}")
            
            # 使用database_manager中的Oracle客户端读取数据
            df = self.db_manager.oracle.execute_query_to_dataframe(sql)
            logger.info(f"从Oracle加载了{len(df)}条港口月度统计记录")
            
            if df.empty:
                logger.warning("从Oracle查询到的数据为空")
                return {"created": 0, "errors": 0}
            
            # 收集年月和港口名称用于依赖检查（统一列名大小写）
            year_months = set(df['YM'].astype(str) if 'YM' in df.columns else df['ym'].astype(str))
            port_names = set(df['PORTNAME'] if 'PORTNAME' in df.columns else df['portName'])
            
            # 创建YearMonth节点
            # self.create_yearmonth_nodes(year_months)
            
            # 检查港口依赖
            missing_ports = self.get_missing_ports(port_names)
            if missing_ports:
                logger.error(f"无法处理数据，缺失港口节点: {missing_ports}")
                return {"created": 0, "errors": len(df)}
            
            created_count = 0
            error_count = 0
            
            with self.db_manager.neo4j.get_session() as session:
                for _, row in df.iterrows():
                    try:
                        # 创建PortMonthStat节点并建立关系
                        session.run("""
                            MATCH (p:Port {name: $port_name})
                            MATCH (ym:YearMonth {ym: $ym})
                            
                            MERGE (pms:PortMonthStat {
                                port_ym_key: $port_name + '_' + $ym
                            })
                            SET pms.inShipCount = $inShipCount,
                                pms.outShipCount = $outShipCount,
                                pms.inCargo_ton = $inCargo_ton,
                                pms.outCargo_ton = $outCargo_ton,
                                pms.anchorTime_days = $anchorTime_days,
                                pms.inLoadRatio = $inLoadRatio,
                                pms.outLoadRatio = $outLoadRatio,
                                pms.lastUpdated = datetime()
                            
                            MERGE (pms)-[:STAT_FOR_PORT]->(p)
                            MERGE (pms)-[:STAT_FOR_MONTH]->(ym)
                        """,
                        port_name=str(row['PORTNAME'] if 'PORTNAME' in row else row['portName']),
                        ym=str(row['YM'] if 'YM' in row else row['ym']),
                        inShipCount=int(row['INSHIPCOUNT'] if 'INSHIPCOUNT' in row else row['inShipCount']) if pd.notna(row.get('INSHIPCOUNT', row.get('inShipCount'))) else 0,
                        outShipCount=int(row['OUTSHIPCOUNT'] if 'OUTSHIPCOUNT' in row else row['outShipCount']) if pd.notna(row.get('OUTSHIPCOUNT', row.get('outShipCount'))) else 0,
                        inCargo_ton=float(row['INCARGO_TUN'] if 'INCARGO_TUN' in row else row['inCargo_ton']) if pd.notna(row.get('INCARGO_TUN', row.get('inCargo_ton'))) else 0.0,
                        outCargo_ton=float(row['OUTCARGO_TUN'] if 'OUTCARGO_TUN' in row else row['outCargo_ton']) if pd.notna(row.get('OUTCARGO_TUN', row.get('outCargo_ton'))) else 0.0,
                        anchorTime_days=float(row['ANCHORTIME_DAYS'] if 'ANCHORTIME_DAYS' in row else row['anchorTime_days']) if pd.notna(row.get('ANCHORTIME_DAYS', row.get('anchorTime_days'))) else 0.0,
                        inLoadRatio=float(row['INLOADRATIO'] if 'INLOADRATIO' in row else row['inLoadRatio']) if pd.notna(row.get('INLOADRATIO', row.get('inLoadRatio'))) else 0.0,
                        outLoadRatio=float(row['OUTLOADRATIO'] if 'OUTLOADRATIO' in row else row['outLoadRatio']) if pd.notna(row.get('OUTLOADRATIO', row.get('outLoadRatio'))) else 0.0)
                        
                        created_count += 1
                        port_name_log = row['PORTNAME'] if 'PORTNAME' in row else row['portName']
                        ym_log = row['YM'] if 'YM' in row else row['ym']
                        logger.debug(f"创建港口月度统计: {port_name_log} - {ym_log}")
                        
                    except Exception as e:
                        error_count += 1
                        port_name_log = row['PORTNAME'] if 'PORTNAME' in row else row['portName']
                        ym_log = row['YM'] if 'YM' in row else row['ym']
                        logger.error(f"创建港口月度统计失败: {port_name_log} - {ym_log}, 错误: {e}")
            
            logger.info(f"港口月度统计加载完成: 成功 {created_count}, 失败 {error_count}")
            return {"created": created_count, "errors": error_count}
            
        except Exception as e:
            logger.error(f"从Oracle加载港口月度统计失败: {e}")
            return {"created": 0, "errors": 1}
    
    def load_port_cargo_stats(self, port_name: str = None, time_filter: str = "ym like '2024%'") -> Dict[str, int]:
        """
        从Oracle数据库加载港口分货类月度统计数据

        Args:
            port_name: 港口名称，如果为None则加载所有港口
            time_filter: 时间过滤条件，默认为2024年数据

        Returns:
            Dict: 加载结果统计
        """
        try:
            # # 如果没有指定港口，获取所有港口
            if port_name is None:
                ports = self.get_all_ports()
                if not ports:
                    logger.warning("未找到任何港口，跳过港口分货类统计加载")
                    return {"created": 0, "errors": 0}

                # 递归调用，为每个港口加载数据
                total_created = 0
                total_errors = 0

                for port in ports:
                    logger.info(f"加载港口分货类统计数据: {port}")
                    result = self.load_port_cargo_stats(port, time_filter)
                    total_created += result.get("created", 0)
                    total_errors += result.get("errors", 0)

                logger.info(f"所有港口分货类统计加载完成: 创建 {total_created}, 错误 {total_errors}")
                return {"created": total_created, "errors": total_errors}

            # 为指定港口构建SQL查询
            sql = f"""
                select a.ym,a.city_name portName,a.cargo_name cargoType,a.c inCargo_tun,b.c outCargo_tun from (
                select ym,merge_city_name_d city_name,t.cargo_name,sum(capacity) c from yssjtj.od_dwd_statistics t where t.merge_city_name_d='{port_name}' and {time_filter} and flag_trade='01' group by ym,t.cargo_name,merge_city_name_d order by ym,cargo_name
                ) a  join (
                select ym,merge_city_name_o city_name,t.cargo_name,sum(capacity) c from yssjtj.od_dwd_statistics t where t.merge_city_name_o='{port_name}' and {time_filter} and flag_trade='01' group by ym,merge_city_name_o,t.cargo_name order by ym,cargo_name
                ) b on a.ym=b.ym and a.cargo_name=b.cargo_name
            """
            
            logger.info(f"从Oracle执行查询: {sql}")
            
            # 使用database_manager中的Oracle客户端读取数据
            df = self.db_manager.oracle.execute_query_to_dataframe(sql)
            logger.info(f"从Oracle加载了{len(df)}条港口货类月度统计记录")
            
            if df.empty:
                logger.warning("从Oracle查询到的数据为空")
                return {"created": 0, "errors": 0}
            
            # 收集数据用于依赖检查（统一列名大小写）
            year_months = set(df['YM'].astype(str) if 'YM' in df.columns else df['ym'].astype(str))
            port_names = set(df['PORTNAME'] if 'PORTNAME' in df.columns else df['portName'])
            cargo_types = set(df['CARGOTYPE'] if 'CARGOTYPE' in df.columns else df['cargoType'])
            
            
            # 检查依赖
            missing_ports = self.get_missing_ports(port_names)
            missing_cargo_types = self.get_missing_cargo_types(cargo_types)
            
            if missing_ports or missing_cargo_types:
                logger.error(f"无法处理数据，缺失节点 - 港口: {missing_ports}, 货类: {missing_cargo_types}")
                return {"created": 0, "errors": len(df)}
            
            created_count = 0
            error_count = 0
            
            with self.db_manager.neo4j.get_session() as session:
                for _, row in df.iterrows():
                    try:
                        # 创建PortMonthCargoStat节点并建立关系
                        session.run("""
                            MATCH (p:Port {name: $port_name})
                            MATCH (ym:YearMonth {ym: $ym})
                            MATCH (ct:CargoType {subName: $cargo_type})
                            
                            MERGE (pmcs:PortMonthCargoStat {
                                port_ym_cargo_key: $port_name + '_' + $ym + '_' + $cargo_type
                            })
                            SET pmcs.inCargo_ton = $inCargo_ton,
                                pmcs.outCargo_ton = $outCargo_ton,
                                pmcs.lastUpdated = datetime()
                            
                            MERGE (pmcs)-[:PORT_CARGO_STAT_FOR_PORT]->(p)
                            MERGE (pmcs)-[:PORT_CARGO_STAT_FOR_MONTH]->(ym)
                            MERGE (pmcs)-[:PORT_CARGO_STAT_FOR_TYPE]->(ct)
                        """,
                        port_name=str(row['PORTNAME'] if 'PORTNAME' in row else row['portName']),
                        ym=str(row['YM'] if 'YM' in row else row['ym']),
                        cargo_type=str(row['CARGOTYPE'] if 'CARGOTYPE' in row else row['cargoType']),
                        inCargo_ton=float(row['INCARGO_TUN'] if 'INCARGO_TUN' in row else row['inCargo_ton']) if pd.notna(row.get('INCARGO_TUN', row.get('inCargo_ton'))) else 0.0,
                        outCargo_ton=float(row['OUTCARGO_TUN'] if 'OUTCARGO_TUN' in row else row['outCargo_ton']) if pd.notna(row.get('OUTCARGO_TUN', row.get('outCargo_ton'))) else 0.0)
                        
                        
                        created_count += 1
                        port_name_log = row['PORTNAME'] if 'PORTNAME' in row else row['portName']
                        ym_log = row['YM'] if 'YM' in row else row['ym']
                        cargo_type_log = row['CARGOTYPE'] if 'CARGOTYPE' in row else row['cargoType']
                        # logger.debug(f"创建港口货类统计: {port_name_log} - {ym_log} - {cargo_type_log}")
                        
                    except Exception as e:
                        error_count += 1
                        port_name_log = row['PORTNAME'] if 'PORTNAME' in row else row['portName']
                        ym_log = row['YM'] if 'YM' in row else row['ym']
                        cargo_type_log = row['CARGOTYPE'] if 'CARGOTYPE' in row else row['cargoType']
                        logger.error(f"创建港口货类统计失败: {port_name_log} - {ym_log} - {cargo_type_log}, 错误: {e}")
            
            logger.info(f"港口货类统计加载完成: 成功 {created_count}, 失败 {error_count}")
            return {"created": created_count, "errors": error_count}
            
        except Exception as e:
            logger.error(f"从Oracle加载港口货类统计失败: {e}")
            return {"created": 0, "errors": 1}
    
    def execute_etl(self, port_name: str = None, time_filter: str = "ym like '2024%'",limit:int=10000) -> Dict[str, Any]:
        """
        执行完整的港口动态节点ETL流程

        Args:
            port_name: 港口名称，如果为None则加载所有港口
            time_filter: 时间过滤条件，默认为2024年数据

        Returns:
            Dict: ETL执行结果
        """
        start_time = datetime.now()
        logger.info("开始执行港口动态节点ETL流程")
        
        try:
            # 验证静态依赖
            dependencies = self.validate_dependencies()
            if not all(dependencies.values()):
                missing = [k for k, v in dependencies.items() if not v]
                logger.error(f"静态节点依赖不满足: {missing}")
                return {
                    "success": False,
                    "error": f"缺失静态节点: {missing}",
                    "execution_time": (datetime.now() - start_time).total_seconds()
                }
            
            # 加载港口月度统计
            port_result = self.load_port_stats(port_name, time_filter)

            # 加载港口货类统计
            cargo_result = self.load_port_cargo_stats(port_name, time_filter)
            
            # 汇总结果
            total_created = port_result["created"] + cargo_result["created"]
            total_errors = port_result["errors"] + cargo_result["errors"]
            
            result = {
                "success": total_errors == 0,
                "port_stats": port_result,
                "cargo_stats": cargo_result,
                "total_created": total_created,
                "total_errors": total_errors,
                "execution_time": (datetime.now() - start_time).total_seconds(),
                "completion_time": datetime.now()
            }
            
            if result["success"]:
                logger.info(f"港口ETL流程成功完成: 创建 {total_created} 个节点，耗时 {result['execution_time']:.2f} 秒")
            else:
                logger.error(f"港口ETL流程部分失败: 创建 {total_created} 个节点，错误 {total_errors} 个")
            
            return result
            
        except Exception as e:
            logger.error(f"港口ETL流程执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_time": (datetime.now() - start_time).total_seconds(),
                "completion_time": datetime.now()
            }
    
    def get_statistics(self) -> Dict[str, int]:
        """
        获取港口动态节点统计信息
        
        Returns:
            Dict: 统计信息
        """
        stats = {}
        
        with self.db_manager.neo4j.get_session() as session:
            # YearMonth节点数量
            result = session.run("MATCH (n:YearMonth) RETURN count(n) as count")
            stats['yearmonths'] = result.single()['count']
            
            # PortMonthStat节点数量
            result = session.run("MATCH (n:PortMonthStat) RETURN count(n) as count")
            stats['port_stats'] = result.single()['count']
            
            # PortMonthCargoStat节点数量
            result = session.run("MATCH (n:PortMonthCargoStat) RETURN count(n) as count")
            stats['cargo_stats'] = result.single()['count']
            
            # 总动态节点数
            stats['total_nodes'] = (
                stats['yearmonths'] + 
                stats['port_stats'] + 
                stats['cargo_stats']
            )
            
            logger.info(f"港口动态节点统计: {stats}")
        
        return stats
    
    def get_port_summary(self) -> List[Dict]:
        """
        获取港口统计摘要信息
        
        Returns:
            List[Dict]: 港口统计摘要
        """
        with self.db_manager.neo4j.get_session() as session:
            result = session.run("""
                MATCH (p:Port)<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth)
                RETURN p.name as port_name,
                       count(pms) as month_count,
                       sum(pms.inCargo_ton) as total_in_cargo,
                       sum(pms.outCargo_ton) as total_out_cargo,
                       avg(pms.inLoadRatio) as avg_in_load_ratio,
                       avg(pms.outLoadRatio) as avg_out_load_ratio
                ORDER BY total_in_cargo DESC
            """)
            
            summary = []
            for record in result:
                summary.append({
                    'port_name': record['port_name'],
                    'month_count': record['month_count'],
                    'total_in_cargo': record['total_in_cargo'],
                    'total_out_cargo': record['total_out_cargo'],
                    'avg_in_load_ratio': record['avg_in_load_ratio'],
                    'avg_out_load_ratio': record['avg_out_load_ratio']
                })
            
            logger.info(f"获取了{len(summary)}个港口的统计摘要")
            return summary

    def get_port_related_routes(self, port_name: str, period: str = None) -> Dict[str, Any]:
        """
        查询港口关联的航线信息

        Args:
            port_name: 港口名称
            period: 可选的时间期间，格式如"202407"

        Returns:
            Dict: 包含出发航线和到达航线的信息
        """
        try:
            with self.db_manager.neo4j.get_session() as session:
                # 查询港口作为起点的航线
                outbound_query = """
                    MATCH (p:Port {name: $port_name})
                    MATCH (p)<-[:ROUTE_ORIGIN]-(sr:ShippingRoute)-[:ROUTE_DESTINATION]->(dest:Port)
                    OPTIONAL MATCH (sr)<-[:ROUTE_STAT_FOR_ROUTE]-(rms:RouteMonthStat)-[:ROUTE_STAT_FOR_MONTH]->(ym:YearMonth)
                    WHERE $period IS NULL OR ym.ym = $period
                    WITH sr, dest,
                         COALESCE(sum(rms.totalShipCount), 0) as total_ships,
                         COALESCE(sum(rms.totalCargo_ton), 0) as total_cargo,
                         COALESCE(avg(rms.avgLoadRatio), 0) as avg_load_ratio
                    RETURN sr.routeId as route_id,
                           sr.routeName as route_name,
                           sr.routeCode as route_code,
                           dest.name as destination_port,
                           sr.distance_km as distance_km,
                           total_ships,
                           total_cargo,
                           avg_load_ratio,
                           'outbound' as direction
                    ORDER BY total_cargo DESC
                """

                # 查询港口作为终点的航线
                inbound_query = """
                    MATCH (p:Port {name: $port_name})
                    MATCH (p)<-[:ROUTE_DESTINATION]-(sr:ShippingRoute)-[:ROUTE_ORIGIN]->(origin:Port)
                    OPTIONAL MATCH (sr)<-[:ROUTE_STAT_FOR_ROUTE]-(rms:RouteMonthStat)-[:ROUTE_STAT_FOR_MONTH]->(ym:YearMonth)
                    WHERE $period IS NULL OR ym.ym = $period
                    WITH sr, origin,
                         COALESCE(sum(rms.totalShipCount), 0) as total_ships,
                         COALESCE(sum(rms.totalCargo_ton), 0) as total_cargo,
                         COALESCE(avg(rms.avgLoadRatio), 0) as avg_load_ratio
                    RETURN sr.routeId as route_id,
                           sr.routeName as route_name,
                           sr.routeCode as route_code,
                           origin.name as origin_port,
                           sr.distance_km as distance_km,
                           total_ships,
                           total_cargo,
                           avg_load_ratio,
                           'inbound' as direction
                    ORDER BY total_cargo DESC
                """

                # 执行查询
                outbound_result = session.run(outbound_query, {"port_name": port_name, "period": period})
                inbound_result = session.run(inbound_query, {"port_name": port_name, "period": period})

                outbound_routes = []
                for record in outbound_result:
                    outbound_routes.append({
                        "route_id": record["route_id"],
                        "route_name": record["route_name"],
                        "route_code": record["route_code"],
                        "destination_port": record["destination_port"],
                        "distance_km": record["distance_km"],
                        "total_ships": record["total_ships"],
                        "total_cargo": record["total_cargo"],
                        "avg_load_ratio": record["avg_load_ratio"],
                        "direction": record["direction"]
                    })

                inbound_routes = []
                for record in inbound_result:
                    inbound_routes.append({
                        "route_id": record["route_id"],
                        "route_name": record["route_name"],
                        "route_code": record["route_code"],
                        "origin_port": record["origin_port"],
                        "distance_km": record["distance_km"],
                        "total_ships": record["total_ships"],
                        "total_cargo": record["total_cargo"],
                        "avg_load_ratio": record["avg_load_ratio"],
                        "direction": record["direction"]
                    })

                result = {
                    "port_name": port_name,
                    "period": period,
                    "outbound_routes": outbound_routes,
                    "inbound_routes": inbound_routes,
                    "total_outbound": len(outbound_routes),
                    "total_inbound": len(inbound_routes),
                    "total_routes": len(outbound_routes) + len(inbound_routes)
                }

                logger.info(f"港口 {port_name} 关联航线查询完成: 出发 {len(outbound_routes)} 条, 到达 {len(inbound_routes)} 条")
                return result

        except Exception as e:
            logger.error(f"查询港口 {port_name} 关联航线失败: {e}")
            return {
                "port_name": port_name,
                "period": period,
                "outbound_routes": [],
                "inbound_routes": [],
                "total_outbound": 0,
                "total_inbound": 0,
                "total_routes": 0,
                "error": str(e)
            }

    def get_port_navigation_summary(self, port_name: str, period: str = None, limit: int = 10) -> Dict[str, Any]:
        """
        获取港口航行摘要信息 - 基于港口统计数据和航线关系

        Args:
            port_name: 港口名称
            period: 可选的时间期间，格式如"202407"
            limit: 返回结果数量限制

        Returns:
            Dict: 港口航行摘要信息
        """
        try:
            with self.db_manager.neo4j.get_session() as session:
                # 使用港口统计数据结合航线关系进行查询
                query = """
                    MATCH (p:Port {name: $port_name})
                    MATCH (p)<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth)
                    WHERE $period IS NULL OR ym.ym = $period

                    // 查找港口作为起点的航线
                    OPTIONAL MATCH (p)<-[:ROUTE_ORIGIN]-(sr1:ShippingRoute)-[:ROUTE_DESTINATION]->(dest:Port)
                    OPTIONAL MATCH (dest)<-[:STAT_FOR_PORT]-(destPms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym)

                    // 查找港口作为终点的航线
                    OPTIONAL MATCH (p)<-[:ROUTE_DESTINATION]-(sr2:ShippingRoute)-[:ROUTE_ORIGIN]->(origin:Port)
                    OPTIONAL MATCH (origin)<-[:STAT_FOR_PORT]-(originPms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym)

                    WITH p, ym, pms,
                         collect(DISTINCT {
                             route_id: sr1.routeId,
                             route_name: sr1.routeName,
                             destination: dest.name,
                             direction: 'outbound',
                             distance_km: sr1.distance_km,
                             out_ships: COALESCE(pms.outShipCount, 0),
                             out_cargo: COALESCE(pms.outCargo_ton, 0),
                             dest_in_ships: COALESCE(destPms.inShipCount, 0),
                             dest_in_cargo: COALESCE(destPms.inCargo_ton, 0)
                         }) as outbound_data,
                         collect(DISTINCT {
                             route_id: sr2.routeId,
                             route_name: sr2.routeName,
                             origin: origin.name,
                             direction: 'inbound',
                             distance_km: sr2.distance_km,
                             in_ships: COALESCE(pms.inShipCount, 0),
                             in_cargo: COALESCE(pms.inCargo_ton, 0),
                             origin_out_ships: COALESCE(originPms.outShipCount, 0),
                             origin_out_cargo: COALESCE(originPms.outCargo_ton, 0)
                         }) as inbound_data

                    RETURN ym.ym as period,
                           pms.inShipCount as total_in_ships,
                           pms.outShipCount as total_out_ships,
                           pms.inCargo_ton as total_in_cargo,
                           pms.outCargo_ton as total_out_cargo,
                           pms.anchorTime_days as avg_anchor_time,
                           outbound_data,
                           inbound_data
                    ORDER BY ym.ym DESC
                    LIMIT $limit
                """

                result = session.run(query, {
                    "port_name": port_name,
                    "period": period,
                    "limit": limit
                })

                navigation_data = []
                for record in result:
                    # 处理出发航线数据
                    outbound_routes = []
                    for route_data in record["outbound_data"]:
                        if route_data["route_id"]:  # 过滤空数据
                            estimated_traffic = route_data["out_ships"] + route_data["dest_in_ships"]
                            estimated_cargo = route_data["out_cargo"] + route_data["dest_in_cargo"]
                            outbound_routes.append({
                                "route_id": route_data["route_id"],
                                "route_name": route_data["route_name"],
                                "destination": route_data["destination"],
                                "distance_km": route_data["distance_km"],
                                "estimated_ship_count": estimated_traffic,
                                "estimated_cargo_volume": estimated_cargo
                            })

                    # 处理到达航线数据
                    inbound_routes = []
                    for route_data in record["inbound_data"]:
                        if route_data["route_id"]:  # 过滤空数据
                            estimated_traffic = route_data["in_ships"] + route_data["origin_out_ships"]
                            estimated_cargo = route_data["in_cargo"] + route_data["origin_out_cargo"]
                            inbound_routes.append({
                                "route_id": route_data["route_id"],
                                "route_name": route_data["route_name"],
                                "origin": route_data["origin"],
                                "distance_km": route_data["distance_km"],
                                "estimated_ship_count": estimated_traffic,
                                "estimated_cargo_volume": estimated_cargo
                            })

                    # 按货物量排序
                    outbound_routes.sort(key=lambda x: x["estimated_cargo_volume"], reverse=True)
                    inbound_routes.sort(key=lambda x: x["estimated_cargo_volume"], reverse=True)

                    navigation_data.append({
                        "period": record["period"],
                        "port_statistics": {
                            "total_in_ships": record["total_in_ships"],
                            "total_out_ships": record["total_out_ships"],
                            "total_in_cargo": record["total_in_cargo"],
                            "total_out_cargo": record["total_out_cargo"],
                            "avg_anchor_time": record["avg_anchor_time"]
                        },
                        "outbound_routes": outbound_routes[:limit],
                        "inbound_routes": inbound_routes[:limit]
                    })

                result_data = {
                    "port_name": port_name,
                    "query_period": period,
                    "navigation_data": navigation_data,
                    "summary": {
                        "periods_found": len(navigation_data),
                        "has_route_data": any(
                            len(data["outbound_routes"]) > 0 or len(data["inbound_routes"]) > 0
                            for data in navigation_data
                        )
                    }
                }

                logger.info(f"港口 {port_name} 航行摘要查询完成: 找到 {len(navigation_data)} 个时间期间的数据")
                return result_data

        except Exception as e:
            logger.error(f"查询港口 {port_name} 航行摘要失败: {e}")
            return {
                "port_name": port_name,
                "query_period": period,
                "navigation_data": [],
                "summary": {"periods_found": 0, "has_route_data": False},
                "error": str(e)
            }

    def load_port_complete_data(self, port_name: str) -> Dict[str, Any]:
        """加载港口完整数据，严格按照数据库设计文档v4.0实现"""
        logger.info(f"加载港口完整数据: {port_name}")

        port_data = {"basic_info": {}, "month_stats": [], "month_cargo_stats": [], "related_provinces": [], "related_basins": [], "related_routes": []}

        # 1. 港口基本信息 - 按设计文档Port节点属性（只有name, prov, sortNo）
        basic_query = "MATCH (p:Port {name: $port_name}) RETURN p.name as name, p.prov as prov, p.sortNo as sortNo"
        basic_result = self._execute_query(basic_query, {"port_name": port_name})
        if basic_result:
            port_data["basic_info"] = basic_result[0]

        # 2. 港口月度统计数据 - 按设计文档PortMonthStat节点属性
        month_stats_query = """
            MATCH (p:Port {name: $port_name})<-[:STAT_FOR_PORT]-(pms:PortMonthStat)
            MATCH (pms)-[:STAT_FOR_MONTH]->(ym:YearMonth)
            RETURN pms.inShipCount as inShipCount, pms.outShipCount as outShipCount,
                   pms.inCargo_ton as inCargo_ton, pms.outCargo_ton as outCargo_ton,
                   pms.anchorTime_days as anchorTime_days, pms.inLoadRatio as inLoadRatio,
                   pms.outLoadRatio as outLoadRatio, pms.lastUpdated as lastUpdated,
                   ym.ym as ym, ym.year as year, ym.month as month, ym.daysInMonth as daysInMonth
            ORDER BY ym.ym DESC
        """
        port_data["month_stats"] = self._execute_query(month_stats_query, {"port_name": port_name})

        # 3. 港口分货类月度统计 - 按设计文档PortMonthCargoStat节点属性
        month_cargo_stats_query = """
            MATCH (p:Port {name: $port_name})<-[:PORT_CARGO_STAT_FOR_PORT]-(pmcs:PortMonthCargoStat)
            MATCH (pmcs)-[:PORT_CARGO_STAT_FOR_MONTH]->(ym:YearMonth)
            MATCH (pmcs)-[:PORT_CARGO_STAT_FOR_TYPE]->(ct:CargoType)
            RETURN pmcs.inCargo_ton as inCargo_ton, pmcs.outCargo_ton as outCargo_ton,
                   pmcs.lastUpdated as lastUpdated, ym.ym as ym, ym.year as year, ym.month as month,
                   ct.subCode as cargo_subCode, ct.subName as cargo_subName, ct.name as cargo_name
            ORDER BY ym.ym DESC, ct.subCode
        """
        port_data["month_cargo_stats"] = self._execute_query(month_cargo_stats_query, {"port_name": port_name})

        # 4. 港口所在省份关系
        province_query = """
            MATCH (p:Port {name: $port_name})-[:LOCATED_IN_PROVINCE]->(prov:Province)
            RETURN prov.name as province_name, prov.code as province_code
        """
        port_data["related_provinces"] = self._execute_query(province_query, {"port_name": port_name})

        # 5. 港口所属流域关系
        basin_query = """
            MATCH (p:Port {name: $port_name})-[:LOCATED_IN_PROVINCE]->(prov:Province)-[:PART_OF_BASIN]->(b:Basin)
            RETURN b.name as basin_name
        """
        port_data["related_basins"] = self._execute_query(basin_query, {"port_name": port_name})

        # 6. 港口关联的航线信息 - 新增
        routes_query = """
            MATCH (p:Port {name: $port_name})
            OPTIONAL MATCH (p)<-[:ROUTE_ORIGIN]-(sr1:ShippingRoute)-[:ROUTE_DESTINATION]->(dest:Port)
            OPTIONAL MATCH (p)<-[:ROUTE_DESTINATION]-(sr2:ShippingRoute)-[:ROUTE_ORIGIN]->(origin:Port)
            WITH p,
                 collect(DISTINCT {
                     route_id: sr1.routeId,
                     route_name: sr1.routeName,
                     route_code: sr1.routeCode,
                     destination: dest.name,
                     distance_km: sr1.distance_km,
                     direction: 'outbound'
                 }) as outbound_routes,
                 collect(DISTINCT {
                     route_id: sr2.routeId,
                     route_name: sr2.routeName,
                     route_code: sr2.routeCode,
                     origin: origin.name,
                     distance_km: sr2.distance_km,
                     direction: 'inbound'
                 }) as inbound_routes
            RETURN outbound_routes + inbound_routes as all_routes
        """
        routes_result = self._execute_query(routes_query, {"port_name": port_name})
        if routes_result and routes_result[0]["all_routes"]:
            # 过滤掉空的航线数据
            valid_routes = [route for route in routes_result[0]["all_routes"] if route["route_id"]]
            port_data["related_routes"] = valid_routes

        logger.info(f"港口 {port_name} 完整数据加载完成")
        return port_data

    def _execute_query(self, query: str, params: Dict = None) -> List[Dict]:
        """执行Neo4j查询并返回结果列表"""
        try:
            with self.db_manager.neo4j.get_session() as session:
                result = session.run(query, params or {})
                return [dict(record) for record in result]
        except Exception as e:
            logger.error(f"执行查询失败: {e}")
            return []
