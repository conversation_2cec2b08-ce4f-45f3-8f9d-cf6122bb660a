"""
港口动态节点ETL模块
用于处理港口月度统计和港口分货类月度统计数据的ETL流程
"""

import pandas as pd
from loguru import logger
from typing import Dict, List, Optional, Set, Any
from datetime import datetime
import os
from concurrent.futures import ThreadPoolExecutor

from .database import DatabaseManager, Neo4jClient, OracleClient


class PortDynamicETL:
    """港口动态节点ETL管理器"""
    
    def __init__(self, database_manager: DatabaseManager = None, batch_size: int = 100):
        """
        初始化港口动态ETL管理器
        
        Args:
            database_manager: 数据库管理器实例
            batch_size: 批处理大小
        """
        self.batch_size = batch_size
        self.processed_count = 0
        self.error_count = 0
        
        if not database_manager:
            raise ValueError("必须提供database_manager实例")
        self.db_manager = database_manager

    def close(self):
        """关闭数据库连接"""
        if self.db_manager:
            self.db_manager.close_all()
            logger.info("PortDynamicETL database connections closed")

    def get_all_ports(self) -> List[str]:
        """
        从Neo4j静态节点获取所有港口名称

        Returns:
            List[str]: 港口名称列表
        """
        try:
            with self.db_manager.neo4j.get_session() as session:
                # 从Neo4j Port节点获取所有港口名称
                result = session.run("""
                    MATCH (port:Port)
                    RETURN port.name as port_name
                    ORDER BY port.name
                """)

                ports = [record["port_name"] for record in result if record["port_name"]]
                logger.info(f"从Neo4j静态节点获取到 {len(ports)} 个港口: {ports[:10]}{'...' if len(ports) > 10 else ''}")

                return ports

        except Exception as e:
            logger.error(f"从Neo4j获取港口列表失败: {e}")
            return []
    
    def validate_dependencies(self) -> Dict[str, bool]:
        """
        验证静态节点依赖是否满足
        
        Returns:
            Dict: 各类静态节点的存在状态
        """
        dependencies = {
            'ports': False,
            'cargo_types': False,
            'years': False
        }
        
        with self.db_manager.neo4j.get_session() as session:
            # 检查Port节点
            result = session.run("MATCH (p:Port) RETURN count(p) as count")
            port_count = result.single()['count']
            dependencies['ports'] = port_count > 0
            logger.info(f"发现 {port_count} 个Port节点")
            
            # 检查CargoType节点
            result = session.run("MATCH (ct:CargoType) RETURN count(ct) as count")
            cargo_type_count = result.single()['count']
            dependencies['cargo_types'] = cargo_type_count > 0
            logger.info(f"发现 {cargo_type_count} 个CargoType节点")
            
            # 检查Year节点
            result = session.run("MATCH (y:Year) RETURN count(y) as count")
            year_count = result.single()['count']
            dependencies['years'] = year_count > 0
            logger.info(f"发现 {year_count} 个Year节点")
        
        return dependencies
    
    def create_yearmonth_nodes(self, year_months: Set[str]) -> int:
        """
        创建YearMonth节点及其与Year的关系
        
        Args:
            year_months: 年月字符串集合，格式如 "202401"
            
        Returns:
            int: 创建的YearMonth节点数量
        """
        created_count = 0
        
        with self.db_manager.neo4j.get_session() as session:
            for ym in year_months:
                if len(ym) != 6 or not ym.isdigit():
                    logger.warning(f"无效的年月格式: {ym}")
                    continue
                
                year = int(ym[:4])
                month = int(ym[4:])
                
                # 创建或获取Year节点
                session.run("""
                    MERGE (y:Year {year: $year})
                """, year=year)
                
                # 创建YearMonth节点
                result = session.run("""
                    MERGE (ym:YearMonth {ym: $ym, year: $year, month: $month})
                    WITH ym
                    MATCH (y:Year {year: $year})
                    MERGE (ym)-[:MONTH_OF_YEAR]->(y)
                    RETURN ym.ym as created_ym
                """, ym=ym, year=year, month=month)
                
                if result.single():
                    created_count += 1
                    logger.debug(f"创建YearMonth节点: {ym}")
        
        logger.info(f"创建了 {created_count} 个YearMonth节点")
        return created_count
    
    def get_missing_ports(self, port_names: Set[str]) -> Set[str]:
        """
        检查缺失的港口节点
        
        Args:
            port_names: 需要检查的港口名称集合
            
        Returns:
            Set[str]: 缺失的港口名称
        """
        existing_ports = set()
        
        with self.db_manager.neo4j.get_session() as session:
            result = session.run("""
                MATCH (p:Port)
                WHERE p.name IN $port_names
                RETURN p.name as name
            """, port_names=list(port_names))
            
            existing_ports = {record['name'] for record in result}
        
        missing_ports = port_names - existing_ports
        if missing_ports:
            logger.warning(f"缺失的港口节点: {missing_ports}")
        
        return missing_ports
    
    def get_missing_cargo_types(self, cargo_types: Set[str]) -> Set[str]:
        """
        检查缺失的货物类型节点
        
        Args:
            cargo_types: 需要检查的货物类型名称集合
            
        Returns:
            Set[str]: 缺失的货物类型名称
        """
        existing_cargo_types = set()
        
        with self.db_manager.neo4j.get_session() as session:
            result = session.run("""
                MATCH (ct:CargoType)
                WHERE ct.subName IN $cargo_types
                RETURN ct.subName as subName
            """, cargo_types=list(cargo_types))
            
            existing_cargo_types = {record['subName'] for record in result}
        
        missing_types = cargo_types - existing_cargo_types
        if missing_types:
            logger.warning(f"缺失的货物类型节点: {missing_types}")
        
        return missing_types
    
    def load_port_stats(self, port_name: str = None, time_filter: str = "ym like '2024%'") -> Dict[str, int]:
        """
        从Oracle数据库加载港口月度统计数据

        Args:
            port_name: 港口名称，如果为None则加载所有港口
            time_filter: 时间过滤条件，默认为2024年数据

        Returns:
            Dict: 加载结果统计
        """
        try:
            # 如果没有指定港口，获取所有港口
            if port_name is None:
                ports = self.get_all_ports()
                if not ports:
                    logger.warning("未找到任何港口，跳过港口统计加载")
                    return {"created": 0, "errors": 0}

                # 递归调用，为每个港口加载数据
                total_created = 0
                total_errors = 0

                for port in ports:
                    logger.info(f"加载港口统计数据: {port}")
                    result = self.load_port_stats(port, time_filter)
                    total_created += result.get("created", 0)
                    total_errors += result.get("errors", 0)

                logger.info(f"所有港口统计加载完成: 创建 {total_created}, 错误 {total_errors}")
                return {"created": total_created, "errors": total_errors}

            # 为指定港口构建SQL查询
            sql = f"""
            select a.ym,a.merge_city_name portName,a.ShipCount inShipCount,b.ShipCount outShipCount,a.LoadRatio inLoadRatio,b.LoadRatio outLoadRatio,c.cargo inCargo_tun,d.cargo outCargo_tun,e.anchorTime_days from(
            select ym,merge_city_name,count(1) ShipCount,round(avg(t.actual_carrying_capacity/t.ship_dwt),2) LoadRatio from yssjtj.dwd_ship_eep_report_i_d t where t.merge_city_name='{port_name}' and t.ship_dwt>0 and t.arrival_or_leave=0 and {time_filter} group by ym,merge_city_name
            ) a join (
            select ym,merge_city_name,count(1) ShipCount,round(avg(t.actual_carrying_capacity/t.ship_dwt),2) LoadRatio from yssjtj.dwd_ship_eep_report_i_d t where t.merge_city_name='{port_name}' and t.ship_dwt>0 and t.arrival_or_leave=1 and {time_filter} group by ym,merge_city_name
            ) b on a.ym=b.ym and a.merge_city_name=b.merge_city_name
            join (
            select ym,merge_city_name_d merge_city_name,sum(capacity) cargo from yssjtj.od_dwd_statistics t where t.merge_city_name_d='{port_name}' and {time_filter} group by ym,merge_city_name_d
            ) c on a.ym=c.ym and a.merge_city_name=c.merge_city_name
            join (
            select ym,merge_city_name_o merge_city_name,sum(capacity) cargo from yssjtj.od_dwd_statistics t where t.merge_city_name_o='{port_name}' and {time_filter} group by ym,merge_city_name_o
            ) d on a.ym=d.ym and a.merge_city_name=d.merge_city_name
            join (
            select ym,merge_city_name_o merge_city_name,round(avg(t.anchor_time)/1440,1) anchorTime_days from yssjtj.dwd_ship_eep_report_iod_i_d t where t.merge_city_name_o='{port_name}' and {time_filter} group by ym,merge_city_name_o
            ) e on a.ym=e.ym and a.merge_city_name=e.merge_city_name
            """
            
            logger.info(f"从Oracle执行查询: {sql}")
            
            # 使用database_manager中的Oracle客户端读取数据
            df = self.db_manager.oracle.execute_query_to_dataframe(sql)
            logger.info(f"从Oracle加载了{len(df)}条港口月度统计记录")
            
            if df.empty:
                logger.warning("从Oracle查询到的数据为空")
                return {"created": 0, "errors": 0}
            
            # 收集年月和港口名称用于依赖检查（统一列名大小写）
            year_months = set(df['YM'].astype(str) if 'YM' in df.columns else df['ym'].astype(str))
            port_names = set(df['PORTNAME'] if 'PORTNAME' in df.columns else df['portName'])
            
            # 创建YearMonth节点
            # self.create_yearmonth_nodes(year_months)
            
            # 检查港口依赖
            missing_ports = self.get_missing_ports(port_names)
            if missing_ports:
                logger.error(f"无法处理数据，缺失港口节点: {missing_ports}")
                return {"created": 0, "errors": len(df)}
            
            created_count = 0
            error_count = 0
            
            with self.db_manager.neo4j.get_session() as session:
                for _, row in df.iterrows():
                    try:
                        # 创建PortMonthStat节点并建立关系
                        session.run("""
                            MATCH (p:Port {name: $port_name})
                            MATCH (ym:YearMonth {ym: $ym})
                            
                            MERGE (pms:PortMonthStat {
                                port_ym_key: $port_name + '_' + $ym
                            })
                            SET pms.inShipCount = $inShipCount,
                                pms.outShipCount = $outShipCount,
                                pms.inCargo_ton = $inCargo_ton,
                                pms.outCargo_ton = $outCargo_ton,
                                pms.anchorTime_days = $anchorTime_days,
                                pms.inLoadRatio = $inLoadRatio,
                                pms.outLoadRatio = $outLoadRatio,
                                pms.lastUpdated = datetime()
                            
                            MERGE (pms)-[:STAT_FOR_PORT]->(p)
                            MERGE (pms)-[:STAT_FOR_MONTH]->(ym)
                        """,
                        port_name=str(row['PORTNAME'] if 'PORTNAME' in row else row['portName']),
                        ym=str(row['YM'] if 'YM' in row else row['ym']),
                        inShipCount=int(row['INSHIPCOUNT'] if 'INSHIPCOUNT' in row else row['inShipCount']) if pd.notna(row.get('INSHIPCOUNT', row.get('inShipCount'))) else 0,
                        outShipCount=int(row['OUTSHIPCOUNT'] if 'OUTSHIPCOUNT' in row else row['outShipCount']) if pd.notna(row.get('OUTSHIPCOUNT', row.get('outShipCount'))) else 0,
                        inCargo_ton=float(row['INCARGO_TUN'] if 'INCARGO_TUN' in row else row['inCargo_ton']) if pd.notna(row.get('INCARGO_TUN', row.get('inCargo_ton'))) else 0.0,
                        outCargo_ton=float(row['OUTCARGO_TUN'] if 'OUTCARGO_TUN' in row else row['outCargo_ton']) if pd.notna(row.get('OUTCARGO_TUN', row.get('outCargo_ton'))) else 0.0,
                        anchorTime_days=float(row['ANCHORTIME_DAYS'] if 'ANCHORTIME_DAYS' in row else row['anchorTime_days']) if pd.notna(row.get('ANCHORTIME_DAYS', row.get('anchorTime_days'))) else 0.0,
                        inLoadRatio=float(row['INLOADRATIO'] if 'INLOADRATIO' in row else row['inLoadRatio']) if pd.notna(row.get('INLOADRATIO', row.get('inLoadRatio'))) else 0.0,
                        outLoadRatio=float(row['OUTLOADRATIO'] if 'OUTLOADRATIO' in row else row['outLoadRatio']) if pd.notna(row.get('OUTLOADRATIO', row.get('outLoadRatio'))) else 0.0)
                        
                        created_count += 1
                        port_name_log = row['PORTNAME'] if 'PORTNAME' in row else row['portName']
                        ym_log = row['YM'] if 'YM' in row else row['ym']
                        logger.debug(f"创建港口月度统计: {port_name_log} - {ym_log}")
                        
                    except Exception as e:
                        error_count += 1
                        port_name_log = row['PORTNAME'] if 'PORTNAME' in row else row['portName']
                        ym_log = row['YM'] if 'YM' in row else row['ym']
                        logger.error(f"创建港口月度统计失败: {port_name_log} - {ym_log}, 错误: {e}")
            
            logger.info(f"港口月度统计加载完成: 成功 {created_count}, 失败 {error_count}")
            return {"created": created_count, "errors": error_count}
            
        except Exception as e:
            logger.error(f"从Oracle加载港口月度统计失败: {e}")
            return {"created": 0, "errors": 1}
    
    def load_port_cargo_stats(self, port_name: str = None, time_filter: str = "ym like '2024%'") -> Dict[str, int]:
        """
        从Oracle数据库加载港口分货类月度统计数据

        Args:
            port_name: 港口名称，如果为None则加载所有港口
            time_filter: 时间过滤条件，默认为2024年数据

        Returns:
            Dict: 加载结果统计
        """
        try:
            # # 如果没有指定港口，获取所有港口
            if port_name is None:
                ports = self.get_all_ports()
                if not ports:
                    logger.warning("未找到任何港口，跳过港口分货类统计加载")
                    return {"created": 0, "errors": 0}

                # 递归调用，为每个港口加载数据
                total_created = 0
                total_errors = 0

                for port in ports:
                    logger.info(f"加载港口分货类统计数据: {port}")
                    result = self.load_port_cargo_stats(port, time_filter)
                    total_created += result.get("created", 0)
                    total_errors += result.get("errors", 0)

                logger.info(f"所有港口分货类统计加载完成: 创建 {total_created}, 错误 {total_errors}")
                return {"created": total_created, "errors": total_errors}

            # 为指定港口构建SQL查询
            sql = f"""
                select a.ym,a.city_name portName,a.cargo_name cargoType,a.c inCargo_tun,b.c outCargo_tun from (
                select ym,merge_city_name_d city_name,t.cargo_name,sum(capacity) c from yssjtj.od_dwd_statistics t where t.merge_city_name_d='{port_name}' and {time_filter} and flag_trade='01' group by ym,t.cargo_name,merge_city_name_d order by ym,cargo_name
                ) a  join (
                select ym,merge_city_name_o city_name,t.cargo_name,sum(capacity) c from yssjtj.od_dwd_statistics t where t.merge_city_name_o='{port_name}' and {time_filter} and flag_trade='01' group by ym,merge_city_name_o,t.cargo_name order by ym,cargo_name
                ) b on a.ym=b.ym and a.cargo_name=b.cargo_name
            """
            
            logger.info(f"从Oracle执行查询: {sql}")
            
            # 使用database_manager中的Oracle客户端读取数据
            df = self.db_manager.oracle.execute_query_to_dataframe(sql)
            logger.info(f"从Oracle加载了{len(df)}条港口货类月度统计记录")
            
            if df.empty:
                logger.warning("从Oracle查询到的数据为空")
                return {"created": 0, "errors": 0}
            
            # 收集数据用于依赖检查（统一列名大小写）
            year_months = set(df['YM'].astype(str) if 'YM' in df.columns else df['ym'].astype(str))
            port_names = set(df['PORTNAME'] if 'PORTNAME' in df.columns else df['portName'])
            cargo_types = set(df['CARGOTYPE'] if 'CARGOTYPE' in df.columns else df['cargoType'])
            
            # 创建YearMonth节点
            self.create_yearmonth_nodes(year_months)
            
            # 检查依赖
            missing_ports = self.get_missing_ports(port_names)
            missing_cargo_types = self.get_missing_cargo_types(cargo_types)
            
            if missing_ports or missing_cargo_types:
                logger.error(f"无法处理数据，缺失节点 - 港口: {missing_ports}, 货类: {missing_cargo_types}")
                return {"created": 0, "errors": len(df)}
            
            created_count = 0
            error_count = 0
            
            with self.db_manager.neo4j.get_session() as session:
                for _, row in df.iterrows():
                    try:
                        # 创建PortMonthCargoStat节点并建立关系
                        session.run("""
                            MATCH (p:Port {name: $port_name})
                            MATCH (ym:YearMonth {ym: $ym})
                            MATCH (ct:CargoType {subName: $cargo_type})
                            
                            MERGE (pmcs:PortMonthCargoStat {
                                port_ym_cargo_key: $port_name + '_' + $ym + '_' + $cargo_type
                            })
                            SET pmcs.inCargo_ton = $inCargo_ton,
                                pmcs.outCargo_ton = $outCargo_ton,
                                pmcs.lastUpdated = datetime()
                            
                            MERGE (pmcs)-[:PORT_CARGO_STAT_FOR_PORT]->(p)
                            MERGE (pmcs)-[:PORT_CARGO_STAT_FOR_MONTH]->(ym)
                            MERGE (pmcs)-[:PORT_CARGO_STAT_FOR_TYPE]->(ct)
                        """,
                        port_name=str(row['PORTNAME'] if 'PORTNAME' in row else row['portName']),
                        ym=str(row['YM'] if 'YM' in row else row['ym']),
                        cargo_type=str(row['CARGOTYPE'] if 'CARGOTYPE' in row else row['cargoType']),
                        inCargo_ton=float(row['INCARGO_TUN'] if 'INCARGO_TUN' in row else row['inCargo_ton']) if pd.notna(row.get('INCARGO_TUN', row.get('inCargo_ton'))) else 0.0,
                        outCargo_ton=float(row['OUTCARGO_TUN'] if 'OUTCARGO_TUN' in row else row['outCargo_ton']) if pd.notna(row.get('OUTCARGO_TUN', row.get('outCargo_ton'))) else 0.0)
                        
                        
                        created_count += 1
                        port_name_log = row['PORTNAME'] if 'PORTNAME' in row else row['portName']
                        ym_log = row['YM'] if 'YM' in row else row['ym']
                        cargo_type_log = row['CARGOTYPE'] if 'CARGOTYPE' in row else row['cargoType']
                        # logger.debug(f"创建港口货类统计: {port_name_log} - {ym_log} - {cargo_type_log}")
                        
                    except Exception as e:
                        error_count += 1
                        port_name_log = row['PORTNAME'] if 'PORTNAME' in row else row['portName']
                        ym_log = row['YM'] if 'YM' in row else row['ym']
                        cargo_type_log = row['CARGOTYPE'] if 'CARGOTYPE' in row else row['cargoType']
                        logger.error(f"创建港口货类统计失败: {port_name_log} - {ym_log} - {cargo_type_log}, 错误: {e}")
            
            logger.info(f"港口货类统计加载完成: 成功 {created_count}, 失败 {error_count}")
            return {"created": created_count, "errors": error_count}
            
        except Exception as e:
            logger.error(f"从Oracle加载港口货类统计失败: {e}")
            return {"created": 0, "errors": 1}
    
    def execute_etl(self, port_name: str = None, time_filter: str = "ym like '2024%'",limit:int=10000) -> Dict[str, Any]:
        """
        执行完整的港口动态节点ETL流程

        Args:
            port_name: 港口名称，如果为None则加载所有港口
            time_filter: 时间过滤条件，默认为2024年数据

        Returns:
            Dict: ETL执行结果
        """
        start_time = datetime.now()
        logger.info("开始执行港口动态节点ETL流程")
        
        try:
            # 验证静态依赖
            dependencies = self.validate_dependencies()
            if not all(dependencies.values()):
                missing = [k for k, v in dependencies.items() if not v]
                logger.error(f"静态节点依赖不满足: {missing}")
                return {
                    "success": False,
                    "error": f"缺失静态节点: {missing}",
                    "execution_time": (datetime.now() - start_time).total_seconds()
                }
            
            # 加载港口月度统计
            port_result = self.load_port_stats(port_name, time_filter)

            # 加载港口货类统计
            cargo_result = self.load_port_cargo_stats(port_name, time_filter)
            
            # 汇总结果
            total_created = port_result["created"] + cargo_result["created"]
            total_errors = port_result["errors"] + cargo_result["errors"]
            
            result = {
                "success": total_errors == 0,
                "port_stats": port_result,
                "cargo_stats": cargo_result,
                "total_created": total_created,
                "total_errors": total_errors,
                "execution_time": (datetime.now() - start_time).total_seconds(),
                "completion_time": datetime.now()
            }
            
            if result["success"]:
                logger.info(f"港口ETL流程成功完成: 创建 {total_created} 个节点，耗时 {result['execution_time']:.2f} 秒")
            else:
                logger.error(f"港口ETL流程部分失败: 创建 {total_created} 个节点，错误 {total_errors} 个")
            
            return result
            
        except Exception as e:
            logger.error(f"港口ETL流程执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_time": (datetime.now() - start_time).total_seconds(),
                "completion_time": datetime.now()
            }
    
    def get_statistics(self) -> Dict[str, int]:
        """
        获取港口动态节点统计信息
        
        Returns:
            Dict: 统计信息
        """
        stats = {}
        
        with self.db_manager.neo4j.get_session() as session:
            # YearMonth节点数量
            result = session.run("MATCH (n:YearMonth) RETURN count(n) as count")
            stats['yearmonths'] = result.single()['count']
            
            # PortMonthStat节点数量
            result = session.run("MATCH (n:PortMonthStat) RETURN count(n) as count")
            stats['port_stats'] = result.single()['count']
            
            # PortMonthCargoStat节点数量
            result = session.run("MATCH (n:PortMonthCargoStat) RETURN count(n) as count")
            stats['cargo_stats'] = result.single()['count']
            
            # 总动态节点数
            stats['total_nodes'] = (
                stats['yearmonths'] + 
                stats['port_stats'] + 
                stats['cargo_stats']
            )
            
            logger.info(f"港口动态节点统计: {stats}")
        
        return stats
    
    def get_port_summary(self) -> List[Dict]:
        """
        获取港口统计摘要信息
        
        Returns:
            List[Dict]: 港口统计摘要
        """
        with self.db_manager.neo4j.get_session() as session:
            result = session.run("""
                MATCH (p:Port)<-[:STAT_FOR_PORT]-(pms:PortMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth)
                RETURN p.name as port_name,
                       count(pms) as month_count,
                       sum(pms.inCargo_ton) as total_in_cargo,
                       sum(pms.outCargo_ton) as total_out_cargo,
                       avg(pms.inLoadRatio) as avg_in_load_ratio,
                       avg(pms.outLoadRatio) as avg_out_load_ratio
                ORDER BY total_in_cargo DESC
            """)
            
            summary = []
            for record in result:
                summary.append({
                    'port_name': record['port_name'],
                    'month_count': record['month_count'],
                    'total_in_cargo': record['total_in_cargo'],
                    'total_out_cargo': record['total_out_cargo'],
                    'avg_in_load_ratio': record['avg_in_load_ratio'],
                    'avg_out_load_ratio': record['avg_out_load_ratio']
                })
            
            logger.info(f"获取了{len(summary)}个港口的统计摘要")
            return summary

    def load_port_complete_data(self, port_name: str) -> Dict[str, Any]:
        """加载港口完整数据，严格按照数据库设计文档v4.0实现"""
        logger.info(f"加载港口完整数据: {port_name}")
        
        port_data = {"basic_info": {}, "month_stats": [], "month_cargo_stats": [], "related_provinces": [], "related_basins": []}
        
        # 1. 港口基本信息 - 按设计文档Port节点属性（只有name, prov, sortNo）
        basic_query = "MATCH (p:Port {name: $port_name}) RETURN p.name as name, p.prov as prov, p.sortNo as sortNo"
        basic_result = self._execute_query(basic_query, {"port_name": port_name})
        if basic_result:
            port_data["basic_info"] = basic_result[0]
        
        # 2. 港口月度统计数据 - 按设计文档PortMonthStat节点属性
        month_stats_query = """
            MATCH (p:Port {name: $port_name})<-[:STAT_FOR_PORT]-(pms:PortMonthStat)
            MATCH (pms)-[:STAT_FOR_MONTH]->(ym:YearMonth)
            RETURN pms.inShipCount as inShipCount, pms.outShipCount as outShipCount,
                   pms.inCargo_ton as inCargo_ton, pms.outCargo_ton as outCargo_ton,
                   pms.anchorTime_days as anchorTime_days, pms.inLoadRatio as inLoadRatio,
                   pms.outLoadRatio as outLoadRatio, pms.lastUpdated as lastUpdated,
                   ym.ym as ym, ym.year as year, ym.month as month, ym.daysInMonth as daysInMonth
            ORDER BY ym.ym DESC
        """
        port_data["month_stats"] = self._execute_query(month_stats_query, {"port_name": port_name})
        
        # 3. 港口分货类月度统计 - 按设计文档PortMonthCargoStat节点属性
        month_cargo_stats_query = """
            MATCH (p:Port {name: $port_name})<-[:PORT_CARGO_STAT_FOR_PORT]-(pmcs:PortMonthCargoStat)
            MATCH (pmcs)-[:PORT_CARGO_STAT_FOR_MONTH]->(ym:YearMonth)
            MATCH (pmcs)-[:PORT_CARGO_STAT_FOR_TYPE]->(ct:CargoType)
            RETURN pmcs.inCargo_ton as inCargo_ton, pmcs.outCargo_ton as outCargo_ton,
                   pmcs.lastUpdated as lastUpdated, ym.ym as ym, ym.year as year, ym.month as month,
                   ct.subCode as cargo_subCode, ct.subName as cargo_subName, ct.name as cargo_name
            ORDER BY ym.ym DESC, ct.subCode
        """
        port_data["month_cargo_stats"] = self._execute_query(month_cargo_stats_query, {"port_name": port_name})
        
        # 4. 港口所在省份关系
        province_query = """
            MATCH (p:Port {name: $port_name})-[:LOCATED_IN_PROVINCE]->(prov:Province)
            RETURN prov.name as province_name, prov.code as province_code
        """
        port_data["related_provinces"] = self._execute_query(province_query, {"port_name": port_name})
        
        # 5. 港口所属流域关系
        basin_query = """
            MATCH (p:Port {name: $port_name})-[:LOCATED_IN_PROVINCE]->(prov:Province)-[:PART_OF_BASIN]->(b:Basin)
            RETURN b.name as basin_name
        """
        port_data["related_basins"] = self._execute_query(basin_query, {"port_name": port_name})
        
        self.stats["entity_loads"] += 1
        logger.info(f"港口 {port_name} 完整数据加载完成")
        return port_data
