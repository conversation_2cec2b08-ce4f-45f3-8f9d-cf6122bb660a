#!/usr/bin/env python3
"""
测试港口航线查询功能
验证新增的港口航线查询方法是否正常工作
"""

import sys
import os

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from kgnode.database import DatabaseManager
from kgnode.node_port import PortDynamicETL
import json
from loguru import logger

def test_port_route_queries():
    """测试港口航线查询功能"""
    print("=== 测试港口航线查询功能 ===")
    
    # 初始化数据库管理器和港口ETL
    db_manager = DatabaseManager()
    port_etl = PortDynamicETL(db_manager)
    
    try:
        # 测试港口列表
        test_ports = ["九江", "武汉", "南京", "上海"]
        test_period = "202407"
        
        for port_name in test_ports:
            print(f"\n--- 测试港口: {port_name} ---")
            
            # 1. 测试基本的航线关联查询
            print("1. 查询港口关联的航线...")
            routes_result = port_etl.get_port_related_routes(port_name, test_period)
            
            if routes_result.get("error"):
                print(f"   ❌ 查询失败: {routes_result['error']}")
                continue
                
            print(f"   ✅ 找到 {routes_result['total_routes']} 条关联航线")
            print(f"      - 出发航线: {routes_result['total_outbound']} 条")
            print(f"      - 到达航线: {routes_result['total_inbound']} 条")
            
            # 显示前3条出发航线
            if routes_result['outbound_routes']:
                print("   出发航线示例:")
                for i, route in enumerate(routes_result['outbound_routes'][:3], 1):
                    print(f"     {i}. {route['route_name']} -> {route['destination_port']}")
                    print(f"        距离: {route['distance_km']}km, 货量: {route['total_cargo']:.1f}吨")
            
            # 显示前3条到达航线
            if routes_result['inbound_routes']:
                print("   到达航线示例:")
                for i, route in enumerate(routes_result['inbound_routes'][:3], 1):
                    print(f"     {i}. {route['origin_port']} -> {route['route_name']}")
                    print(f"        距离: {route['distance_km']}km, 货量: {route['total_cargo']:.1f}吨")
            
            # 2. 测试航行摘要查询
            print("\n2. 查询港口航行摘要...")
            summary_result = port_etl.get_port_navigation_summary(port_name, test_period, limit=5)
            
            if summary_result.get("error"):
                print(f"   ❌ 摘要查询失败: {summary_result['error']}")
                continue
                
            print(f"   ✅ 找到 {summary_result['summary']['periods_found']} 个时间期间的数据")
            print(f"   ✅ 包含航线数据: {summary_result['summary']['has_route_data']}")
            
            # 显示摘要数据
            for nav_data in summary_result['navigation_data'][:1]:  # 只显示第一个期间
                stats = nav_data['port_statistics']
                print(f"   期间 {nav_data['period']} 港口统计:")
                print(f"     进港: {stats['total_in_ships']}艘次, {stats['total_in_cargo']:.1f}吨")
                print(f"     出港: {stats['total_out_ships']}艘次, {stats['total_out_cargo']:.1f}吨")
                print(f"     锚泊时间: {stats['avg_anchor_time']:.1f}天")
                
                if nav_data['outbound_routes']:
                    print(f"     主要出发航线: {nav_data['outbound_routes'][0]['route_name']}")
                if nav_data['inbound_routes']:
                    print(f"     主要到达航线: {nav_data['inbound_routes'][0]['route_name']}")
            
            # 3. 测试完整数据加载（包含航线信息）
            print("\n3. 测试完整数据加载...")
            complete_data = port_etl.load_port_complete_data(port_name)
            
            print(f"   ✅ 基本信息: {complete_data['basic_info']}")
            print(f"   ✅ 月度统计: {len(complete_data['month_stats'])} 条")
            print(f"   ✅ 货类统计: {len(complete_data['month_cargo_stats'])} 条")
            print(f"   ✅ 关联航线: {len(complete_data['related_routes'])} 条")
            
            # 显示航线信息
            if complete_data['related_routes']:
                print("   关联航线示例:")
                for i, route in enumerate(complete_data['related_routes'][:3], 1):
                    direction = "出发" if route['direction'] == 'outbound' else "到达"
                    other_port = route.get('destination', route.get('origin', '未知'))
                    print(f"     {i}. {route['route_name']} ({direction}) -> {other_port}")
            
            print(f"   港口 {port_name} 测试完成\n")
            
            # 只测试第一个港口的详细信息，避免输出过多
            if port_name == test_ports[0]:
                break
    
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        port_etl.close()
        db_manager.close_all()
    
    return True

def test_data_availability():
    """检查数据可用性"""
    print("=== 检查数据可用性 ===")
    
    db_manager = DatabaseManager()
    
    try:
        with db_manager.neo4j.get_session() as session:
            # 检查港口数量
            result = session.run("MATCH (p:Port) RETURN count(p) as count")
            port_count = result.single()['count']
            print(f"港口节点数量: {port_count}")
            
            # 检查航线数量
            result = session.run("MATCH (sr:ShippingRoute) RETURN count(sr) as count")
            route_count = result.single()['count']
            print(f"航线节点数量: {route_count}")
            
            # 检查港口统计数量
            result = session.run("MATCH (pms:PortMonthStat) RETURN count(pms) as count")
            port_stat_count = result.single()['count']
            print(f"港口月度统计数量: {port_stat_count}")
            
            # 检查航线统计数量
            result = session.run("MATCH (rms:RouteMonthStat) RETURN count(rms) as count")
            route_stat_count = result.single()['count']
            print(f"航线月度统计数量: {route_stat_count}")
            
            # 检查关系
            result = session.run("""
                MATCH (p:Port)<-[:ROUTE_ORIGIN]-(sr:ShippingRoute)-[:ROUTE_DESTINATION]->(p2:Port)
                RETURN count(*) as count
            """)
            route_relation_count = result.single()['count']
            print(f"航线-港口关系数量: {route_relation_count}")
            
            if route_count == 0:
                print("⚠️  警告: 没有航线数据，需要先运行航线ETL")
                return False
            elif route_relation_count == 0:
                print("⚠️  警告: 航线与港口的关系未建立")
                return False
            else:
                print("✅ 数据检查通过")
                return True
                
    except Exception as e:
        print(f"❌ 数据检查失败: {e}")
        return False
    
    finally:
        db_manager.close_all()

def main():
    """主函数"""
    print("开始测试港口航线查询功能\n")
    
    # 1. 检查数据可用性
    if not test_data_availability():
        print("\n❌ 数据不完整，请先确保:")
        print("1. 运行静态节点ETL创建港口节点")
        print("2. 运行航线ETL创建航线节点和关系")
        print("3. 运行港口ETL创建港口统计数据")
        return False
    
    # 2. 测试查询功能
    success = test_port_route_queries()
    
    print("\n=== 测试结果 ===")
    if success:
        print("🎉 港口航线查询功能测试成功！")
        print("\n✅ 新增功能:")
        print("1. get_port_related_routes() - 查询港口关联的所有航线")
        print("2. get_port_navigation_summary() - 获取港口航行摘要信息")
        print("3. load_port_complete_data() - 完整数据加载（包含航线信息）")
        print("\n💡 使用方法:")
        print("from kgnode.node_port import PortDynamicETL")
        print("port_etl = PortDynamicETL(database_manager)")
        print("routes = port_etl.get_port_related_routes('九江', '202407')")
        print("summary = port_etl.get_port_navigation_summary('九江')")
        return True
    else:
        print("❌ 港口航线查询功能测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
