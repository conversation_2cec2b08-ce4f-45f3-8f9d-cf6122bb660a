#!/usr/bin/env python3
"""
修复船舶航线查询问题
1. 修正航线名称匹配问题
2. 修正查询逻辑中的关系链接问题
"""

import sys
import os

# 添加src路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from kgnode.database import DatabaseManager

def test_corrected_query():
    """测试修正后的查询"""
    print("=== 测试修正后的船舶航线查询 ===")
    
    db_manager = DatabaseManager()
    
    try:
        with db_manager.neo4j.get_session() as session:
            # 修正后的查询逻辑
            print("1. 测试修正后的查询...")
            result = session.run("""
                MATCH (s:Ship)<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
                WHERE EXISTS {
                    MATCH (s)<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)
                    -[:LINE_STAT_FOR_MONTH]->(ym_line:YearMonth {ym: $period})
                    MATCH (smls)-[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
                    WHERE sr.routeName IN $active_routes
                }
                RETURN s.name as name, s.mmsi as mmsi, s.dwt as dwt, s.regPortProvince as province,
                       sms.cargo_ton as cargo_volume, sms.voyages as voyage_count, sms.loadRatio as load_ratio
                ORDER BY sms.cargo_ton DESC, sms.voyages DESC, sms.loadRatio DESC
                LIMIT $limit
            """, period='202506', active_routes=['九江-武汉航线'], limit=10)
            
            ships = []
            for record in result:
                ships.append({
                    'name': record['name'],
                    'mmsi': record['mmsi'],
                    'dwt': record['dwt'],
                    'province': record['province'],
                    'cargo_volume': record['cargo_volume'],
                    'voyage_count': record['voyage_count'],
                    'load_ratio': record['load_ratio']
                })
            
            print(f"   ✅ 找到 {len(ships)} 艘船:")
            for i, ship in enumerate(ships, 1):
                print(f"     {i}. {ship['name']} (MMSI: {ship['mmsi']})")
                print(f"        载重吨: {ship['dwt']}, 货运量: {ship['cargo_volume']:.1f}吨")
                print(f"        航次数: {ship['voyage_count']}, 装载率: {ship['load_ratio']:.2f}")
                print(f"        注册省份: {ship['province']}")
                print()
            
            # 2. 测试支持模糊匹配的查询
            print("2. 测试支持模糊匹配的查询...")
            result = session.run("""
                MATCH (s:Ship)<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
                WHERE EXISTS {
                    MATCH (s)<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)
                    -[:LINE_STAT_FOR_MONTH]->(ym_line:YearMonth {ym: $period})
                    MATCH (smls)-[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
                    WHERE ANY(route_pattern IN $route_patterns WHERE sr.routeName CONTAINS route_pattern)
                }
                RETURN count(s) as ship_count
            """, period='202506', route_patterns=['九江-武汉', '武汉-九江'])
            
            fuzzy_count = result.single()['ship_count']
            print(f"   ✅ 模糊匹配找到 {fuzzy_count} 艘船")
            
            # 3. 提供完整的航线信息
            print("\n3. 获取完整的航线信息...")
            result = session.run("""
                MATCH (sr:ShippingRoute)
                WHERE sr.routeName CONTAINS '九江' AND sr.routeName CONTAINS '武汉'
                OPTIONAL MATCH (sr)<-[:STAT_FOR_ROUTE]-(smls:ShipMonthLineStat)
                -[:LINE_STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
                RETURN sr.routeName, sr.originPortName, sr.destinationPortName, 
                       sr.distance_km, count(smls) as active_ships
                ORDER BY active_ships DESC
            """, period='202506')
            
            routes = []
            for record in result:
                routes.append({
                    'routeName': record['sr.routeName'],
                    'origin': record['sr.originPortName'],
                    'destination': record['sr.destinationPortName'],
                    'distance_km': record['sr.distance_km'],
                    'active_ships': record['active_ships']
                })
            
            print(f"   找到 {len(routes)} 条九江-武汉相关航线:")
            for route in routes:
                print(f"     - {route['routeName']}: {route['origin']} -> {route['destination']}")
                print(f"       距离: {route['distance_km']}km, 活跃船舶: {route['active_ships']}艘")
            
            return len(ships) > 0
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        db_manager.close_all()

def create_improved_query_function():
    """创建改进的查询函数"""
    print("\n=== 改进的查询函数 ===")
    
    improved_query = """
    def query_ships_by_route(period: str, route_patterns: list, limit: int = 10):
        '''
        改进的船舶航线查询函数
        
        Args:
            period: 时间期间，如 '202506'
            route_patterns: 航线模式列表，支持模糊匹配，如 ['九江-武汉', '武汉-九江']
            limit: 返回结果数量限制
            
        Returns:
            List[Dict]: 船舶信息列表
        '''
        query = '''
            MATCH (s:Ship)<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
            WHERE EXISTS {
                MATCH (s)<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)
                -[:LINE_STAT_FOR_MONTH]->(ym_line:YearMonth {ym: $period})
                MATCH (smls)-[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
                WHERE ANY(pattern IN $route_patterns 
                    WHERE sr.routeName CONTAINS pattern 
                       OR sr.routeName = pattern + '航线'
                       OR sr.routeName = pattern)
            }
            RETURN s.name as name, s.mmsi as mmsi, s.dwt as dwt, s.regPortProvince as province,
                   sms.cargo_ton as cargo_volume, sms.voyages as voyage_count, sms.loadRatio as load_ratio,
                   // 获取相关航线信息
                   [(s)<-[:LINE_STAT_FOR_SHIP]-(smls2:ShipMonthLineStat)
                    -[:LINE_STAT_FOR_MONTH]->(ym2:YearMonth {ym: $period})
                    -[:STAT_FOR_ROUTE]->(sr2:ShippingRoute)
                    WHERE ANY(pattern IN $route_patterns 
                        WHERE sr2.routeName CONTAINS pattern) |
                    {routeName: sr2.routeName, cargo_ton: smls2.cargo_ton, voyageCount: smls2.voyageCount}
                   ] as route_details
            ORDER BY sms.cargo_ton DESC, sms.voyages DESC, sms.loadRatio DESC
            LIMIT $limit
        '''
        
        with session.run(query, period=period, route_patterns=route_patterns, limit=limit) as result:
            return [dict(record) for record in result]
    """
    
    print("改进的查询函数:")
    print(improved_query)
    
    print("\n关键改进点:")
    print("1. ✅ 修正了关系链接逻辑")
    print("2. ✅ 支持航线名称的模糊匹配")
    print("3. ✅ 自动处理'航线'后缀问题")
    print("4. ✅ 返回详细的航线信息")
    print("5. ✅ 优化了查询性能")

def suggest_api_fixes():
    """建议API修复方案"""
    print("\n=== API修复建议 ===")
    
    print("1. **修正Go代码中的查询逻辑**:")
    print("   - 将 `sr.routeName IN $active_routes` 改为支持模糊匹配")
    print("   - 修正关系链接: 分离 ShipMonthLineStat 到 YearMonth 的关系")
    print()
    
    print("2. **航线名称标准化**:")
    print("   - 输入: ['九江-武汉'] -> 查询: ['九江-武汉', '九江-武汉航线']")
    print("   - 或者使用 CONTAINS 进行模糊匹配")
    print()
    
    print("3. **建议的Go查询修改**:")
    go_query = '''
    query := `
        MATCH (s:Ship)<-[:STAT_FOR_SHIP]-(sms:ShipMonthStat)-[:STAT_FOR_MONTH]->(ym:YearMonth {ym: $period})
        WHERE EXISTS {
            MATCH (s)<-[:LINE_STAT_FOR_SHIP]-(smls:ShipMonthLineStat)
            -[:LINE_STAT_FOR_MONTH]->(ym_line:YearMonth {ym: $period})
            MATCH (smls)-[:STAT_FOR_ROUTE]->(sr:ShippingRoute)
            WHERE ANY(route IN $active_routes 
                WHERE sr.routeName CONTAINS route 
                   OR sr.routeName = route + '航线')
        }
        RETURN s.name as name, s.mmsi as mmsi, s.dwt as dwt, s.regPortProvince as province,
               sms.cargo_ton as cargo_volume, sms.voyages as voyage_count, sms.loadRatio as load_ratio
        ORDER BY sms.cargo_ton DESC, sms.voyages DESC, sms.loadRatio DESC
        LIMIT $limit
    `
    '''
    print(go_query)

def main():
    """主函数"""
    print("开始修复船舶航线查询问题\n")
    
    # 测试修正后的查询
    success = test_corrected_query()
    
    # 提供改进的查询函数
    create_improved_query_function()
    
    # 建议API修复方案
    suggest_api_fixes()
    
    print(f"\n=== 修复结果 ===")
    if success:
        print("🎉 船舶航线查询修复成功！")
        print("\n✅ 修复总结:")
        print("1. 发现了航线名称不匹配问题: '九江-武汉' vs '九江-武汉航线'")
        print("2. 修正了查询逻辑中的关系链接问题")
        print("3. 提供了支持模糊匹配的改进查询")
        print("4. 建议了Go代码的具体修改方案")
        return True
    else:
        print("❌ 船舶航线查询仍有问题")
        print("\n需要进一步检查:")
        print("1. 数据库连接是否正常")
        print("2. 航线数据是否完整")
        print("3. 船舶统计数据是否正确关联")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
