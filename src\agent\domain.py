"""长江航运领域数据 - 扩展支持多实体类型"""

from typing import Dict, Any

# 港口实体词典
PORTS = {
    "南京港", "镇江港", "武汉港", "重庆港", "九江港", "芜湖港", 
    "宜昌港", "岳阳港", "常州港", "无锡港", "苏州港", "南通港"
}

# 省份实体词典
PROVINCES = {
    "江苏省", "安徽省", "湖北省", "湖南省", "江西省", "重庆市", "四川省"
}

# 船舶实体词典（测试）
SHIPS = {
    "汉海5号"
}

# 流域/区域实体词典
BASINS = {
    "长江干线", "长江流域", "干线", "长江"
}

# 指标实体词典 - 扩展支持全部13个船舶指标
METRICS = {
    # 原有指标
    "吞吐量", "货物吞吐量", "集装箱吞吐量", "船舶通过量", "货运量",
    "锚地时间", "装卸效率", "周转率", "基本信息",  # 移除ALL
    
    # 船舶核心业务指标
    "capacity_ton", "货运量", "运输量",
    "turnover_tonkm", "周转量", "货物周转量",
    "voyages", "航次数", "运次", "航次",
    "mileage_km", "里程", "航行里程",
    
    # 船舶效率比率指标
    "opRatio", "营运率", "经营率",
    "loadRatio", "负载率", "装载率",
    "sailRatio", "航行率", "航行时间比",
    "dispatchLoadRatio", "发船装载率", "出港装载率",
    "distanceLoadRatio", "运距装载率", "里程装载率",
    
    # 船舶时间消耗指标
    "anchorTime_day", "在港时间", "停靠时间", "锚泊时间",
    "sailTime_day", "航行时间", "航海时间",
    
    # 船舶产出效率指标
    "tonDayOutput", "吨天产量", "载重吨天",
    "tonShipOutput", "吨船产量", "载重吨船"
}

# 货类实体词典 - 基于货类分类标准
CARGO_TYPES = {
    "散货", "集装箱", "煤炭", "铁矿石", "石油", "化工品", 
    "粮食", "建材", "钢材", "有色金属"
}

# 同义词映射 - 扩展船舶指标同义词
SYNONYMS = {
    # 港口同义词
    "上海": "上海港", "南通": "南通港", "太仓": "太仓港", "张家港": "张家港港", 
    "常熟": "常熟港", "泰州": "泰州港", "江阴": "江阴港", "常州": "常州港",
    "扬州": "扬州港", "镇江": "镇江港", "南京": "南京港", "安庆": "安庆港",
    "马鞍山": "马鞍山港", "芜湖": "芜湖港", "池州": "池州港", "铜陵": "铜陵港",
    "九江": "九江港", "咸宁": "咸宁港", "黄石": "黄石港", "鄂州": "鄂州港",
    "武汉": "武汉港", "岳阳": "岳阳港",
    
    # 省份同义词
    "安徽": "安徽省", "江苏": "江苏省", "重庆": "重庆市", "四川": "四川省",
    "上海": "上海市", "江西": "江西省", "湖南": "湖南省", "云南": "云南省",
    "湖北": "湖北省",
    
    # 指标同义词 - 传统指标
    "运量": "吞吐量", "通过量": "船舶通过量",
    "集装箱量": "集装箱吞吐量", "货运": "货物吞吐量",
    
    # 船舶指标同义词 - 核心业务
    "运输量": "capacity_ton", "货物运输量": "capacity_ton",
    "货物周转量": "turnover_tonkm", "周转": "turnover_tonkm",
    "航次": "voyages", "运次数": "voyages",
    "航行里程": "mileage_km", "行驶里程": "mileage_km",
    
    # 船舶指标同义词 - 效率比率
    "经营率": "opRatio", "运营率": "opRatio",
    "装载率": "loadRatio", "满载率": "loadRatio",
    "航行时间比": "sailRatio", "航行比": "sailRatio",
    "出港装载率": "dispatchLoadRatio", "起航装载率": "dispatchLoadRatio",
    "里程装载率": "distanceLoadRatio", "运距负载率": "distanceLoadRatio",
    
    # 船舶指标同义词 - 时间消耗
    "停靠时间": "anchorTime_day", "锚泊时间": "anchorTime_day", "港内时间": "anchorTime_day",
    "航海时间": "sailTime_day", "航行小时": "sailTime_day",
    
    # 船舶指标同义词 - 产出效率
    "载重吨天": "tonDayOutput", "吨天": "tonDayOutput",
    "载重吨船": "tonShipOutput", "吨船": "tonShipOutput",
    
    # 货类同义词
    "煤": "煤炭", "铁矿": "铁矿石", "石化": "化工品",
    "箱子": "集装箱", "散装": "散货",
    
    # 时间同义词
    "去年": "上年", "今年": "本年", "上个月": "上月",
    
    # 移除ALL映射 - 任何查询都必须有明确时间范围
    # "整体情况": "ALL", "全部情况": "ALL", "详细情况": "ALL", "画像": "ALL",
    # "全貌": "ALL", "完整信息": "ALL",  # 已删除
    "基本情况": "基本信息", "概况": "基本信息", "简介": "基本信息"
}

# 实体类型映射
ENTITY_TYPE_MAP = {
    # 比较/同比关键词
    "同比": "YoY", "环比": "MoM", "年同比": "YoY", "月环比": "MoM"
}

# 时间模式正则
PATTERNS = [
    r'(\d{4})年', r'(\d{1,2})月', r'(去年|今年)', r'(上个月|这个月)',
    r'近(\d+)年', r'近(\d+)个月', r'(\d{4})[-年](\d{1,2})月?'
]


def normalize(text: str) -> str:
    """标准化实体名称"""
    return SYNONYMS.get(text, text)


def get_type(text: str) -> str:
    """获取实体类型"""
    text = normalize(text)
    if text in PORTS:
        return "port"
    if text in PROVINCES:
        return "province"
    if text in SHIPS:
        return "ship"
    if text in BASINS:
        return "basin"
    if text in METRICS:
        return "metric"
    if text in CARGO_TYPES:
        return "cargo"
    return "unknown"


def extract_compare_type(text: str) -> str:
    """从文本中提取比较类型"""
    text = text.lower()
    for keyword, compare_type in ENTITY_TYPE_MAP.items():
        if keyword in text:
            return compare_type
    return "None"


def extract_cargo_code(entities: Dict[str, Any]) -> str:
    """从实体中提取并标准化货类代码"""
    # 简化映射，实际可建立完整的cargo_code表
    cargo_map = {
        # 干散货类
        "煤炭": "0100", "煤炭及制品": "0100",
        "金属矿石": "0300", "铁矿石": "0300",
        "钢铁": "0400", "矿物性建筑材料": "0500", "水泥": "0600",
        "木材": "0700", "非金属矿石": "0800", "磷矿": "0801",
        "化学肥料": "0900", "农药": "0900", "盐": "1000",
        "粮食": "1100", "机械": "1200", "设备": "1200", "电器": "1200",
        "有色金属": "1400", "轻工产品": "1500", "医药产品": "1500",
        "日用工业品": "1501", "农林牧渔业产品": "1600", "棉花": "1601",
        
        # 散装液货危险品
        "石油": "0200", "天然气": "0200", "原油": "0201",
        "化工原料": "1300", "化工制品": "1300",
        
        # 集装箱类
        "集装箱": "1900", "集装箱货运量": "1900", "集装箱运量": "1800",
        
        # 其他
        "滚装车辆": "2000", "滚装车辆货运量": "2100",
        
        # 通用分类
        "散货": "0100", "干散货": "0100", "液货": "0200", "危险品": "0200"
    }
    
    cargo_entities = entities.get("cargo", [])
    if not cargo_entities:
        return "None"  # 改为None而不是ALL
    
    cargo_name = normalize(cargo_entities[0])
    return cargo_map.get(cargo_name, "None")  # 改为None而不是ALL


# 实体词典集合（供jieba等工具使用）
ALL_ENTITIES = PORTS | PROVINCES | SHIPS | BASINS | METRICS | CARGO_TYPES


# ========== 缓存配置 ==========

# TTL配置（秒）
TTL_CONFIG = {
    # 实体基础数据TTL
    "entity": {
        "port": 21600,      # 6小时
        "ship": 21600,      # 6小时  
        "province": 86400,  # 24小时
        "yangtze": 86400,   # 24小时
    },
    
    # 船舶指标TTL
    "ship_metric": {
        # 核心业务指标 - 1小时
        "capacity_ton": 3600, "cargo_ton": 3600, "货运量": 3600, "周转量": 3600,
        "turnover_tonkm": 3600, "mileage_km": 3600, "里程": 3600,
        "voyages": 3600, "航次数": 3600, "运次": 3600,
        
        # 效率比率指标 - 30分钟  
        "opRatio": 1800, "营运率": 1800,
        "loadRatio": 1800, "负载率": 1800,
        "sailRatio": 1800, "航行率": 1800,
        "dispatchLoadRatio": 1800, "发船装载率": 1800,
        "distanceLoadRatio": 1800, "运距装载率": 1800,
        
        # 时间消耗指标 - 20分钟
        "anchorTime_day": 1200, "在港时间": 1200,
        "sailTime_day": 1200, "航行时间": 1200,
        
        # 产出效率指标 - 45分钟
        "tonDayOutput": 2700, "吨天产量": 2700,
        "tonShipOutput": 2700, "吨船产量": 2700,
    },
    
    # 港口指标TTL
    "port_metric": {
        "cargo_ton": 3600, "货运量": 3600,
        "ship_count": 3600, "船舶数": 3600,
        "avg_tonnage": 3600, "平均吨位": 3600,
        "total_tonnage": 3600, "总吨位": 3600,
    },
    
    # 意图特殊TTL
    "intent": {
        "6": 600,    # 异常检测：10分钟
        "7": 7200,   # 预测：2小时
    },
    
    # 派生缓存TTL
    "derived": {
        "port_stat": 3600,     # 港口统计：1小时
        "ship_stat": 3600,     # 船舶统计：1小时  
        "province_stat": 7200, # 省份统计：2小时
        "basin_stat": 7200,    # 流域统计：2小时
        "global_stat": 7200,   # 全局统计：2小时
    }
}

# 预加载配置
PRELOAD_CONFIG = {
    # 任务限制
    "limits": {
        "ship_tasks": 2000,
        "port_tasks": 1000,
    }
}

# 默认缓存配置
DEFAULT_CACHE_CONFIG = {
    "max_size": 500,
    "cleanup_interval": 300.0,  # 5分钟
    "derived_max_size": 5000,
}