<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上帝光效果 (最终优化版)</title>
    <style>
        body, html {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #f5f5f5;
        }
        canvas {
            display: block;
        }
    </style>
</head>
<body>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        // 1. 场景设置 (Scene Setup)
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.set(0, 0, 8); 

        const renderer = new THREE.WebGLRenderer({ antialias: true });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setPixelRatio(window.devicePixelRatio);
        
        renderer.setClearColor(0xf5f5f5, 1.0); 
        renderer.outputEncoding = THREE.sRGBEncoding; 
        renderer.toneMapping = THREE.ACESFilmicToneMapping; 
        // --- 【修改1】增加曝光度，让亮部更白 ---
        renderer.toneMappingExposure = 1.2; 
        renderer.physicallyCorrectLights = true;
        
        document.body.appendChild(renderer.domElement);

        // 2. 着色器代码 (Shader Code)
        const godRayVertexShader = `
            varying vec2 vUv;
            void main() {
                vUv = uv;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
        `;

        const godRayFragmentShader = `
            precision highp float;

            uniform float time;
            uniform float intensity;
            varying vec2 vUv;
            
            float hash(vec2 p) { return fract(sin(dot(p, vec2(127.1, 311.7))) * 43758.5453); }
            
            float noise(vec2 p) {
                vec2 i = floor(p);
                vec2 f = fract(p);
                float a = hash(i);
                float b = hash(i + vec2(1.0, 0.0));
                float c = hash(i + vec2(0.0, 1.0));
                float d = hash(i + vec2(1.0, 1.0));
                vec2 u = f * f * (3.0 - 2.0 * f);
                return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
            }
            
            void main() {
                vec2 center = vec2(0.1, 0.9);
                vec2 ray = normalize(vUv - center);
                float distance = length(vUv - center);
                float volumeStrength = 0.0;
                
                for(int i = 0; i < 8; i++) {
                    float t = float(i) / 8.0;
                    vec2 samplePos = vUv - ray * t * 2.0;
                    float density = noise(samplePos * 3.0 + time * 0.1);
                    density *= noise(samplePos * 8.0 + time * 0.2) * 0.5 + 0.5;
                    float falloff = 1.0 - smoothstep(0.0, 0.8, distance + t * 0.2);
                    falloff = pow(falloff, 1.5);
                    volumeStrength += density * falloff * (1.0 - t * 0.3);
                }
                
                volumeStrength /= 8.0;
                
                float angle = atan(ray.y, ray.x);
                // --- 【修改2】调整光束遮罩，提高暗部亮度，减少暗度 ---
                float rayMask = abs(sin(angle * 6.0 + time * 0.3)) * 0.5 + 0.5;
                float finalIntensity = volumeStrength * rayMask * intensity;
                // --- 【修改3】调整平滑步进，让强度过渡更平缓 ---
                finalIntensity = smoothstep(0.0, 1.0, finalIntensity);
                
                vec3 color = vec3(1.0, 1.0, 1.0) * finalIntensity;
                
                gl_FragColor = vec4(color, finalIntensity * 0.9);
            }
        `;

        // 3. 创建物体和材质 (Create Objects and Material)
        const godRayMaterial = new THREE.ShaderMaterial({
            vertexShader: godRayVertexShader,
            fragmentShader: godRayFragmentShader,
            uniforms: {
                time: { value: 0 },
                intensity: { value: 1.0 },
            },
            transparent: true,
            blending: THREE.NormalBlending, 
        });

        const planeGeometry = new THREE.PlaneGeometry(35, 25);
        const godRayPlane = new THREE.Mesh(planeGeometry, godRayMaterial);
        godRayPlane.position.set(0, 0, -5); 
        scene.add(godRayPlane);

        // 4. 添加灯光 (Add Lights)
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.2);
        scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.2);
        directionalLight.position.set(-3, 5, 2);
        scene.add(directionalLight);

        const pointLight = new THREE.PointLight(0xffffff, 0.9, 12);
        pointLight.position.set(-2, 3, 1);
        scene.add(pointLight);

        // 5. 动画循环 (Animation Loop)
        const clock = new THREE.Clock();
        
        function animate() {
            requestAnimationFrame(animate);
            
            godRayMaterial.uniforms.time.value = clock.getElapsedTime();
            
            renderer.render(scene, camera);
        }
        
        animate();

        // 6. 响应窗口大小变化 (Handle Window Resize)
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

    </script>
</body>
</html>
