# 长江航运智能指标问答Agent (CJHY-KGAgent)

🚢 基于LangGraph、Neo4j和Mem0构建的长江航运智能指标问答系统

## 📖 项目简介

CJHY-KGAgent是一个专门为长江航运领域设计的智能问答Agent，能够回答关于港口、航道、船舶等航运指标的专业问题。系统集成了知识图谱、智能记忆、实时数据API等多种技术，提供准确、及时的航运信息服务。

## ✨ 核心特性

- 🧠 **智能问答**: 基于LangGraph的多步骤推理，支持复杂航运问题解答
- 🕸️ **知识图谱**: Neo4j存储港口、航道、船舶等实体关系
- 🔍 **智能记忆**: Mem0提供对话历史存储和语义检索
- 📊 **实时数据**: 集成内部和外部API获取最新航运数据
- 🎯 **专业工具**: 内置航运指标查询、数据处理等专业工具
- 🌐 **多端支持**: 命令行、Web界面、API接口多种访问方式

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面层     │    │     API层       │    │    数据层       │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • Streamlit UI  │    │ • FastAPI       │    │ • Neo4j KG      │
│ • 命令行CLI     │◄───┤ • RESTful API   │◄───┤ • Mem0记忆库    │
│ • Jupyter NB    │    │ • 接口文档      │    │ • 内部数据API   │
└─────────────────┘    └─────────────────┘    │ • 外部实时API   │
                                              └─────────────────┘
           ▲                        ▲                    ▲
           │                        │                    │
┌─────────────────────────────────────────────────────────────────┐
│                     LangGraph Agent 核心                        │
├─────────────────┬─────────────────┬─────────────────┬───────────┤
│   问题理解      │   知识查询      │   数据处理      │  答案生成  │
│ • 实体提取      │ • Neo4j查询     │ • 数据整合      │ • 自然语言 │
│ • 意图识别      │ • API调用       │ • 统计计算      │ • 结果格式化│
│ • 上下文理解    │ • 记忆检索      │ • 单位转换      │ • 置信度评估│
└─────────────────┴─────────────────┴─────────────────┴───────────┘
```

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Neo4j 5.0+
- Docker (可选)

### 安装依赖

本项目使用 [uv](https://github.com/astral-sh/uv) 作为现代Python包管理工具。

```bash
# 安装 uv (如果还未安装)
# Windows (PowerShell)
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# 克隆项目
git clone <repository-url>
cd ch_agent

# 安装依赖 (uv会自动创建虚拟环境)
uv sync
```

### 配置环境

```bash
# 复制配置文件模板
cp config.env.example config.env

# 编辑配置文件，填入实际配置
nano config.env
```

主要配置项：

#### LLM配置 (至少配置一个)
```env
# OpenAI配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# Anthropic Claude配置  
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 阿里云通义千问Qwen配置 (推荐)
QWEN_API_KEY=your_qwen_api_key_here
QWEN_MODEL_NAME=qwen-turbo-3.0
```

#### 可选配置
```env
# Mem0智能记忆库
MEM0_API_KEY=your_mem0_api_key_here

# 外部API
AIS_API_KEY=your_ais_api_key_here
WEATHER_API_KEY=your_weather_api_key_here

# ETL配置
ETL_BATCH_SIZE=100
ETL_PARALLEL_JOBS=2
```

**注意**: Neo4j配置已在`src/config.py`中硬编码为本地默认值，如需修改请直接编辑该文件。

### 启动Neo4j

```bash
# 使用Docker启动Neo4j
docker run -d \
  --name neo4j \
  -p 7474:7474 -p 7687:7687 \
  -e NEO4J_AUTH=neo4j/password \
  neo4j:5.15
```

### 运行系统

```bash
# 命令行模式
uv run ch-agent
# 或者
uv run python main.py

# Web界面模式
uv run ch-agent-web
# 或者
uv run streamlit run streamlit_app.py

# API服务模式 (LangServe)
uv run ch-agent-api
# 或者
uv run python run_api.py
```

## 📚 使用指南

### 命令行使用

```bash
# 交互式问答
uv run ch-agent

# 单次问答
uv run ch-agent --mode single --question "九江港上个月的集装箱吞吐量是多少？"

# 调试模式
uv run ch-agent --debug
```

### Web界面使用

1. 启动Streamlit应用：`uv run ch-agent-web`
2. 打开浏览器访问：`http://localhost:8501`
3. 在聊天界面输入问题，获得智能回答

### API接口使用

基于LangServe提供RESTful API接口，支持同步问答、流式输出和批量处理。

```bash
# 启动API服务
uv run ch-agent-api

# 同步问答
curl -X POST "http://localhost:8000/agent/invoke" \
  -H "Content-Type: application/json" \
  -d '{
    "input": {
      "question": "九江港上个月的集装箱吞吐量是多少？",
      "session_id": "my-session"
    }
  }'

# 流式问答
curl -X POST "http://localhost:8000/agent/stream" \
  -H "Content-Type: application/json" \
  -d '{
    "input": {
      "question": "武汉港的发展历史",
      "session_id": "stream-session"
    }
  }'

# 健康检查
curl http://localhost:8000/health

# 查看API文档
open http://localhost:8000/docs

# 访问演示页面
open http://localhost:8000/
```

详细的API使用指南请参考：[LangServe API 使用指南](doc/langserve_api_guide.md)

### 数据同步

```bash
# 手动同步所有数据
uv run ch-agent-sync --mode manual --type all

# 启动定时同步
uv run ch-agent-sync --mode schedule
```

## 🛠️ 开发指南

### 项目结构

```
ch_agent/
├── src/                   # 核心源代码包
│   ├── __init__.py       # 包初始化
│   ├── config.py         # 配置管理
│   ├── utils.py          # 工具函数
│   ├── agent/            # 智能问答Agent模块
│   │   ├── __init__.py  # Agent包初始化
│   │   ├── agent.py     # LangGraph Agent核心
│   │   ├── tools.py     # LangChain工具集
│   │   └── memory.py    # Mem0智能记忆
│   └── kg_etl/           # 知识图谱和数据ETL模块
│       ├── __init__.py  # ETL包初始化
│       ├── database.py  # Neo4j数据库客户端
│       └── api_client.py # API客户端
├── main.py               # 命令行入口
├── streamlit_app.py      # Web界面
├── fastapi_app.py        # API服务
├── data_sync.py          # 数据同步
├── tests.py              # 测试模块
├── pyproject.toml        # 项目配置和依赖
├── uv.lock               # 依赖锁定文件
├── UV_USAGE.md           # UV使用说明
└── doc/                  # 项目文档
```

### 添加新功能

1. **新增工具**: 在`tools.py`中继承`BaseTool`类
2. **新增API**: 在`api_client.py`中添加新的API调用方法
3. **修改Agent流程**: 在`agent.py`中调整LangGraph工作流
4. **添加测试**: 在`tests.py`中添加对应的测试用例

### 代码规范

- 使用Python类型注解
- 遵循PEP 8代码风格
- 添加详细的文档字符串
- 使用loguru进行日志记录

### 测试

```bash
# 运行所有测试
uv run python tests.py

# 安装开发依赖
uv sync --extra dev

# 使用pytest运行测试
uv run pytest tests.py -v

# 代码格式化和检查
uv run black src/
uv run isort src/
uv run flake8 src/
```

## 📋 支持的问题类型

### 港口指标查询
- "九江港上个月的集装箱吞吐量是多少？"
- "武汉港今年的货物吞吐量增长情况如何？"
- "长江沿线主要港口的排名情况？"

### 航道信息查询
- "长江中游航道的通航水深情况如何？"
- "目前哪些航段有通航限制？"
- "枯水期对航道通航能力的影响？"

### 船舶信息查询
- "目前在锚地等待的船舶有哪些？"
- "长江干线船舶通行量统计？"
- "特定船舶的航行轨迹信息？"

### 指标定义查询
- "什么是船舶实际载重系数？"
- "港口吞吐量的计算方法是什么？"
- "航道维护深度的标准是多少？"

## 🔧 配置说明

### Neo4j配置
```env
NEO4J_URI=bolt://localhost:7687      # Neo4j连接地址
NEO4J_USERNAME=neo4j                 # 用户名
NEO4J_PASSWORD=password              # 密码
NEO4J_DATABASE=neo4j                 # 数据库名
```

### LLM配置
```env
OPENAI_API_KEY=sk-...                # OpenAI API密钥
ANTHROPIC_API_KEY=sk-ant-...         # Anthropic API密钥
OPENAI_BASE_URL=https://api.openai.com/v1  # OpenAI API地址
```

### 应用配置
```env
APP_ENV=development                  # 应用环境
LOG_LEVEL=INFO                       # 日志级别
STREAMLIT_SERVER_PORT=8501           # Streamlit端口
FASTAPI_HOST=0.0.0.0                # FastAPI主机
FASTAPI_PORT=8000                    # FastAPI端口
```

## 🐛 常见问题

### Q: Neo4j连接失败
A: 检查Neo4j服务是否启动，确认连接地址和认证信息正确

### Q: API调用超时
A: 检查网络连接，调整API超时时间配置

### Q: 内存使用过高
A: 调整批处理大小，启用数据分页加载

### Q: 答案不准确
A: 检查知识图谱数据完整性，更新训练数据

## 📈 性能优化

- 使用连接池管理数据库连接
- 启用查询结果缓存
- 优化Cypher查询语句
- 使用异步处理提升并发性能

## 🔒 安全考虑

- 使用环境变量管理敏感信息
- 限制API访问频率
- 验证输入参数防止注入攻击
- 定期更新依赖包版本

## 🤝 贡献指南

1. Fork项目
2. 创建特性分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 提交Pull Request

## 📄 许可证

本项目采用MIT许可证，详见[LICENSE](LICENSE)文件。

## 📞 联系我们

- 项目主页：[GitHub Repository]
- 问题反馈：[GitHub Issues]
- 技术交流：[联系邮箱]

---

**CJHY-KGAgent** - 让航运数据更智能 🚢✨ 