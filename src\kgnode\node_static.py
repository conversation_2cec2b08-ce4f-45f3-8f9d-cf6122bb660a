﻿"""
静态节点数据管理模块
用于创建和管理Neo4j数据库中的静态节点数据，包括时间节点、货物类型、船舶类型、港口和省份
"""

import pandas as pd
from loguru import logger
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import os
from .database import DatabaseManager


class StaticNodeManager:
    """静态节点管理器"""

    def __init__(self, database_manager: DatabaseManager):
        """
        初始化静态节点管理器

        Args:
            database_manager: 数据库管理器实例
        """
        self.db_manager = database_manager
        logger.info("StaticNodeManager initialized")

    def close(self):
        """关闭数据库连接"""
        # 数据库管理器会处理连接关闭
        logger.info("StaticNodeManager closed")
    
    def create_standard_year_months(self, year: int = 2024) -> Dict[str, int]:
        """
        创建标准的年份和月份节点（仅2024年）

        Args:
            year: 年份，默认2024

        Returns:
            Dict: 创建的节点统计信息
        """
        with self.db_manager.neo4j.get_session() as session:
            # 创建年份节点
            session.run(
                "MERGE (y:Year {year: $year, name: $name})",
                year=year, name=str(year)
            )
            
            # 创建月份节点并建立关系
            months = ['01', '02', '03', '04', '05', '06', 
                     '07', '08', '09', '10', '11', '12']
            
            for month in months:
                month_name = f"{year}-{month}"
                session.run("""
                    MERGE (m:YearMonth {year: $year, month: $month, name: $name})
                    WITH m
                    MATCH (y:Year {year: $year})
                    MERGE (m)-[:BELONGS_TO_YEAR]->(y)
                """, year=year, month=month, name=month_name)
            
            logger.info(f"Created year {year} and 12 months")
            return {"years": 1, "months": 12}
    
    def load_cargo_types_from_csv(self) -> Dict[str, int]:
        """
        从CSV文件加载货物类型数据
        
        Returns:
            Dict: 加载的节点统计信息
        """
        csv_path = "resource/csv/图谱-货类.csv"
        
        if not os.path.exists(csv_path):
            logger.warning(f"货物类型CSV文件不存在: {csv_path}")
            return {"cargo_categories": 0, "cargo_types": 0}
        
        # 读取CSV文件
        df = pd.read_csv(csv_path, encoding='utf-8', dtype=str)
        logger.info(f"从CSV加载了{len(df)}条货物类型记录")
        
        categories_count = 0
        subcategories_count = 0
        
        with self.db_manager.neo4j.get_session() as session:
            # 创建货物大类节点（去重）- 按照设计文档只有name属性
            categories = df[['TYPE_NAME']].drop_duplicates()
            for _, row in categories.iterrows():
                session.run("""
                    MERGE (c:CargoCategory {
                        name: $name
                    })
                """, name=row['TYPE_NAME'])
                categories_count += 1
                logger.debug(f"创建货物大类: {row['TYPE_NAME']}")
            
            # 创建货物类别节点并建立关系 - 按照设计文档应为CargoType
            for _, row in df.iterrows():
                session.run("""
                    MERGE (ct:CargoType {
                        subCode: $sub_code,
                        subName: $sub_name
                    })
                    WITH ct
                    MATCH (cc:CargoCategory {name: $category_name})
                    MERGE (ct)-[:IS_CATEGORY_OF]->(cc)
                """, 
                sub_code=str(row['TYPE_SUB_CODE']),
                sub_name=row['TYPE_SUB_NAME'],
                category_name=row['TYPE_NAME'])
                subcategories_count += 1
                logger.debug(f"创建货物类别: {row['TYPE_SUB_NAME']} ({row['TYPE_SUB_CODE']})")
        
        logger.info(f"创建了{categories_count}个货物大类和{subcategories_count}个货物类别")
        return {"cargo_categories": categories_count, "cargo_types": subcategories_count}
    
    def load_ship_types_from_csv(self) -> Dict[str, int]:
        """
        从CSV文件加载船舶类型数据
        根据新的设计文档：
        - ShipCategory (船舶大类): 只有name属性，来自TYPE_NAME
        - ShipType (船舶类型): 详细属性，来自TYPE_SUB_NAME等
        - 关系: (ShipType)-[:BELONGS_TO_CATEGORY]->(ShipCategory)
        
        Returns:
            Dict: 加载的节点统计信息
        """
        csv_path = "resource/csv/图谱-船类.csv"
        
        if not os.path.exists(csv_path):
            logger.warning(f"船舶类型CSV文件不存在: {csv_path}")
            return {"ship_categories": 0, "ship_types": 0}
        
        # 读取CSV文件
        df = pd.read_csv(csv_path, encoding='utf-8', dtype=str)
        print(df)
        logger.info(f"从CSV加载了{len(df)}条船舶类型记录")
        categories_count = 0
        types_count = 0
        
        with self.db_manager.neo4j.get_session() as session:
            # 创建船舶大类节点（去重）- 按照设计文档只有name属性
            categories = df[['TYPE_NAME']].drop_duplicates()
            for _, row in categories.iterrows():
                session.run("""
                    MERGE (sc:ShipCategory {
                        name: $name
                    })
                """, name=row['TYPE_NAME'])
                categories_count += 1
                logger.debug(f"创建船舶大类: {row['TYPE_NAME']}")
            
            # 创建船舶类型节点并建立关系 - 按照设计文档的ShipType属性
            for _, row in df.iterrows():
                session.run("""
                    MERGE (st:ShipType {
                        subCode: $sub_code,
                        subName: $sub_name,
                        code: $code,
                        name: $name
                    })
                    WITH st
                    MATCH (sc:ShipCategory {name: $category_name})
                    MERGE (st)-[:BELONGS_TO_CATEGORY]->(sc)
                """, 
                sub_code=str(row['TYPE_SUB_CODE']),
                sub_name=row['TYPE_SUB_NAME'],
                code=str(row['TYPE_CODE']),
                name=row['TYPE_NAME'],
                category_name=row['TYPE_NAME'])
                types_count += 1
                logger.debug(f"创建船舶类型: {row['TYPE_SUB_NAME']} ({row['TYPE_SUB_CODE']})")
        
        logger.info(f"创建了{categories_count}个船舶大类和{types_count}个船舶类型")
        return {"ship_categories": categories_count, "ship_types": types_count}
    
    def load_provinces_from_csv(self) -> Dict[str, int]:
        """
        从CSV文件加载省份数据
        
        Returns:
            Dict: 加载的节点统计信息
        """
        csv_path = "resource/csv/图谱-省份.csv"
        
        if not os.path.exists(csv_path):
            logger.warning(f"省份CSV文件不存在: {csv_path}")
            return {"provinces": 0}
        
        # 读取CSV文件
        df = pd.read_csv(csv_path, encoding='utf-8', dtype=str)
        logger.info(f"从CSV加载了{len(df)}条省份记录")
        
        provinces_count = 0
        
        with self.db_manager.neo4j.get_session() as session:
            for _, row in df.iterrows():
                session.run("""
                    MERGE (p:Province {
                        name: $name,
                        code: $code
                    })
                """, 
                name=row['name'], 
                code=str(row['code']))
                provinces_count += 1
                logger.debug(f"创建省份: {row['name']} ({row['code']})")
        
        logger.info(f"创建了{provinces_count}个省份节点")
        return {"provinces": provinces_count}
    
    
    def create_basin_nodes(self) -> Dict[str, int]:
        """
        创建流域节点并建立省份与流域的关系
        根据设计文档，创建"长江"流域节点，并让所有省份都属于长江流域
        
        Returns:
            Dict: 创建的节点统计信息
        """
        basins_count = 0
        
        with self.db_manager.neo4j.get_session() as session:
            # 创建长江流域节点
            session.run("""
                MERGE (b:Basin {name: $name})
            """, name="长江")
            basins_count += 1
            logger.debug("创建流域节点: 长江")
            
            # 建立所有省份与长江流域的关系
            result = session.run("""
                MATCH (p:Province)
                MATCH (b:Basin {name: "长江"})
                MERGE (p)-[:PART_OF_BASIN]->(b)
                RETURN count(p) as province_count
            """)
            province_count = result.single()['province_count']
            logger.info(f"建立了{province_count}个省份与长江流域的关系")
        
        logger.info(f"创建了{basins_count}个流域节点")
        return {"basins": basins_count}
    
    def load_ports_from_csv(self) -> Dict[str, int]:
        """
        从CSV文件加载港口数据
        
        Returns:
            Dict: 加载的节点统计信息
        """
        csv_path = "resource/csv/图谱-港口.csv"
        
        if not os.path.exists(csv_path):
            logger.warning(f"港口CSV文件不存在: {csv_path}")
            return {"ports": 0}
        
        # 读取CSV文件
        df = pd.read_csv(csv_path, encoding='utf-8', dtype=str)
        logger.info(f"从CSV加载了{len(df)}条港口记录")
        
        ports_count = 0
        
        with self.db_manager.neo4j.get_session() as session:
            for _, row in df.iterrows():
                # 创建港口节点 - 按照设计文档添加prov属性
                session.run("""
                    MERGE (port:Port {name: $name})
                    SET port.sortNo = $sort_no,
                        port.prov = $prov
                """,
                name=row['name'],
                sort_no=int(row['sortNo']) if pd.notna(row['sortNo']) else None,
                prov=row['prov'])
                
                # 建立与省份的关系 - 按照设计文档使用LOCATED_IN_PROVINCE
                session.run("""
                    MATCH (port:Port {name: $port_name})
                    MATCH (province:Province {name: $province_name})
                    MERGE (port)-[:LOCATED_IN_PROVINCE]->(province)
                """, 
                port_name=row['name'],
                province_name=row['prov'])
                
                ports_count += 1
                logger.debug(f"创建港口: {row['name']} 属于 {row['prov']}")
        
        logger.info(f"创建了{ports_count}个港口节点")
        return {"ports": ports_count}
    
    def create_all_static_nodes(self) -> Dict[str, Dict[str, int]]:
        """
        创建所有静态节点
        
        Returns:
            Dict: 所有创建的节点统计信息
        """
        results = {}
        
        # 创建时间节点
        results['time'] = self.create_standard_year_months()
        results['time'] = self.create_standard_year_months(2025)
        
        # 加载货物类型
        results['cargo'] = self.load_cargo_types_from_csv()
        
        # 加载船舶类型
        results['ship_type'] = self.load_ship_types_from_csv()
        
        # 加载省份
        results['province'] = self.load_provinces_from_csv()
        
        # 加载港口
        results['port'] = self.load_ports_from_csv()
        
        # 创建流域节点（需要在省份创建之后）
        results['basin'] = self.create_basin_nodes()
        
        logger.info("所有静态节点创建完成")
        return results
    
    def get_statistics(self) -> Dict[str, int]:
        """
        获取数据库中各类节点的统计信息
        
        Returns:
            Dict: 节点统计信息
        """
        with self.db_manager.neo4j.get_session() as session:
            # 统计各类节点数量
            stats = {}
            
            # 年份节点
            result = session.run("MATCH (n:Year) RETURN count(n) as count")
            stats['years'] = result.single()['count']
            
            # 月份节点
            result = session.run("MATCH (n:YearMonth) RETURN count(n) as count")
            stats['months'] = result.single()['count']
            
            # 货物大类
            result = session.run("MATCH (n:CargoCategory) RETURN count(n) as count")
            stats['cargo_categories'] = result.single()['count']
            
            # 货物类别
            result = session.run("MATCH (n:CargoType) RETURN count(n) as count")
            stats['cargo_types'] = result.single()['count']
            
            # 船舶大类
            result = session.run("MATCH (n:ShipCategory) RETURN count(n) as count")
            stats['ship_categories'] = result.single()['count']
            
            # 船舶类型
            result = session.run("MATCH (n:ShipType) RETURN count(n) as count")
            stats['ship_types'] = result.single()['count']
            
            # 省份
            result = session.run("MATCH (n:Province) RETURN count(n) as count")
            stats['provinces'] = result.single()['count']
            
            # 港口
            result = session.run("MATCH (n:Port) RETURN count(n) as count")
            stats['ports'] = result.single()['count']
            
            # 流域
            result = session.run("MATCH (n:Basin) RETURN count(n) as count")
            stats['basins'] = result.single()['count']
            
            # 船舶
            result = session.run("MATCH (n:Ship) RETURN count(n) as count")
            stats['ships'] = result.single()['count']
            
            # 总节点数
            result = session.run("MATCH (n) RETURN count(n) as count")
            stats['total_nodes'] = result.single()['count']
            
            # 总关系数
            result = session.run("MATCH ()-[r]-() RETURN count(r) as count")
            stats['total_relationships'] = result.single()['count']
            
            return stats
    
    def get_ports_summary(self) -> List[Dict]:
        """
        获取港口摘要信息
        
        Returns:
            List[Dict]: 港口信息列表
        """
        with self.db_manager.neo4j.get_session() as session:
            result = session.run("""
                MATCH (port:Port)
                OPTIONAL MATCH (port)-[:LOCATED_IN_PROVINCE]->(province:Province)
                RETURN port.name as port_name, 
                       port.code as port_code,
                       province.name as province_name
                ORDER BY port.name
            """)
            
            ports = []
            for record in result:
                ports.append({
                    'port_name': record['port_name'],
                    'port_code': record['port_code'],
                    'province_name': record['province_name']
                })
            
            return ports
    
    def get_ships_summary(self) -> List[Dict]:
        """
        获取船舶摘要信息
        
        Returns:
            List[Dict]: 船舶摘要信息列表
        """
        with self.db_manager.neo4j.get_session() as session:
            result = session.run("""
                MATCH (ship:Ship)
                OPTIONAL MATCH (ship)-[:IS_TYPE]->(shipType:ShipType)
                OPTIONAL MATCH (shipType)-[:BELONGS_TO_CATEGORY]->(shipCategory:ShipCategory)
                RETURN ship.mmsi as mmsi,
                       ship.shipId as ship_id,
                       ship.name as ship_name, 
                       ship.regPortName as reg_port,
                       ship.dwt as dwt,
                       ship.grossTon as gross_ton,
                       ship.builtDate as built_date,
                       shipType.subName as ship_type,
                       shipCategory.name as ship_category
                ORDER BY ship.name
            """)
            
            summary = []
            for record in result:
                summary.append({
                    'mmsi': record['mmsi'],
                    'ship_id': record['ship_id'],
                    'ship_name': record['ship_name'],
                    'reg_port': record['reg_port'],
                    'dwt': record['dwt'],
                    'gross_ton': record['gross_ton'],
                    'built_date': str(record['built_date']) if record['built_date'] else None,
                    'ship_type': record['ship_type'],
                    'ship_category': record['ship_category']
                })
            
            logger.info(f"获取了{len(summary)}条船舶摘要信息")
            return summary

def create_port_node(session, name: str, code: str = ""):
    """创建港口节点"""
    session.run("""
        MERGE (p:Port {name: $name, code: $code})
    """, name=name, code=code)


def create_province_node(session, name: str, code: str = ""):
    """创建省份节点"""
    session.run("""
        MERGE (p:Province {name: $name, code: $code})
    """, name=name, code=code)


if __name__ == "__main__":
    # 测试代码
    from src.config import NEO4J_CONFIG
    
    manager = StaticNodeManager(
        uri=NEO4J_CONFIG['uri'],
        username=NEO4J_CONFIG['username'],
        password=NEO4J_CONFIG['password']
    )
    
    try:
        # 创建所有静态节点
        results = manager.create_all_static_nodes()
        print("Creation results:", results)
        
        # 获取统计信息
        stats = manager.get_statistics()
        print("Database statistics:", stats)
        
        # 获取港口摘要
        ports_summary = manager.get_ports_summary()
        print("Ports by province:", ports_summary)
        
        # 获取船舶摘要
        ships_summary = manager.get_ships_summary()
        print("Ships summary:", ships_summary)
        
    finally:
        manager.close()
